(function () {
    'use strict';
    angular.module('app').controller("BinAuditBins", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog,$window) {
        $scope.busy = false;
        $scope.RemovalCodeList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];


        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'speed/includes/pendingbinaudit.submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinAuditBinsList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBinAuditBinsList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic    


        $scope.RecordUserNavigationTransaction = function (TransactionType,Description,PageURL,id) {

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserNavigationTransaction&TransactionType=' + TransactionType + '&Description=' + Description + '&PageURL=' + PageURL+id,
                success: function (data) {
                    
                    if (data.Success) {                        
                    } else {                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {                    
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };


        $scope.ProcessBinAudit = function (pallet,ev) {   
            if($scope.filter_text[0].BinName != '' && $scope.filter_text[0].BinName) {

                if($scope.filter_text[0].BinName == pallet.BinName) {
                    $scope.RecordUserNavigationTransaction('Audit ---> Pending BIn Audit','Hit on Process Button','speed/#!/BinAudit/',pallet.ID);
                    window.location = "#!/BinAudit/"+pallet.ID;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched BIN is different from Processed BIN')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("filter_Bin").focus();    
                }
            }  else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter BIN Name in BIN Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("filter_Bin").focus();
            }
        };

    });


    angular.module('app').controller("BinAudit", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {
        $scope.audit_details = {};
        $scope.asset = {'AuditNotes': 'n/a','Damaged': 'No Damage'};

        //Start Get Current Time Function
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        //End Get Current Time Function

        if ($stateParams.ID) {
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBinAuditBinsDetails&ID='+$stateParams.ID,
                success: function(data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.audit_details = {'ControlID': data.Result.ControlID,'BinAuditID': data.Result.ID,'SourceBinID': data.Result.CustomPalletID,'SourceBinName': data.Result.BinName,'SourceBinCount': data.Result.AssetsCount,'SourceDisposition': data.Result.disposition_id,'SourceDispositionName': data.Result.disposition,'SourceDispositionColor': data.Result.color_code,'Accuracy': data.Result.Accuracy,'AccuracyTarget': data.Result.AccuracyTarget};
                        $scope.GetCurrentTime($scope.audit_details,'origin_bin_scan_time');
                        setTimeout(function () {
                            $window.document.getElementById('SameDispositionBinName').focus();
                        }, 100);
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.ValidateBinAuditSameDispositionBin = function () {
            if($scope.audit_details.SourceBinID > 0 && $scope.audit_details.SourceDisposition > 0) {
                if($scope.audit_details.SameDispositionBinName != '') {

                    $rootScope.$broadcast('preloader:active');

                    jQuery.ajax({
                        url: host+'speed/includes/pendingbinaudit.submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateBinAuditSameDispositionBin&SameDispositionBinName='+$scope.audit_details.SameDispositionBinName+'&SourceDisposition='+$scope.audit_details.SourceDisposition,
                        success: function(data) {                    
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.audit_details.SameDispositionBinName = data.Result.BinName;
                                $scope.audit_details.SameDispositionBinID = data.Result.CustomPalletID;
                                $scope.audit_details.SameDispositionBinCount = data.Result.AssetsCount;
                                $scope.audit_details.SameDispositionDisposition = data.Result.disposition_id;
                                
                                $window.document.getElementById('AllDispositionBinName').focus();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.error = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {                    
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Output Bin in empty')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }                
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Source BIN details are invalid')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        $scope.ValidateBinAuditAllDispositionBin = function () {
            if($scope.audit_details.SourceBinID > 0) {
                if($scope.audit_details.AllDispositionBinName != '') {

                    $rootScope.$broadcast('preloader:active');

                    jQuery.ajax({
                        url: host+'speed/includes/pendingbinaudit.submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateBinAuditAllDispositionBin&AllDispositionBinName='+$scope.audit_details.AllDispositionBinName,
                        success: function(data) {                    
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.audit_details.AllDispositionBinName = data.Result.BinName;
                                $scope.audit_details.AllDispositionBinID = data.Result.CustomPalletID;
                                $scope.audit_details.AllDispositionBinCount = data.Result.AssetsCount;
                                $scope.audit_details.AllDispositionDisposition = data.Result.disposition_id;        
                                                                
                                setTimeout(function () {
                                    $window.document.getElementById('SerialNumber').focus();
                                }, 100);
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.error = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {                    
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Output Bin in empty')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }                
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Source BIN details are invalid')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.SourceBinChanged = function (SourceBinName) {
            $scope.audit_details = {'SourceBinName': SourceBinName};
            // Always get fresh timestamp when source bin changes
            $scope.GetCurrentTime($scope.audit_details,'origin_bin_scan_time');
        };

        $scope.SameDispositionBinChanged = function () {            
            $scope.audit_details.SameDispositionBinID = '';
            $scope.audit_details.SameDispositionBinCount = '';
            $scope.audit_details.SameDispositionDisposition = '';
        };

        $scope.AllDispositionBinChanged = function () {        
            $scope.audit_details.AllDispositionBinID = '';
            $scope.audit_details.AllDispositionBinCount = '';
            $scope.audit_details.AllDispositionDisposition = '';
        };



        $scope.ValidateBinAuditSourceBin = function () {            
            if($scope.audit_details.SourceBinName != '') {

                $rootScope.$broadcast('preloader:active');

                jQuery.ajax({
                    url: host+'speed/includes/pendingbinaudit.submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ValidateBinAuditSourceBin&SourceBinName='+$scope.audit_details.SourceBinName,
                    success: function(data) {                    
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.audit_details = {'ControlID': data.Result.ControlID,'BinAuditID': data.Result.ID,'SourceBinID': data.Result.CustomPalletID,'SourceBinName': data.Result.BinName,'SourceBinCount': data.Result.AssetsCount,'SourceDisposition': data.Result.disposition_id,'SourceDispositionName': data.Result.disposition,'SourceDispositionColor': data.Result.color_code,'Accuracy': data.Result.Accuracy,'AccuracyTarget': data.Result.AccuracyTarget};

                            // Always get fresh timestamp when source bin is validated
                            $scope.GetCurrentTime($scope.audit_details,'origin_bin_scan_time');

                            setTimeout(function () {
                                $window.document.getElementById('SameDispositionBinName').focus();
                            }, 100);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {                    
                $mdToast.show(
                    $mdToast.simple()
                        .content('Source Bin in empty')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }                
            
        };

        $scope.ValidateBinAuditSerial = function () {

            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateBinAuditSerial&SerialNumber='+$scope.asset.SerialNumber+'&'+$.param($scope.audit_details),
                success: function(data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {                        
                        $window.document.getElementById('MPN').focus();
                        $scope.asset.AssetScanID = data.Result.AssetScanID;
                        $scope.asset.CurrentBinID = data.Result.CustomPalletID;
                        if(data.BinMismatch) {
                            //$scope.asset.EvaluationResult = 'Fail-SerialBinMismatch';
                            $scope.asset.SerialVerified = false;   
                            $scope.asset.SerialInBinStatus = 'Not In Bin'
                            $scope.asset.valid_serial_flag = 0;
                        } else {
                            //$scope.asset.EvaluationResult = 'Pass-Audit';    
                            $scope.asset.SerialVerified = true;          
                            $scope.asset.SerialInBinStatus = 'In Bin'  
                            $scope.asset.valid_serial_flag = 1;                
                        }

                        if(data.DispositionMismatch) {
                            // $scope.asset.NewBinName = $scope.audit_details.AllDispositionBinName;
                            // $scope.asset.NewBinID = $scope.audit_details.AllDispositionBinID;
                            //$scope.asset.NewDispositionID = data.Result.disposition_id;
                            //$scope.asset.NewDispositionName = data.Result.disposition;

                            $scope.asset.DispositionType = 'Do not match';
                        } else {
                            // $scope.asset.NewBinName = $scope.audit_details.SameDispositionBinName;
                            // $scope.asset.NewBinID = $scope.audit_details.SameDispositionBinID;
                            // $scope.asset.NewDispositionID = $scope.audit_details.SameDispositionDisposition;
                            // $scope.asset.NewDispositionName =  $scope.audit_details.SourceDispositionName;

                            $scope.asset.DispositionType = 'Match';
                        }

                        if(data.SerialNumberStatus) {
                            $scope.asset.SerialNumberStatus = data.SerialNumberStatus;
                            if(data.SerialNumberStatus != 'Active') {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Serial Status is not Active')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                        }

                        $scope.asset.CurrentDisposition = data.Result.disposition;
                        $scope.asset.CurrentDispositionColor = data.Result.color_code;

                    } else {
                        $scope.asset.SerialVerified = false;
                        $scope.asset.valid_serial_flag = 0;
                        $scope.asset.AssetScanID = '';
                        $scope.asset.EvaluationResult = '';
                        $scope.asset.CurrentDisposition = '';
                        $scope.asset.CurrentDispositionColor = '';
                        $scope.asset.NewBinName = '';
                        $scope.asset.NewBinID = '';
                        $scope.asset.CurrentBinID = '';
                        $scope.asset.NewDispositionID = '';
                        $scope.asset.NewDispositionName = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };


        $scope.ValidateBinAuditMPN = function () {

            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateBinAuditMPN&'+$.param($scope.asset),
                success: function(data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {                        
                        if(data.MPN) {
                            $scope.asset.MPN = data.MPN;                            
                            $window.document.getElementById('SaveButton').focus();
                        }
                        if(data.MPNMismatch) {
                            if($scope.asset.EvaluationResult == 'Fail-SerialBinMismatch') {
                                //$scope.asset.EvaluationResult = 'Fail-SerialMPNMismatch';
                            } else {
                                //$scope.asset.EvaluationResult = 'Fail-MPNMismatch';
                            }                            
                            $scope.asset.MPNVerified = false;   
                            $scope.asset.MPNStatus = 'Does not Matches';
                            $scope.asset.valid_mpn_flag = 0;
                            $scope.GetEvaluationResult();
                        } else {                            
                            $scope.asset.MPNVerified = true;
                            $scope.asset.MPNStatus = 'Matches';
                            $scope.asset.valid_mpn_flag = 1;
                            $scope.GetEvaluationResult();
                        }
                    } else {
                        $scope.asset.MPNVerified = false;
                        $scope.asset.valid_mpn_flag = 0;
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.GetEvaluationResult = function () {
            $scope.asset.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetEvaluationResult&'+$.param($scope.asset),
                success: function(data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    $scope.asset.busy = false;
                    if(data.Success) {
                        // $mdToast.show(
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-danger md-block')
                        // );
                        if(data.Result.EvaluationResult) {
                            $scope.asset.EvaluationResult = data.Result.EvaluationResult;
                            $scope.GetCurrentTime($scope.asset,'EvaluationResult_scan_time');
                        }

                        if(data.Result.DestinationBinType == 'Same') {
                            $scope.asset.NewBinName = $scope.audit_details.SameDispositionBinName;
                            $scope.asset.NewBinID = $scope.audit_details.SameDispositionBinID;
                            $scope.asset.NewDispositionID = $scope.audit_details.SameDispositionDisposition;
                            $scope.asset.NewDispositionName =  $scope.audit_details.SourceDispositionName;                    
                            $scope.GetCurrentTime($scope.asset,'assigned_bin_scan_time');
                        } else {
                            $scope.asset.NewBinName = $scope.audit_details.AllDispositionBinName;
                            $scope.asset.NewBinID = $scope.audit_details.AllDispositionBinID;
                            $scope.asset.NewDispositionID = data.Result.AssetDispositionID;
                            $scope.asset.NewDispositionName = data.Result.AssetDisposition;
                            $scope.GetCurrentTime($scope.asset,'assigned_bin_scan_time');
                        }

                    } else {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.asset.EvaluationResult = '';


                        $scope.asset.NewBinName = '';
                        $scope.asset.NewBinID = '';
                        $scope.asset.NewDispositionID = '';
                        $scope.asset.NewDispositionName = '';
                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.SerialChanged = function (Serial) {
            $scope.asset = {'SerialNumber' : Serial,'AuditNotes': 'n/a','Damaged': 'No Damage'};
            // Get fresh timestamp when audit notes are reset to 'n/a' (this is a change in audit notes)
            $scope.GetCurrentTime($scope.asset,'audit_notes_scan_time');
        };

        $scope.RecentRecord = {};
        $scope.AuditBinSerial = function () {
            $scope.asset.busy = true;

            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'speed/includes/pendingbinaudit.submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=AuditBinSerial&'+$.param($scope.asset)+'&'+$.param($scope.audit_details),
                success: function(data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    $scope.asset.busy = false;
                    if(data.Success) {        
                        if(data.SourceBinCount) {
                            $scope.audit_details.SourceBinCount = data.SourceBinCount;                            
                        }
                        if(data.SameDispositionBinCount) {
                            $scope.audit_details.SameDispositionBinCount = data.SameDispositionBinCount;                            
                        }
                        if(data.AllDispositionBinCount) {
                            $scope.audit_details.AllDispositionBinCount = data.AllDispositionBinCount;                            
                        }
                        
                        if(data.Accuracy) {
                            $scope.audit_details.Accuracy = data.Accuracy;                            
                        }
                        if(data.AccuracyTarget) {
                            $scope.audit_details.AccuracyTarget = data.AccuracyTarget;                            
                        }

                        

                        $scope.RecentRecord = {'SourceBinName': $scope.audit_details.SourceBinName,'SerialNumber': $scope.asset.SerialNumber,'MPN': $scope.asset.MPN,'NewDispositionName': $scope.asset.NewDispositionName,'NewBinName': $scope.asset.NewBinName,'AuditNotes': $scope.asset.AuditNotes};
                        if(data.AuditRecordID){
                            $scope.RecentRecord.AuditRecordID = data.AuditRecordID;
                            if($scope.asset.AuditNotes != 'n/a') {
                                window.open('../label/master/examples/binauditlabel.php?id='+data.AuditRecordID, '_blank');
                            }
                        }
                        $scope.asset = {'AuditNotes': 'n/a','Damaged': 'No Damage'};
                        // Get fresh timestamp when audit notes are reset to 'n/a' for next serial
                        $scope.GetCurrentTime($scope.asset,'audit_notes_scan_time');
                        $window.document.getElementById('SerialNumber').focus();

                        if(data.Accuracy >= data.AccuracyTarget) {
                            $scope.CompleteAudit();
                        }

                    } else {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };


        $scope.MoveSerialsToOutputBin = function(ev) {

            var confirm = $mdDialog.confirm()
            .title('Are you sure, there are no parts in source bin?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Yes')
            .cancel('No');
            $mdDialog.show(confirm).then(function() {

                $scope.asset.busy = true;

                $rootScope.$broadcast('preloader:active');
    
                jQuery.ajax({
                    url: host+'speed/includes/pendingbinaudit.submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveSerialsToOutputBin&'+$.param($scope.asset)+'&'+$.param($scope.audit_details)+'&OutputBin='+$scope.OutputBin,
                    success: function(data) {                    
                        $rootScope.$broadcast('preloader:hide');
                        $scope.asset.busy = false;
                        if(data.Success) {                            
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );                            
                            window.location = "#!/PendingBinAudit";
                        } else {                        
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });


            }, function() {
            }); 

        };

        $scope.CompleteBinAudit = function (ev) {
            if($scope.audit_details.Accuracy < $scope.audit_details.AccuracyTarget) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Accuracy Target was not reached')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                var confirm = $mdDialog.confirm()
                .title('Are you sure, you want to complete Audit for BIN ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('No');
                $mdDialog.show(confirm).then(function() {

                    $scope.asset.busy = true;

                    $rootScope.$broadcast('preloader:active');
        
                    jQuery.ajax({
                        url: host+'speed/includes/pendingbinaudit.submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CompleteBinAudit&'+$.param($scope.asset)+'&'+$.param($scope.audit_details)+'&OutputBin='+$scope.OutputBin,
                        success: function(data) {                    
                            $rootScope.$broadcast('preloader:hide');
                            $scope.asset.busy = false;
                            if(data.Success) {                            
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );                            
                                window.location = "#!/PendingBinAudit";
                            } else {                        
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.error = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });


                }, function() {
                }); 
            }
        };



        $scope.CompleteAudit  = function () {            
            var confirm = $mdDialog.confirm()
            .title('Accuracy Target was reached, do you want to Complete Bin Audit?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent()
            .ok('Yes')
            .cancel('No');
            $mdDialog.show(confirm).then(function() {

                $scope.asset.busy = true;

                $rootScope.$broadcast('preloader:active');
    
                jQuery.ajax({
                    url: host+'speed/includes/pendingbinaudit.submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CompleteBinAudit&'+$.param($scope.asset)+'&'+$.param($scope.audit_details)+'&OutputBin='+$scope.OutputBin,
                    success: function(data) {                    
                        $rootScope.$broadcast('preloader:hide');
                        $scope.asset.busy = false;
                        if(data.Success) {                            
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );                            
                            window.location = "#!/PendingBinAudit";
                        } else {                        
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });


            }, function() {
            }); 
            
        };


    });


})(); 