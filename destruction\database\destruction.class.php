<?php
session_start();
include_once("../../audit/database/audit.class.php");
class DestructionClass extends AuditClass {

    public function GetPendingMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Destruction Page';
				return json_encode($json);
			}
      //,u.FirstName,u.LastName,si.SiteName,p.LoadId,p.ReceivedDate
      //left join users u on rr.CreatedBy = u.UserId
      //left join pallets p on rr.idPallet = p.idPallet
      //left join custompallet c on rr.CustomPalletID = c.CustomPalletID
          $geDestoyedDispositionsQ = "select group_concat(disposition_id) as destroyedIds from disposition where destruction_disposition=1";
          $geDestoyedDispositionsQEx = mysqli_query($this->connectionlink,$geDestoyedDispositionsQ);
    			if(mysqli_error($this->connectionlink)) {
    				$json['Success'] = false;
    				$json['Result'] = mysqli_error($this->connectionlink);
    				return json_encode($json);
    			}
    			if(mysqli_affected_rows($this->connectionlink) > 0) {
            $destroyedIdRow = mysqli_fetch_assoc($geDestoyedDispositionsQEx);
            $destroyedIds = $destroyedIdRow['destroyedIds'];
          }
            $query = "select * FROM (";
            $query .= "select 
            'speed_server_recovery' as 'table',
            rr.ServerID AS AssetID, 
            rr.ServerSerialNumber as SerialNumber, 
            d.disposition, 
            f.FacilityName, 
            pt.parttype, 
            rr.MPN as MPN, 
            cp.BinName, 
            rr.parttypeid,
            rr.disposition_id,
            rr.idPallet as UniqueID,
            DATEDIFF(CURRENT_DATE, rr.CreatedDate) AS aging 
            
             from speed_server_recovery rr
            left join parttype pt on rr.parttypeid = pt.parttypeid
            left join disposition d on rr.disposition_id = d.disposition_id
            left join asset_status s on rr.StatusID = s.StatusID 
            LEFT JOIN pallets p ON rr.idPallet = p.idPallet 
            LEFT JOIN facility f ON p.PalletFacilityID = f.FacilityID 
            left join site si on rr.SiteID = si.SiteID 
            left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
            where (rr.StatusID = 1 or rr.StatusID = 9) and p.PalletFacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.") and p.MaterialType != 'Media Rack' ";

            if(count($data[0]) > 0) {
              foreach ($data[0] as $key => $value) {
                if($value != '') {

                  if($key == 'idPallet') {
                    $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'MPN') {
                    $query = $query . " AND rr.MPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'MediaSerialNumber') {
                    $query = $query . " AND rr.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'PartType') {
                    $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'dispositionasigned') {
                    $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'Status') {
                    $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'BinName') {
                    $query = $query . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'FirstName') {
                    $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'AuditControllerID') {
                    $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }

                  if($key == 'FacilityName') {
                    $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'SiteName') {
                    $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }                              
                  if($key == 'dispositionasigneddate') {
                    $query = $query . " AND DATEDIFF(CURRENT_DATE, rr.CreatedDate) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }

                  if($key == 'CreatedDate') {
                    $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }


                }
              }
            }

      $query .= " UNION ALL ";

      $query .= "select 
      'asset' as 'table',
      rr.AssetScanID AS AssetID, 
      rr.SerialNumber as SerialNumber, 
      d.disposition, 
      f.FacilityName , 
      pt.parttype , 
      rr.UniversalModelNumber as MPN, 
      cp.BinName,
      rr.parttypeid,
      rr.disposition_id,
      rr.TopLevelUniqueIdentifier as UniqueID,
      DATEDIFF(CURRENT_DATE, rr.DateCreated) AS aging 
      from asset rr 
      left join parttype pt on rr.parttypeid = pt.parttypeid
      left join disposition d on rr.disposition_id = d.disposition_id
      left join statusses s on rr.StatusID = s.StatusID 
      left join pallets p on rr.idPallet = p.idPallet 
      left join facility f on rr.FacilityID = f.FacilityID
      left join site si on rr.SiteID = si.SiteID
      left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
      where (rr.StatusID = 1 or rr.StatusID = 9) and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.") and p.MaterialType != 'Media Rack' ";

      if(count($data[0]) > 0) {
        foreach ($data[0] as $key => $value) {
          if($value != '') {

            if($key == 'idPallet') {
              $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MPN') {
              $query = $query . " AND rr.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MediaSerialNumber') {
              $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'PartType') {
              $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'dispositionasigned') {
              $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'Status') {
              $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'BinName') {
              $query = $query . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'FirstName') {
              $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'AuditControllerID') {
              $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'FacilityName') {
              $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'SiteName') {
              $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }                       
            if($key == 'dispositionasigneddate') {
              $query = $query . " AND DATEDIFF(CURRENT_DATE, rr.DateCreated) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

            if($key == 'CreatedDate') {
              $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

          }
        }
      }

      $query .= " UNION ALL ";

      $query .= "select 
      'speed_media_recovery' as 'table',
      rr.MediaID AS AssetID, 
      rr.MediaSerialNumber COLLATE utf8_unicode_ci as SerialNumber, 
      d.disposition, 
      f.FacilityName , 
      pt.parttype , 
      rr.MediaMPN as MPN, 
      cp.BinName,
      rr.parttypeid,
      rr.disposition_id,
      rr.ServerSerialNumber as UniqueID,
      DATEDIFF(CURRENT_DATE, rr.CreatedDate) AS aging 
       
      from speed_media_recovery rr
      left join parttype pt on rr.parttypeid = pt.parttypeid
      left join disposition d on rr.disposition_id = d.disposition_id
      left join statusses s on rr.StatusID = s.StatusID
      left join facility f on rr.FacilityID = f.FacilityID
      left join site si on rr.SiteID = si.SiteID 
      left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
      where (rr.StatusID = 2 ) and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.")  ";

      if(count($data[0]) > 0) {
        foreach ($data[0] as $key => $value) {
          if($value != '') {

            if($key == 'idPallet') {
              $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MPN') {
              $query = $query . " AND rr.MediaMPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MediaSerialNumber') {
              $query = $query . " AND rr.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'PartType') {
              $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'dispositionasigned') {
              $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'Status') {
              $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'BinName') {
              $query = $query . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'FirstName') {
              $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'AuditControllerID') {
              $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'FacilityName') {
              $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'SiteName') {
              $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }                        
            if($key == 'dispositionasigneddate') {
              $query = $query . " AND DATEDIFF(CURRENT_DATE, rr.CreatedDate) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'CreatedDate') {
              $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

          }
        }
      }

    $query .= ") AS result ";

    if($data['OrderBy'] != '') {
      if($data['OrderByType'] == 'asc') {
        $order_by_type = 'asc';
      } else {
        $order_by_type = 'desc';
      }

      if($data['OrderBy'] == 'dispositionasigned') {
        $query = $query . " order by disposition ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'PartType') {
        $query = $query . " order by parttype ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'MediaSerialNumber') {
        $query = $query . " order by SerialNumber ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'MPN') {
        $query = $query . " order by MPN ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'disposition') {
        $query = $query . " order by disposition ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'Status') {
        $query = $query . " order by Status ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'BinName') {
        $query = $query . " order by BinName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'FirstName') {
        $query = $query . " order by FirstName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'AuditControllerID') {
        $query = $query . " order by AuditControllerID ".$order_by_type." ";
      }

              else if($data['OrderBy'] == 'FacilityName') {
        $query = $query . " order by FacilityName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'SiteName') {
        $query = $query . " order by SiteName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'LoadId') {
        $query = $query . " order by LoadId ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'ReceivedDate') {
        $query = $query . " order by ReceivedDate ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'dispositionasigneddate') {
        $query = $query . " order by aging ".$order_by_type." ";
      }

    } else {
      $query = $query . " order by aging desc ";
    }

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

      //echo $query;exit;

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				//$assets = array();
				while($row = mysqli_fetch_assoc($q)) {
					$assets[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
				//return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Serials Available';
				//return json_encode($json);
			}
		  if($data['skip'] == 0) {

        $query1 = "select count(*) FROM (";


        $query1 .= "select 
          'speed_server_recovery' as 'table',
          rr.ServerID AS AssetID, 
          rr.ServerSerialNumber as SerialNumber, 
          d.disposition, 
          f.FacilityName, 
          pt.parttype, 
          rr.MPN as MPN, 
          cp.BinName, 
          rr.parttypeid,
          rr.disposition_id,
          DATEDIFF(CURRENT_DATE, rr.CreatedDate) AS aging 
          
            from speed_server_recovery rr
          left join parttype pt on rr.parttypeid = pt.parttypeid
          left join disposition d on rr.disposition_id = d.disposition_id
          left join asset_status s on rr.StatusID = s.StatusID 
          LEFT JOIN pallets p ON rr.idPallet = p.idPallet 
          LEFT JOIN facility f ON p.PalletFacilityID = f.FacilityID 
          left join site si on rr.SiteID = si.SiteID 
          left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
          where (rr.StatusID = 1 or rr.StatusID = 9) and p.PalletFacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.") and p.MaterialType != 'Media Rack' ";

          if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
              if($value != '') {

                if($key == 'idPallet') {
                  $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MPN') {
                  $query1 = $query1 . " AND rr.MPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MediaSerialNumber') {
                  $query1 = $query1 . " AND rr.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'PartType') {
                  $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'dispositionasigned') {
                  $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'Status') {
                  $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'BinName') {
                  $query1 = $query1 . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'FirstName') {
                  $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'AuditControllerID') {
                  $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }

                if($key == 'FacilityName') {
                  $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'SiteName') {
                  $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }                              
                if($key == 'dispositionasigneddate') {
                  $query1 = $query1 . " AND DATEDIFF(CURRENT_DATE, rr.CreatedDate) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }

                if($key == 'CreatedDate') {
                  $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }


              }
            }
          }



            

          $query1 .= " UNION ALL ";

          $query1 .= "select 
          'asset' as 'table',
          rr.AssetScanID AS AssetID, 
          rr.SerialNumber as SerialNumber, 
          d.disposition, 
          f.FacilityName , 
          pt.parttype , 
          rr.UniversalModelNumber as MPN, 
          cp.BinName,
          rr.parttypeid,
          rr.disposition_id,
          DATEDIFF(CURRENT_DATE, rr.DateCreated) AS aging 
          from asset rr 
          left join parttype pt on rr.parttypeid = pt.parttypeid
          left join disposition d on rr.disposition_id = d.disposition_id
          left join statusses s on rr.StatusID = s.StatusID 
          left join pallets p on rr.idPallet = p.idPallet 
          left join facility f on rr.FacilityID = f.FacilityID
          left join site si on rr.SiteID = si.SiteID
          left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
          where (rr.StatusID = 1 or rr.StatusID = 9) and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.") and p.MaterialType != 'Media Rack' ";

          if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
              if($value != '') {

                if($key == 'idPallet') {
                  $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MPN') {
                  $query1 = $query1 . " AND rr.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MediaSerialNumber') {
                  $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'PartType') {
                  $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'dispositionasigned') {
                  $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'Status') {
                  $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'BinName') {
                  $query1 = $query1 . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'FirstName') {
                  $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'AuditControllerID') {
                  $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'FacilityName') {
                  $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'SiteName') {
                  $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }                       
                if($key == 'dispositionasigneddate') {
                  $query1 = $query1 . " AND DATEDIFF(CURRENT_DATE, rr.DateCreated) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }

                if($key == 'CreatedDate') {
                  $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }

              }
            }
          }
                  
          $query1 .= " UNION ALL ";




          $query1 .= "select 
          'speed_media_recovery' as 'table',
          rr.MediaID AS AssetID, 
          rr.MediaSerialNumber COLLATE utf8_unicode_ci as SerialNumber, 
          d.disposition, 
          f.FacilityName , 
          pt.parttype , 
          rr.MediaMPN as MPN, 
          cp.BinName,
          rr.parttypeid,
          rr.disposition_id,
          DATEDIFF(CURRENT_DATE, rr.CreatedDate) AS aging 
          
          from speed_media_recovery rr
          left join parttype pt on rr.parttypeid = pt.parttypeid
          left join disposition d on rr.disposition_id = d.disposition_id
          left join statusses s on rr.StatusID = s.StatusID
          left join facility f on rr.FacilityID = f.FacilityID
          left join site si on rr.SiteID = si.SiteID 
          left join custompallet cp on rr.CustomPalletID = cp.CustomPalletID 
          where (rr.StatusID = 2 ) and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' and rr.disposition_id in (".$destroyedIds.")  ";

          if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
              if($value != '') {

                if($key == 'idPallet') {
                  $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MPN') {
                  $query1 = $query1 . " AND rr.MediaMPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'MediaSerialNumber') {
                  $query1 = $query1 . " AND rr.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'PartType') {
                  $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'dispositionasigned') {
                  $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'Status') {
                  $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'BinName') {
                  $query1 = $query1 . " AND cp.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'FirstName') {
                  $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'AuditControllerID') {
                  $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'FacilityName') {
                  $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'SiteName') {
                  $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }                        
                if($key == 'dispositionasigneddate') {
                  $query1 = $query1 . " AND DATEDIFF(CURRENT_DATE, rr.CreatedDate) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }
                if($key == 'CreatedDate') {
                  $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                }

              }
            }
          }

          

          $query1 .= ") AS result ";
          $q1 = mysqli_query($this->connectionlink,$query1);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          if(mysqli_affected_rows($this->connectionlink) > 0) {
            $row1 = mysqli_fetch_assoc($q1);
            $count = $row1['count(*)'];
          }
          $json['total'] = $count;
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GeneratePendingMediaPannelxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pending Destruction Page';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['PendingMediaPannelxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function ValidateNextBinID($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Destruction Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$query = "select c.*,d.disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['InventoryBased'] == '1') {
				// 	$json['Success'] = false;
				// 	$json['Error'] = 'BIN is dedicated for Sub Component';
				// 	return json_encode($json);
				// }
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Error'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Error'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['CustomPalletID'] = $row['CustomPalletID'];
				$json['BinName'] = $row['BinName'];
				$json['disposition_id'] = $row['disposition_id'];
				if($row['AcceptAllDisposition'] == '1') {
					$json['disposition'] = 'All Disposition';
				} else {
					$json['disposition'] = $row['disposition'];
				}
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

  public function ProcessPendingDestruction($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Destruction Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access Pending Destruction Page';
				return json_encode($json);
			}

			$json = array(
				'Success' => false,
				'Result' => $data
			);
			//return json_encode($json);
			//Start validate Audit Controller

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['DestructionController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Destruction Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Destruction Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller


      if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);
				}
				//End get to_cp_details

        //if(isset($data['RecoveryTypeID']) && ($data['RecoveryTypeID'] == '6' || $data['RecoveryTypeID'] == '1')){
        if(isset($data['table']) && ($data['table'] == 'asset')) {

          $to_status = '12';//Added to Shipment
          $to_status_text = 'Shreded';
          $to_disposition_id = $to_cp['disposition_id'];
          $assetQuantity = 1;

          //get existing record data
          $getExistingDataQ = "select a.*,cp.BinName,cp.disposition_id as to_cp_disposition_id from asset a 
          left join custompallet cp on a.CustomPalletID = cp.CustomPalletID 
          where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          //$to_disposition_id = $existingData['to_cp_disposition_id'];

          //Start validate destination bin capacity and customer lock using common function
          // Use customer ID directly from asset data
          $assetCustomerID = isset($existingData['AWSCustomerID']) ? $existingData['AWSCustomerID'] : null;

          $binValidation = $this->ValidateBinCapacityAndCustomerLock(
            $data['NewCustomPalletID'],
            $assetCustomerID,
            $to_cp['BinName'],
            $_SESSION['user']['UserId'],
            'Pending Destruction'
          );

          if (!$binValidation['Success']) {
            $json['Success'] = false;
            $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
            return json_encode($json);
          }
          //End validate destination bin

          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])) {
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }
          }
          //Start update Media SN
          $query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."',RecentDispositionDate = NOW(),RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Created in Pending Destruction' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN
          
          //Start update Custom Pallet Items
          $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' WHERE AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";          
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Items
          

          //Start update Custom Pallet Counts
          $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
          $q3 = mysqli_query($this->connectionlink,$query3);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }

          $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$existingData['CustomPalletID'])."'";
          $q4 = mysqli_query($this->connectionlink,$query4);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Counts
          


          //Start insert into tracking
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') from BIN (BINID:'.$existingData['BinName'].')';          
          $action .= " - Controller: " . $data['AuditControllerName'];	
          $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking

          //start insert into destruction history
          $query7 = "insert into destruction_history(AssetScanID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
          ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history
        } if(isset($data['table']) && ($data['table'] == 'speed_server_recovery')) {

          $to_status = '12';//Added to Shipment
          $to_status_text = 'Shreded';
          $to_disposition_id = $to_cp['disposition_id'];
          $assetQuantity = 1;


          //get existing record data
          $getExistingDataQ = "select s.*,c.BinName from speed_server_recovery s 
          left join custompallet c on s.CustomPalletID = c.CustomPalletID 
          where s.ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          if(mysqli_affected_rows($this->connectionlink) > 0){
            $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          } else {
            $json['Success'] = false;
            $json['Result'] = 'Invalid Serial';
            return json_encode($json);
          }
          
          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])){
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }

          }
          //Start update Media SN          
  				$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN

          //Start update Custom Pallet Items
          $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' WHERE ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";          
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Items
          

          //Start update Custom Pallet Counts
          $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
          $q3 = mysqli_query($this->connectionlink,$query3);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }

          $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$existingData['CustomPalletID'])."'";
          $q4 = mysqli_query($this->connectionlink,$query4);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Counts


          //Start insert into tracking          
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') from BIN (BINID:'.$existingData['BinName'].')';
          $action .= " - Controller: " . $data['AuditControllerName'];	
          $query6 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',NOW(),'".$_SESSION['user']['UserId']."','".$data['SerialNumber']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking


          //start insert into destruction history
          $query7 = "insert into destruction_history(ServerID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
          ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history
        } else if(isset($data['table']) && ($data['table'] == 'speed_media_recovery')) {
          //get existing record data
          $getExistingDataQ = "select m.*,c.BinName from speed_media_recovery m 
          left join custompallet c on m.CustomPalletID = c.CustomPalletID 
          where m.MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])){
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }

          }
          //Start update Media SN          
  				$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '3' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN

          
          //Start update Custom Pallet Items
          $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' WHERE MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";          
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Items
          

          //Start update Custom Pallet Counts
          $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
          $q3 = mysqli_query($this->connectionlink,$query3);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }

          $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$existingData['CustomPalletID'])."'";
          $q4 = mysqli_query($this->connectionlink,$query4);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Counts


          //Start insert into tracking          
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') from BIN (BINID:'.$existingData['BinName'].')';          
          $action .= " - Controller: " . $data['AuditControllerName'];	
          $query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,ServerID,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',NOW(),'".$_SESSION['user']['UserId']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking


          //start insert into destruction history
          $query7 = "insert into destruction_history(MediaID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
          ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history         

        }

				$json['Success'] = true;
				$json['Result'] = "Destruction Process Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Next BIN ID";
				return json_encode($json);
			}





			if(!empty($data['ShippingContainerID'])) {

        $query22 = "select c.*,d.disposition from shipping_containers c
				left join disposition  d on  c.disposition_id = d.disposition_id
				where c.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Shipment Container";
					return json_encode($json);
				}       
        $to_status = '8';//Added to Shipment
        $to_status_text = 'Destructed and Added to Shipment Container';
        $to_disposition_id = $to_cp['disposition_id'];
        $assetQuantity = 1;

        //if(isset($data['RecoveryTypeID']) && ($data['RecoveryTypeID'] == '6' || $data['RecoveryTypeID'] == '1')){
        if(isset($data['table']) && ($data['table'] == 'asset')) {
          //get existing record data
          $getExistingDataQ = "select a.*,cp.BinName from asset a 
          left join custompallet cp on a.CustomPalletID = cp.CustomPalletID 
          where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])){
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }
          }
          //Start update Media SN
          $query1 = "update asset set CustomPalletID = NULL,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."',RecentDispositionDate = NOW(),RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Created in Pending Destruction' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";

          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN
          
          $query2 = "delete from custompallet_items WHERE AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";

          //Start update Custom Pallet Items
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }

          //Start insert into Shipment Container
          /*$query5 = "insert into shipping_container_serials (SerialNumber,RackRecoveryRecordID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,RackRecoverySerialNumber,byproduct_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."')";
          $q5 = mysqli_query($this->connectionlink,$query5);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }*/
          //End insert into Shipment Container
          if($data['byproduct_id'] > 0) {
            $query7 = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid where b.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."' ";
            $q7 = mysqli_query($this->connectionlink,$query7);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
              $row7 = mysqli_fetch_assoc($q7);

              $query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,byproduct_id,FromCustomPalletID,FromBinName,FromDispositionID) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$row7['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."','".$existingData['CustomPalletID']."','".mysqli_real_escape_string($this->connectionlink,$existingData['BinName'])."','".$existingData['disposition_id']."')";
              $q5 = mysqli_query($this->connectionlink,$query5);
              if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
              }
            }
          }

          //Start insert into tracking
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to Shipment Container ('.$data['ShippingContainerID'].') from BIN (BINID:'.$existingData['BinName'].')';          
          $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking

          //start insert into destruction history
          $query7 = "insert into destruction_history(AssetScanID,FromCustomPalletID,FromBinName,ToShippingContainerID,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,SiteID,RigID,AuditController,bulk_transaction_flag,serial_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
          ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history


        } else if(isset($data['table']) && ($data['table'] == 'speed_server_recovery')) {

          //get existing record data
          $getExistingDataQ = "select s.*,c.BinName from speed_server_recovery s 
          left join custompallet c on s.CustomPalletID = c.CustomPalletID 
          where s.ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])){
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }

          }
          //Start update Media SN
  				$query1 = "update speed_server_recovery set CustomPalletID = NULL,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";

          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN

          //$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where AssemblyRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";
          $query2 = "delete from custompallet_items WHERE ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";

          //Start update Custom Pallet Items
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End update Custom Pallet Items
          //Start insert into Shipment Container
          /*$query5 = "insert into shipping_container_serials (SerialNumber,AssemblyRecoveryRecordID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,AssemblyRecoverySerialNumber,byproduct_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."')";
          $q5 = mysqli_query($this->connectionlink,$query5);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }*/
          //End insert into Shipment Container
          if($data['byproduct_id'] > 0) {
  					$query7 = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid where b.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."' ";
  					$q7 = mysqli_query($this->connectionlink,$query7);
  					if(mysqli_error($this->connectionlink)) {
  						$json['Success'] = false;
  						$json['Result'] = mysqli_error($this->connectionlink);
  						return json_encode($json);
  					}
  					if(mysqli_affected_rows($this->connectionlink) > 0) {
  						$row7 = mysqli_fetch_assoc($q7);

              $query5 = "insert into shipping_container_serials (SerialNumber,ServerID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,byproduct_id,FromCustomPalletID,FromBinName,FromDispositionID) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$row7['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".$existingData['disposition_id']."')";
              $q5 = mysqli_query($this->connectionlink,$query5);
              if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
              }
  					}
  				}

          //Start insert into tracking
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to Shipment Container ('.$data['ShippingContainerID'].') from BIN (BINID:'.$existingData['BinName'].')';          
          $query6 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',NOW(),'".$_SESSION['user']['UserId']."','".$data['SerialNumber']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking

          //start insert into destruction history
          $query7 = "insert into destruction_history(ServerID,FromCustomPalletID,FromBinName,ToShippingContainerID,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,SiteID,RigID,AuditController,bulk_transaction_flag,serial_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
          ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history

        } else if(isset($data['table']) && ($data['table'] == 'speed_media_recovery')) {
          //get existing record data
          $getExistingDataQ = "select m.*,c.BinName from speed_media_recovery m 
          left join custompallet c on m.CustomPalletID = c.CustomPalletID 
          where m.MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          // check custompallet for Audit Lock
          if(!empty($existingData['CustomPalletID'])){
            $checkCPQ = "select AuditLocked from custompallet where CustomPalletID='".$existingData['CustomPalletID']."'";
            $checkCPQEx = mysqli_query($this->connectionlink,$checkCPQ);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0){
              $cpRow = mysqli_fetch_assoc($checkCPQEx);
              if($cpRow['AuditLocked'] == '1') {
      					$json['Success'] = false;
      					$json['Result'] = 'The BIN in which Serial exists is locked for Audit';
      					return json_encode($json);
      				}
            }

          }
          //Start update Media SN
  				$query1 = "update speed_media_recovery set CustomPalletID = NULL,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '5' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";

          $q1 = mysqli_query($this->connectionlink,$query1);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Media SN

          //$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where ComponentRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";
          $query2 = "delete from custompallet_items WHERE MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."'";


  				//Start update Custom Pallet Items
  				$q2 = mysqli_query($this->connectionlink,$query2);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
  				//End update Custom Pallet Items
          //Start insert into Shipment Container
          /*$query5 = "insert into shipping_container_serials (SerialNumber,ComponentRecoveryRecordID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ComponentRecoverySerialNumber,byproduct_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."')";
          $q5 = mysqli_query($this->connectionlink,$query5);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }*/
          //End insert into Shipment Container
          //Start insert in Shipment Container Serials
  				if($data['byproduct_id'] > 0) {
  					$query7 = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid where b.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."' ";
  					$q7 = mysqli_query($this->connectionlink,$query7);
  					if(mysqli_error($this->connectionlink)) {
  						$json['Success'] = false;
  						$json['Result'] = mysqli_error($this->connectionlink);
  						return json_encode($json);
  					}
  					if(mysqli_affected_rows($this->connectionlink) > 0) {
  						$row7 = mysqli_fetch_assoc($q7);

              $query5 = "insert into shipping_container_serials (SerialNumber,MediaID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,byproduct_id,FromCustomPalletID,FromBinName,FromDispositionID) values ('".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$row7['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".$existingData['disposition_id']."')";
              $q5 = mysqli_query($this->connectionlink,$query5);
              if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
              }
  					}
  				}
  				//End insert in Shipment Container Serials

          //Start insert into tracking
          $action = 'Serial Number ('.$data['SerialNumber'].') is Destroyed and Added to Shipment Container ('.$data['ShippingContainerID'].') from BIN (BINID:'.$existingData['BinName'].')';          
          $query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,ServerID,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttype'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,$existingData['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',NOW(),'".$_SESSION['user']['UserId']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking

          //start insert into destruction history
          $query7 = "insert into destruction_history(MediaID,FromCustomPalletID,FromBinName,ToShippingContainerID,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,SiteID,RigID,AuditController,bulk_transaction_flag,serial_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$data['AssetID'])."','".$existingData['CustomPalletID']."','".$existingData['BinName']."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".$existingData['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW(),
          '".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."',
          '".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Pending Destruction')";
          $q7 = mysqli_query($this->connectionlink,$query7);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into destruction history

        }
        /*else{
          //get existing record data
          $getExistingDataQ = "select * from unserialized_recovery_records where UnserializedRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";
          $getExistingDataQEx = mysqli_query($this->connectionlink,$getExistingDataQ);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $existingData = mysqli_fetch_assoc($getExistingDataQEx);
          //Start update Media SN
  				$query1 = "update unserialized_recovery_records set CustomPalletID = '',DispositionBin = '',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',AuditController = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where UnserializedRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";

          //$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where UnserializedRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";
          $query2 = "delete from custompallet_items WHERE UnserializedRecoveryRecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RackRecoveryRecordID'])."'";

          // get part type quantity from UnSerialized parts
          $query3 = "select Quantity from unserialized_recovery_records where UnserializedRecoveryRecordID='".mysqli_real_escape_string($this->connectionlink,$data['UnserializedRecoveryRecordID'])."'";
          $q3 = mysqli_query($this->connectionlink,$query3);
  				if(mysqli_error($this->connectionlink)) {
  					$json['Success'] = false;
  					$json['Result'] = mysqli_error($this->connectionlink);
  					return json_encode($json);
  				}
          if(mysqli_affected_rows($this->connectionlink)>0){
            $row3 = mysqli_fetch_assoc($q3);
            $assetQuantity = $row3['Quantity'];
          }

        }*/



				//Start update Custom Pallet Counts
				/*$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + ".$assetQuantity." WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}*/

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - ".$assetQuantity." WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$existingData['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				$json['Success'] = true;
				$json['Result'] = "Destruction Process Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Shipping Container";
				return json_encode($json);
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetAllNextBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Destruction Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destroyed_disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.destroyed_disposition = 1 or d.ssd_disposition = 1) order by c.BinName ";
      $query = "select c.*,d.disposition,d.ssd_disposition,d.destroyed_disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.destroyed_disposition = 1 ) order by c.BinName ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No BINs Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

  public function SelectWorkstationDestruction($data)
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    $sql = "Select SiteID, SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' order by SiteName";

    //$sql = "select SiteID,SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' and (Locked = '0' or LockedForUser = '".$_SESSION['user']['UserId']."') and order by SiteName";

    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }

  public function GetDestrcutionRig($data)
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    /*$sql = "select sd.*,d.disposition from station_disposition_mapping sd,disposition d,site S where sd.SiteID = S.SiteID AND sd.disposition_id = d.disposition_id
    AND S.SiteID = '".$data['SiteID']."'";*/
    $sql = "select R.SiteID,R.Rigname,S.SiteName,R.RigID from Rig R
          LEFT JOIN site S ON R.SiteID = S.SiteID
          WHERE R.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
    //sd.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }

  public function AddSerialToContainer ($data) {
    if(!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => 'No Data'
    );
    try {
      if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
        $json['Success'] = false;
        $json['Result'] = 'No Access to Pending Destruction page';
        return json_encode($json);
      }
      if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Destruction')) {
        $json['Success'] = false;
        $json['Result'] = 'You have Read only Access to Pending Destruction';
        return json_encode($json);
      }


      //Start check If any byproduct is added to container
      $query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
      $q55 = mysqli_query($this->connectionlink,$query55);
      if(mysqli_error($this->connectionlink)) {
        $json['Success'] = false;
        $json['Result'] = mysqli_error($this->connectionlink);
        return json_encode($json);
      }
      if(mysqli_affected_rows($this->connectionlink) > 0) {
        $row55 = mysqli_fetch_assoc($q55);
        if($row55['count(*)'] > 0) {
          $json['Success'] = false;
          $json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
          return json_encode($json);
        }
      }
      //End check If any byproduct is added to container


      //Start check If ContainerMPNLock Satisfies
      /*$contaner_lock = $this->isContainerMPNLock($data['ShippingID'],$data['UniversalModelNumber'],$data['ShippingContainerID']);
      if($contaner_lock['Success']) {
      } else {
        $json['Success'] = false;
        $json['Result'] = $contaner_lock['Error'];
        return json_encode($json);
      }*/
      //End check If ContainerMPNLock Satisfied

      //Start get serialnumber details
      $query = "select a.*,m.part_type from asset a
      left join catlog_creation m on a.UniversalModelNumber = m.mpn_id
      where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
      $query = "select a.* from asset a where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.StatusID = '1' ";
      $q = mysqli_query($this->connectionlink,$query);
      if(mysqli_error($this->connectionlink)) {
        $json['Success'] = false;
        $json['Result'] = mysqli_error($this->connectionlink);
        return json_encode($json);
      }
      if(mysqli_affected_rows($this->connectionlink) > 0) {
        $row = mysqli_fetch_assoc($q);
        // if($row['StatusID'] != '1') {
        // 	$json['Success'] = false;
        // 	$json['Result'] = 'Serial Number Status is not active';
        // 	return json_encode($json);
        // }

        //Start check for Seal Matching
        $query22 = "select sanitization_verification_id,sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc";
        $q22 = mysqli_query($this->connectionlink,$query22);
        if(mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        if(mysqli_affected_rows($this->connectionlink) > 0) {
          $row22 = mysqli_fetch_assoc($q22);
          if($row22['sanitization_seal_id'] != $data['sanitization_seal_id'])	{
            $json['Success'] = false;
            $json['Result'] = 'Seal ID not matching';
            return json_encode($json);
          } else {
          }
        } else {
          // $json['Success'] = false;
          // $json['Result'] = 'Serial not involved in Sanitization';
          // return json_encode($json);
        }
        //End check for Seal Matching

        //Start get Shipping Disposition
        $query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
        $q1 = mysqli_query($this->connectionlink,$query1);
        if(mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        if(mysqli_affected_rows($this->connectionlink) > 0) {
          $row1 = mysqli_fetch_assoc($q1);
          if($row1['ShipmentStatusID'] != '1') {
            $json['Success'] = false;
            $json['Result'] = 'Shipment Status is not active';
            return json_encode($json);
          }
          if($row1['disposition_id'] != $row['disposition_id']) {
            $json['Success'] = false;
            $json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
            return json_encode($json);
          }

          if($row1['FacilityID'] != $row['FacilityID']) {
            $json['Success'] = false;
            $json['Result'] = 'Shipment Facility is Different from Serial Number Facility';
            return json_encode($json);
          }

          //Start get Part type from MPN and validate MPN
          $query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          if(mysqli_affected_rows($this->connectionlink) > 0) {
            $row6 = mysqli_fetch_assoc($q6);
          } else {
            $json['Success'] = false;
            $json['Result'] = 'Invalid MPN';
            return json_encode($json);
          }
          //End get Part type from MPN and validate MPN

          //Start Remove from CustomPallet
          $query2 = "delete from custompallet_items where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
          $q2 = mysqli_query($this->connectionlink,$query2);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
          $q3 = mysqli_query($this->connectionlink,$query3);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          $query4 = "update asset set CustomPalletID = NULL,StatusID=8,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
          $query4 = "update asset set CustomPalletID = NULL,StatusID=8,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
          $q4 = mysqli_query($this->connectionlink,$query4);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End Remove from CustomPallet

          //Start insert into Shipment Container
          //$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
          //$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
          $query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
          $q5 = mysqli_query($this->connectionlink,$query5);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into Shipment Container

          //Start insert into tracking
          $action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') of Shipment ('.$data['ShippingID'].')';
          $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
          $q6 = mysqli_query($this->connectionlink,$query6);
          if(mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
          }
          //End insert into tracking

          //Start check IF MPN Changed
          if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {//If MPN of Asset Changed
            $query7 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
            $q7 = mysqli_query($this->connectionlink,$query7);
            if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
            }

            $desc = "Asset MPN Changed from '".$row['UniversalModelNumber']."' to '".$data['UniversalModelNumber']."' in Shipment Prep Screen";
            $query8 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $q8 = mysqli_query($this->connectionlink,$query8);
          }
          //End check IF MPN Changed

          $json['Success'] = true;
          $json['Result'] = 'Serial Number added to Shipment Container';
          return json_encode($json);
        } else {
          $json['Success'] = false;
          $json['Result'] = 'Invalid Shipment';
          return json_encode($json);
        }
        //End get Shiping Disposition
      } else {
        $json['Success'] = false;
        $json['Result'] = 'SN is not in eViridis';
        return json_encode($json);
      }
      //End get serialnumber details
    } catch (Exception $e) {
      $json['Success'] = false;
      $json['Result'] = $e->getMessage();
      return json_encode($json);
    }
  }

  public function GeneratePendingDestructionPannelxls($data) {
    if(!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    /*
    if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Destruction List')) {
      $json['Success'] = false;
      $json['Result'] = 'No Access to Pending Destruction Page';
      return json_encode($json);
    }*/
    $json = array(
      'Success' => false,
      'Result' => $data
    );
    $_SESSION['PendingDestructionPannelxls'] = $data;
    $json['Success'] = true;
    //$json['Result'] = $result;
    return json_encode($json);
  }

 public function DestructionConfigurationSave($data)
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => $data
    );
    try {
      //$data['BreRequired'] = !empty($data['BreRequired']) ? $data['BreRequired'] : 'No';
      if(!empty($data['Destruction_Configuration_ID'])){
        $query = "select count(*) from Destruction_Configuration where parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "' and disposition_id = '".mysqli_real_escape_string($this->connectionlink, $data['disposition_id'])."' and RigID = '".mysqli_real_escape_string($this->connectionlink, $data['RigID'])."' AND Destruction_Configuration_ID != '".mysqli_real_escape_string($this->connectionlink, $data['Destruction_Configuration_ID'])."' ";
        $q = mysqli_query($this->connectionlink, $query);
        if (mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        if (mysqli_affected_rows($this->connectionlink) > 0) {
          $row = mysqli_fetch_assoc($q);
          if ($row['count(*)'] > 0) {
            $json['Success'] = false;
            $json['Result'] = 'Destruction Combination Already Exists';
            return json_encode($json);
          }
        }

        $updateQuery = "update Destruction_Configuration set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',parttypeid='" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "',disposition_id='" . mysqli_real_escape_string($this->connectionlink, $data['disposition_id']) . "',RigID='" . mysqli_real_escape_string($this->connectionlink, $data['RigID']) . "',Post_Destruction_Serialized='" . mysqli_real_escape_string($this->connectionlink, $data['Post_Destruction_Serialized']) . "',byproduct_id='" . mysqli_real_escape_string($this->connectionlink, $data['byproduct_id']) . "',Post_Destruction_Disposition_id='" . mysqli_real_escape_string($this->connectionlink, $data['Post_Destruction_Disposition_id']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',DateUpdated =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where Destruction_Configuration_ID='" . mysqli_real_escape_string($this->connectionlink, $data['Destruction_Configuration_ID']) . "'";
        //echo $updateQuery;exit;
        $updateQueryEx = mysqli_query($this->connectionlink, $updateQuery);
        if (mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        $json['Success'] = true;
        $json['Result'] = "Updated successfully";
        return json_encode($json);

      }else{
        //Start check If Destruction_Configuration already mapped
         $query = "select count(*) from Destruction_Configuration where parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "' and disposition_id = '".mysqli_real_escape_string($this->connectionlink, $data['disposition_id'])."' and RigID = '".mysqli_real_escape_string($this->connectionlink, $data['RigID'])."'";
        $q = mysqli_query($this->connectionlink, $query);
        if (mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        if (mysqli_affected_rows($this->connectionlink) > 0) {
          $row = mysqli_fetch_assoc($q);
          if ($row['count(*)'] > 0) {
            $json['Success'] = false;
            $json['Result'] = 'Destruction Combination Already Exists';
            return json_encode($json);
          }
        }
        //End check If Part Type already mapped to Destruction_Configuration

        $query = "insert into Destruction_Configuration (FacilityID,parttypeid,disposition_id,RigID,Post_Destruction_Serialized,byproduct_id,Post_Destruction_Disposition_id,Status,DateCreated,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['disposition_id']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['RigID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Post_Destruction_Serialized']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['byproduct_id']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Post_Destruction_Disposition_id']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
        $q = mysqli_query($this->connectionlink, $query);
        if (mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        $insert_id = mysqli_insert_id($this->connectionlink);
        $json['Success'] = true;
        $json['Result'] = "New Destruction Configuration Created";
        $json['Destruction_Configuration_ID'] = $insert_id;
        return json_encode($json);
      }

    } catch (Exception $e) {
      $json['Success'] = false;
      $json['Result'] = $e->getMessage();
      return json_encode($json);
    }
  }


  public function GetDestructionConfigurationDetails($data)
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => $data
    );

   /* if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
      $json['Success'] = false;
      $json['Result'] = 'No Access to Manufacturer Page';
      return json_encode($json);
    }
    if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
      $json['Success'] = false;
      $json['Result'] = 'You have Read only Access to Manufacturer Page';
      return json_encode($json);
    }
*/
    //return json_encode($json);
    $query = "select * from Destruction_Configuration where Destruction_Configuration_ID = '" . mysqli_real_escape_string($this->connectionlink, $data['Destruction_Configuration_ID']) . "' ";
    $q = mysqli_query($this->connectionlink, $query);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    }
    if (mysqli_affected_rows($this->connectionlink) > 0) {
      $row = mysqli_fetch_assoc($q);
      $json['Success'] = true;
      $json['Result'] = $row;
    } else {
      $json['Success'] = false;
      $json['Result'] = "Invalid Destruction_Configuration ID";
    }
    return json_encode($json);
  }

  public function GetDestructionConfigurationList($data)
  {
    try {
      if (!isset($_SESSION['user'])) {
        $json['Success'] = false;
        $json['Result'] = 'Login to continue';
        return json_encode($json);
      }
      $json = array(
        'Success' => false,
        'Result' => $data['Destruction_Configuration_ID']
      );

     /* if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
        $json['Success'] = false;
        $json['Result'] = 'No Access to Manufacturer Page';
        return json_encode($json);
      }*/

      $query = "select dc.*,p.parttype,d.disposition,R.Rigname,ss.StatusName,f.FacilityName,bp.part_type as byproductsparttype,bpp.parttype as bppparttype,dc.Post_Destruction_Disposition_id as PostDestructionDispositionid,dp.disposition as PostDestructionDispositionName from Destruction_Configuration dc
      left join parttype p on p.parttypeid = dc.parttypeid
      left join disposition d on d.disposition_id = dc.disposition_id
      left join Rig R on R.RigID = dc.RigID
      left join by_products bp on bp.byproduct_id = dc.byproduct_id
      left join parttype bpp on bpp.parttypeid = bp.part_type
      left join disposition dp on dp.disposition_id = dc.Post_Destruction_Disposition_id
      left join statusses ss on dc.Status = ss.StatusID
      left join facility f on dc.FacilityID = f.FacilityID
      where dc.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
      if (count($data[0]) > 0) {
        foreach ($data[0] as $key => $value) {
          if ($value != '') {
            if ($key == 'parttype') {
              $query = $query . " AND p.parttype like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if ($key == 'disposition') {
              $query = $query . " AND d.disposition like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if ($key == 'Rigname') {
              $query = $query . " AND R.Rigname like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if ($key == 'FacilityName') {
              $query = $query . " AND f.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if ($key == 'byproductsparttype') {
              $query = $query . " AND bp.part_type like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if ($key == 'StatusName') {
              $query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
             if ($key == 'Post_Destruction_Serialized') {
              $query = $query . " AND dc.Post_Destruction_Serialized like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
            }
            if($key == 'PostDestructionDispositionName') {
              $query = $query . " AND dp.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'bppparttype') {
              $query = $query . " AND bpp.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
          }
        }
      }

      if ($data['OrderBy'] != '') {
        if ($data['OrderByType'] == 'asc') {
          $order_by_type = 'asc';
        } else {
          $order_by_type = 'desc';
        }

        if ($data['OrderBy'] == 'parttype') {
          $query = $query . " order by p.parttype " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'disposition') {
          $query = $query . " order by d.disposition " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'Rigname') {
          $query = $query . " order by R.Rigname " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'FacilityName') {
          $query = $query . " order by f.FacilityName " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'part_type') {
          $query = $query . " order by bp.part_type " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'StatusName') {
          $query = $query . " order by ss.StatusName " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'Post_Destruction_Serialized') {
          $query = $query . " order by dc.Post_Destruction_Serialized " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'disposition') {
          $query = $query . " order by dp.PostDestructionDispositionName " . $order_by_type . " ";
        } else if ($data['OrderBy'] == 'parttype') {
          $query = $query . " order by bpp.bppparttype " . $order_by_type . " ";
        }
      } else {
        $query = $query . " order by FacilityName desc ";
      }

      $query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

      $q = mysqli_query($this->connectionlink, $query);
      if (mysqli_error($this->connectionlink)) {
        $json['Success'] = false;
        $json['Result'] = mysqli_error($this->connectionlink);
        return json_encode($json);
      }
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($q)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = "No Destruction Configuration Available";
      }

      if ($data['skip'] == 0) {

        $query1 = "select count(*) from Destruction_Configuration dc
      left join parttype p on p.parttypeid = dc.parttypeid
      left join disposition d on d.disposition_id = dc.disposition_id
      left join Rig R on R.RigID = dc.RigID
      left join by_products bp on bp.byproduct_id = dc.byproduct_id
      left join parttype bpp on bpp.parttypeid = bp.part_type
      left join disposition dp on dp.disposition_id = dc.Post_Destruction_Disposition_id
      left join statusses ss on dc.Status = ss.StatusID
      left join facility f on dc.FacilityID = f.FacilityID
      where dc.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
        if (count($data[0]) > 0) {
          foreach ($data[0] as $key => $value) {
            if ($value != '') {

              if ($key == 'parttype') {
                $query1 = $query1 . " AND p.parttype like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'disposition') {
                $query1 = $query1 . " AND d.disposition like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'Rigname') {
                $query1 = $query1 . " AND R.Rigname like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'FacilityName') {
                $query1 = $query1 . " AND f.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'byproductsparttype') {
                $query1 = $query1 . " AND bp.part_type like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'StatusName') {
                $query1 = $query1 . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'Post_Destruction_Serialized') {
                $query1 = $query1 . " AND dc.Post_Destruction_Serialized like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'PostDestructionDispositionName') {
                $query1 = $query1 . " AND dp.disposition like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
              if ($key == 'bppparttype') {
                $query1 = $query1 . " AND bpp.parttype like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
              }
            }
          }
        }

        $q1 = mysqli_query($this->connectionlink, $query1);
        if (mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        if (mysqli_affected_rows($this->connectionlink) > 0) {
          $row1 = mysqli_fetch_assoc($q1);
          $count = $row1['count(*)'];
        }
        $json['total'] = $count;
      }
      return json_encode($json);
    } catch (Exception $ex) {
      $json['Success'] = false;
      $json['Result'] = $ex->getMessage();
      return json_encode($json);
    }
  }

  public function SelectRemovalcodeparttype()
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    $sql = "Select * from parttype where Status = '1' AND FacilityID = '".$_SESSION['user']['FacilityID']."' order by parttype ASC";
    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }

  public function SelectDestructionRig()
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    $sql = "select r.*,S.SiteName from Rig r,site S where r.SiteID = S.SiteID and S.FacilityID='".$_SESSION['user']['FacilityID']."' and r.Status = '1' order by Rigname ASC";
    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }

  public function SelectDestructionbyproducts()
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    //$sql = "Select * from Rig where Status = '1' order by Rigname ASC";
    $sql = "select bp.*,p.parttype from by_products bp,parttype p where bp.part_type = p.parttypeid and bp.StatusID = '1' ";
    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }



  public function GetBulkMediaDestructionSourceBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {			
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Serial Destruction Page';
				return json_encode($json);
			}
                        
			$json = array(
				'Success' => false,
				'Result' => $data
			);	

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destroyed_disposition from custompallet c 
			left join disposition  d on  c.disposition_id = d.disposition_id 
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.destruction_disposition = 1) and c.LockedForBulkMediaDestruction = 0 order by c.BinName ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;				
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No BINs Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}


  public function ValidatDestructionController ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['DestructionController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Destruction Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


  public function StartBulkMediaProcess ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);        
		try {
          if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bulk Serial Destruction Page';
            return json_encode($json);
          }

          if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
              $json['Success'] = false;
              $json['Result'] = 'You have Read only Access to Bulk Serial Destruction Page';
              return json_encode($json);
          }

            //Start validate source bin
            $query = "select c.*,d.ssd_disposition,d.hdd_disposition,d.destruction_disposition from custompallet c 
            left join disposition d on c.disposition_id = d.disposition_id 
            where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                //where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.ssd_disposition = 1 or d.hdd_disposition = 1) and c.LockedForBulkMediaDestruction = 0 order by c.BinName ";
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Source BIN Facility is different from User Facility';
                    return json_encode($json);    
                }

                if($row['StatusID'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'Source BIN Status is not Active';
                    return json_encode($json);    
                }

                // if($row['LockedForBulkMediaDestruction'] == '1') {
                //     $json['Success'] = false;
                //     $json['Result'] = 'Source BIN is Locked';
                //     return json_encode($json);    
                // }

                // if($row['ssd_disposition'] != '1' && $row['hdd_disposition'] != '1') {
                //     $json['Success'] = false;
                //     $json['Result'] = 'BIN disposition is not meant for Media';
                //     return json_encode($json);    
                // }

                if($row['destruction_disposition'] != '1') {
                  $json['Success'] = false;
                  $json['Result'] = 'BIN disposition is not meant for Destruction';
                  return json_encode($json);    
                }
                $data['SourceBinID'] = $row['CustomPalletID'];
                $json['SourceCustomPalletID'] = $row['CustomPalletID'];
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Source BIN';
                return json_encode($json);
            }
            //End validate source bin

			      $query3 = "insert into speed_bulk_media_process (MediaType,NextStatus,CreatedDate,CreatedBy,NewCustomPalletID,ControllerLoginID,FacilityID,SourceBinID";
            if($data['NextStatus'] == 'Degauss and Shreded') {
                $query3 = $query3.",DestructionCustomPalletID";
            }
            $query3 = $query3.") values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['NextStatus'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            if($data['NextStatus'] == 'Degauss and Shreded'){
                $query3 = $query3.",'".mysqli_real_escape_string($this->connectionlink,$data['DestructionCustomPalletID'])."'";
            }
            $query3 = $query3.")";
            $q3 = mysqli_query($this->connectionlink,$query3);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }            
            $ID = mysqli_insert_id($this->connectionlink);

            //Start lock bin
            // $query4 = "insert into custompallet_lock_history (CustomPalletID,LockedType,LockedPage,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."','Locked','Bulk Media Destruction',NOW(),'".$_SESSION['user']['UserId']."')";
            // $q4 = mysqli_query($this->connectionlink,$query4);
            // if(mysqli_error($this->connectionlink)) {
            //     $json['Success'] = false;
            //     $json['Result'] = mysqli_error($this->connectionlink);
            //     return json_encode($json);
            // }
            // $query5 = "update custompallet set LockedForBulkMediaDestruction = '1',LockedTime = NOW(),LockedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            // $q5 = mysqli_query($this->connectionlink,$query5);
            // if(mysqli_error($this->connectionlink)) {
            //     $json['Success'] = false;
            //     $json['Result'] = mysqli_error($this->connectionlink);
            //     return json_encode($json);
            // }
            //End lock bin

            $json['Success'] = true;
            $json['BulkMediaProcessID'] = $ID;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


  public function AddSerialToBulkList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Serial Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Serial Destruction Page';
                return json_encode($json);
            }

            //Start get media details
            $query = "select m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName from asset m  
                left join asset_status ss on m.StatusID = ss.StatusID 
                left join disposition d on m.disposition_id = d.disposition_id 
                left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                left join users u on m.CreatedBy = u.UserId 
                left join facility f on m.FacilityID = f.FacilityID 
                where m.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'
            ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End get media details
			      if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);

                if($row['CustomPalletID'] != $data['SourceCustomPalletID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Only Serial from '.$data['SourceBinID'].' location are allowed to scan';
                    return json_encode($json);
                }

                //Start check If media already added or not
                $query121 = "select * from speed_bulk_media_process_details where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' and BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q121 = mysqli_query($this->connectionlink,$query121);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row121 = mysqli_fetch_assoc($q121);
                    $json['Success'] = false;
                    $json['Result'] = 'SN already scanned';
                    return json_encode($json);
                }
                //End check If media already added or not

                // if($row['MediaType'] != $data['MediaType']) {
                //     $json['Success'] = false;
                //     $json['Result'] = 'Media Type is not matching';
                //     return json_encode($json);    
                // }
                if($row['Status'] == 'Shreded') {
                    $json['Success'] = false;
                    $json['Result'] = 'SN already destroyed';
                    return json_encode($json);
                }
                // if($row['Status'] == 'PendingDegauss') {
                //     if($data['NextStatus'] != 'Degauss' && $data['NextStatus'] != 'Degauss and Shreded') {
                //         $json['Success'] = false;
                //         $json['Result'] = 'Media SN current status is PendingDegauss, Next Status should be "Degauss" or "Degauss and Shreded"';
                //         return json_encode($json);
                //     }                    
                // }
                // if($row['Status'] == 'PendingShred') {
                //     if($data['NextStatus'] != 'Shreded') {
                //         $json['Success'] = false;
                //         $json['Result'] = 'Media SN current status is PendingShred, Next Status should be "Shreded"';
                //         return json_encode($json);
                //     }                    
                // }
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'SN Facility is not matching with user Facility';
                    return json_encode($json);
                }

                //Start validate destination bin capacity and customer lock
                // Use destination bin ID directly from input data
                if(isset($data['NewCustomPalletID']) && $data['NewCustomPalletID'] > 0) {
                    // Use customer ID directly from asset data
                    $assetCustomerID = isset($row['AWSCustomerID']) ? $row['AWSCustomerID'] : null;

                    $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                        $data['NewCustomPalletID'],
                        $assetCustomerID,
                        '', // Bin name will be retrieved from function
                        $_SESSION['user']['UserId'],
                        'Bulk Serial Destruction'
                    );

                    if (!$binValidation['Success']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                        return json_encode($json);
                    }
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid destination BIN ID';
                    return json_encode($json);
                }
                //End validate destination bin

                //Start insert into speed_bulk_media_process_details
                $query1 = "insert into speed_bulk_media_process_details (BulkMediaProcessID,AssetScanID,CreatedDate,CreatedBy,serial_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."',NOW(),'".$_SESSION['user']['UserId']."',NOW())";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $ID = mysqli_insert_id($this->connectionlink);
                //End insert into speed_bulk_media_process_details
                
                //Start get inserted details
                $query2 = "select dd.*,m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName from speed_bulk_media_process_details dd 
                    left join asset m on dd.AssetScanID= m.AssetScanID  
                    left join asset_status ss on m.StatusID = ss.StatusID 
                    left join disposition d on m.disposition_id = d.disposition_id 
                    left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                    left join users u on m.CreatedBy = u.UserId 
                    left join facility f on m.FacilityID = f.FacilityID
                    where dd.DetailID = '".mysqli_real_escape_string($this->connectionlink,$ID)."' 
                ";
                $q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row2 = mysqli_fetch_assoc($q2);
                    $json['Success'] = true;
                    $json['MediaDetails'] = $row2;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid';
                    return json_encode($json);
                }
                //End get inserted details


            } else {//Check if Server Recovery

              $query = "select m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName,f.FacilityID from speed_server_recovery m  
                left join asset_status ss on m.StatusID = ss.StatusID 
                left join disposition d on m.disposition_id = d.disposition_id 
                left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                left join users u on m.CreatedBy = u.UserId 
                left join pallets p on m.idPallet = p.idPallet   
                left join facility f on p.PalletFacilityID = f.FacilityID                 
                where m.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'
              ";
              $q = mysqli_query($this->connectionlink,$query);
              if(mysqli_error($this->connectionlink)) {
                  $json['Success'] = false;
                  $json['Result'] = mysqli_error($this->connectionlink);
                  return json_encode($json);
              }
              if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['CustomPalletID'] != $data['SourceCustomPalletID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Only Serial from '.$data['SourceBinID'].' location are allowed to scan';
                    return json_encode($json);
                }

                //Start check If media already added or not
                $query121 = "select * from speed_bulk_media_process_details where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."' and BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q121 = mysqli_query($this->connectionlink,$query121);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row121 = mysqli_fetch_assoc($q121);
                    $json['Success'] = false;
                    $json['Result'] = 'SN already scanned';
                    return json_encode($json);
                }
                //End check If media already added or not

                // if($row['MediaType'] != $data['MediaType']) {
                //     $json['Success'] = false;
                //     $json['Result'] = 'Media Type is not matching';
                //     return json_encode($json);    
                // }
                if($row['Status'] == 'Shreded') {
                    $json['Success'] = false;
                    $json['Result'] = 'SN already destroyed';
                    return json_encode($json);
                }
                // if($row['Status'] == 'PendingDegauss') {
                //     if($data['NextStatus'] != 'Degauss' && $data['NextStatus'] != 'Degauss and Shreded') {
                //         $json['Success'] = false;
                //         $json['Result'] = 'Media SN current status is PendingDegauss, Next Status should be "Degauss" or "Degauss and Shreded"';
                //         return json_encode($json);
                //     }                    
                // }
                // if($row['Status'] == 'PendingShred') {
                //     if($data['NextStatus'] != 'Shreded') {
                //         $json['Success'] = false;
                //         $json['Result'] = 'Media SN current status is PendingShred, Next Status should be "Shreded"';
                //         return json_encode($json);
                //     }                    
                // }
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'SN Facility is not matching with user Facility';
                    return json_encode($json);
                }

                //Start validate destination bin capacity and customer lock
                // Use destination bin ID directly from input data
                if(isset($data['NewCustomPalletID']) && $data['NewCustomPalletID'] > 0) {
                    // Use customer ID directly from server recovery data
                    $assetCustomerID = isset($row['AWSCustomerID']) ? $row['AWSCustomerID'] : null;

                    $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                        $data['NewCustomPalletID'],
                        $assetCustomerID,
                        '', // Bin name will be retrieved from function
                        $_SESSION['user']['UserId'],
                        'Bulk Serial Destruction'
                    );

                    if (!$binValidation['Success']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                        return json_encode($json);
                    }
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid destination BIN ID';
                    return json_encode($json);
                }
                //End validate destination bin

                //Start insert into speed_bulk_media_process_details
                $query1 = "insert into speed_bulk_media_process_details (BulkMediaProcessID,ServerID,CreatedDate,CreatedBy,serial_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."',NOW(),'".$_SESSION['user']['UserId']."',NOW())";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $ID = mysqli_insert_id($this->connectionlink);
                //End insert into speed_bulk_media_process_details
                
                //Start get inserted details
                $query2 = "select dd.*,m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName,m.ServerSerialNumber as SerialNumber,m.Type as part_type from speed_bulk_media_process_details dd 
                    left join speed_server_recovery m on dd.ServerID= m.ServerID  
                    left join asset_status ss on m.StatusID = ss.StatusID 
                    left join disposition d on m.disposition_id = d.disposition_id 
                    left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                    left join users u on m.CreatedBy = u.UserId 
                    left join pallets p on m.idPallet = p.idPallet   
                    left join facility f on p.PalletFacilityID = f.FacilityID
                    where dd.DetailID = '".mysqli_real_escape_string($this->connectionlink,$ID)."' 
                ";
                $q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row2 = mysqli_fetch_assoc($q2);
                    $json['Success'] = true;
                    $json['MediaDetails'] = $row2;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid';
                    return json_encode($json);
                }
                //End get inserted details




              } else {//check in media recovery

                $query = "select m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName,f.FacilityID,p.MaterialType from speed_media_recovery m  
                left join speed_status ss on m.StatusID = ss.StatusID 
                left join disposition d on m.disposition_id = d.disposition_id 
                left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                left join users u on m.CreatedBy = u.UserId 
                left join pallets p on m.idPallet = p.idPallet   
                left join facility f on p.PalletFacilityID = f.FacilityID 
                where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'
                ";
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //End get media details
                if(mysqli_affected_rows($this->connectionlink) > 0) {

                  $row = mysqli_fetch_assoc($q);
                  if($row['MaterialType'] == 'Media Rack') {
                    $json['Success'] = false;
                    $json['Result'] = 'SPEED Serials are not allowed';
                    return json_encode($json);
                  }

                  if($row['CustomPalletID'] != $data['SourceCustomPalletID']) {
                      $json['Success'] = false;
                      $json['Result'] = 'Only Serials from '.$data['SourceBinID'].' location are allowed to scan';
                      return json_encode($json);
                  }

                  //Start check If media already added or not
                  $query121 = "select * from speed_bulk_media_process_details where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' and BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                  $q121 = mysqli_query($this->connectionlink,$query121);
                  if(mysqli_error($this->connectionlink)) {
                      $json['Success'] = false;
                      $json['Result'] = mysqli_error($this->connectionlink);
                      return json_encode($json);
                  }
                  if(mysqli_affected_rows($this->connectionlink) > 0) {
                      $row121 = mysqli_fetch_assoc($q121);
                      $json['Success'] = false;
                      $json['Result'] = 'SN already scanned';
                      return json_encode($json);
                  }
                  //End check If media already added or not

                  // if($row['MediaType'] != $data['MediaType']) {
                  //     $json['Success'] = false;
                  //     $json['Result'] = 'Media Type is not matching';
                  //     return json_encode($json);    
                  // }
                  if($row['Status'] == 'Shreded') {
                      $json['Success'] = false;
                      $json['Result'] = 'SN already destroyed';
                      return json_encode($json);
                  }
                  // if($row['Status'] == 'PendingDegauss') {
                  //     if($data['NextStatus'] != 'Degauss' && $data['NextStatus'] != 'Degauss and Shreded') {
                  //         $json['Success'] = false;
                  //         $json['Result'] = 'Media SN current status is PendingDegauss, Next Status should be "Degauss" or "Degauss and Shreded"';
                  //         return json_encode($json);
                  //     }                    
                  // }
                  // if($row['Status'] == 'PendingShred') {
                  //     if($data['NextStatus'] != 'Shreded') {
                  //         $json['Success'] = false;
                  //         $json['Result'] = 'Media SN current status is PendingShred, Next Status should be "Shreded"';
                  //         return json_encode($json);
                  //     }                    
                  // }
                  if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                      $json['Success'] = false;
                      $json['Result'] = 'SN Facility is not matching with user Facility';
                      return json_encode($json);
                  }

                  //Start validate destination bin capacity and customer lock
                  // Use destination bin ID directly from input data
                  if(isset($data['NewCustomPalletID']) && $data['NewCustomPalletID'] > 0) {
                      // Use customer ID directly from media recovery data
                      $assetCustomerID = isset($row['AWSCustomerID']) ? $row['AWSCustomerID'] : null;

                      $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                          $data['NewCustomPalletID'],
                          $assetCustomerID,
                          '', // Bin name will be retrieved from function
                          $_SESSION['user']['UserId'],
                          'Bulk Serial Destruction'
                      );

                      if (!$binValidation['Success']) {
                          $json['Success'] = false;
                          $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                          return json_encode($json);
                      }
                  } else {
                      $json['Success'] = false;
                      $json['Result'] = 'Invalid destination BIN ID';
                      return json_encode($json);
                  }
                  //End validate destination bin

                  //Start insert into speed_bulk_media_process_details
                  $query1 = "insert into speed_bulk_media_process_details (BulkMediaProcessID,MediaID,CreatedDate,CreatedBy,serial_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."',NOW(),'".$_SESSION['user']['UserId']."',NOW())";
                  $q1 = mysqli_query($this->connectionlink,$query1);
                  if(mysqli_error($this->connectionlink)) {
                      $json['Success'] = false;
                      $json['Result'] = mysqli_error($this->connectionlink);
                      return json_encode($json);
                  }
                  $ID = mysqli_insert_id($this->connectionlink);
                  //End insert into speed_bulk_media_process_details
                  
                  //Start get inserted details              
                  $query2 = "select dd.*,m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName,m.MediaSerialNumber as SerialNumber,m.MediaType as part_type from speed_bulk_media_process_details dd 
                    left join speed_media_recovery m on dd.MediaID= m.MediaID  
                    left join speed_status ss on m.StatusID = ss.StatusID 
                    left join disposition d on m.disposition_id = d.disposition_id 
                    left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                    left join users u on m.CreatedBy = u.UserId 
                    left join pallets p on m.idPallet = p.idPallet   
                    left join facility f on p.PalletFacilityID = f.FacilityID
                    where dd.DetailID = '".mysqli_real_escape_string($this->connectionlink,$ID)."' ";
                  $q2 = mysqli_query($this->connectionlink,$query2);
                  if(mysqli_error($this->connectionlink)) {
                      $json['Success'] = false;
                      $json['Result'] = mysqli_error($this->connectionlink);
                      return json_encode($json);
                  }

                  if(mysqli_affected_rows($this->connectionlink) > 0) {
                      $row2 = mysqli_fetch_assoc($q2);
                      $json['Success'] = true;
                      $json['MediaDetails'] = $row2;
                      return json_encode($json);
                  } else {
                      $json['Success'] = false;
                      $json['Result'] = 'Invalid';
                      return json_encode($json);
                  }
                  //End get inserted details



                } else {
                  $json['Success'] = false;
                  $json['Result'] = 'SN not in ev';
                  return json_encode($json);
                }                
              }              
            }            
                                    
            $json['Success'] = true;
            $json['BulkMediaProcessID'] = $ID;
			      return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


  public function RemoveScannedSerial ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Serial Destruction Page';
				return json_encode($json);
			}

      if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
          $json['Success'] = false;
          $json['Result'] = 'You have Read only Access to Bulk Serial Destruction Page';
          return json_encode($json);
      }

			$query3 = "delete from speed_bulk_media_process_details where DetailID = '".mysqli_real_escape_string($this->connectionlink,$data['DetailID'])."' ";
      $q3 = mysqli_query($this->connectionlink,$query3);
      if(mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
      }                        
      $json['Success'] = true;
      $json['Result'] = 'SN Removed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


  public function ProcessBulkSerials ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Serial Destruction Page';
				return json_encode($json);
			}

      if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Serial Destruction')) {
          $json['Success'] = false;
          $json['Result'] = 'You have Read only Access to Bulk Serial Destruction Page';
          return json_encode($json);
      }

      //Start validate Audit Controller

        if($data['AuditController'] != $data['ShippingControllerLoginID']) {
          $json['Success'] = false;
				  $json['Result'] = 'Controller verified at the beginning of the process and the end should be same';
				  return json_encode($json);
        }

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['DestructionController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Destruction Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Destruction Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller


      if($data['NewCustomPalletID'] > 0) {

        if(empty($data['controller_scan_time'])){
          $serialScanTime = $this->GetDBCurrentTime();
          $serialScanTime = json_decode($serialScanTime,TRUE);
          $data['controller_scan_time'] = $serialScanTime['Result'];
        }

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);    
          $to_available = $to_cp['MaximumAssets'] - $to_cp['AssetsCount'];                
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);	
				}
				//End get to_cp_details
        $to_disposition_id = $to_cp['disposition_id'];
				if($data['NextStatus'] == 'Shreded') {
					$to_status = '12';
					$to_status_text = 'Shreded';
				} else if($data['NextStatus'] == 'Degauss') {
					$to_status = '2';
					$to_status_text = 'PendingShred';
				} else if($data['NextStatus'] == 'Degauss and Shreded') {
					$to_status = '3';
					$to_status_text = 'Shreded';
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				$to_CustomPalletID = $to_cp['CustomPalletID'];

      //Start get scanned serials
      if($data['BulkMediaProcessID'] > 0) {
          $query1 = "select d.*,m.*,cp.BinName,d.serial_scan_time as serial_scan_time1 from speed_bulk_media_process_details d 
          left join asset m on d.AssetScanID = m.AssetScanID 
          left join custompallet cp on m.CustomPalletID = cp.CustomPalletID 
          where d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'";


          $query1 = "SELECT 
              d.*, 
              m.*, 
              ss.ServerID as ss_ServerID, ss.idPallet as ss_idPallet, ss.ServerSerialNumber as ss_ServerSerialNumber, ss.Type as ss_Type, ss.MPN as ss_MPN, ss.disposition_id as ss_disposition_id, ss.CustomPalletID as ss_CustomPalletID, ss.StatusID as ss_StatusID,
              mm.MediaID as mm_MediaID,mm.idPallet as mm_idPallet,mm.ServerSerialNumber as mm_ServerSerialNumber,mm.MediaSerialNumber as mm_MediaSerialNumber,mm.MediaType as mm_MediaType,mm.MediaMPN as mm_MediaMPN,mm.disposition_id as mm_disposition_id,mm.CustomPalletID as mm_CustomPalletID,mm.StatusID as mm_StatusID,
              cp.BinName, 
              d.serial_scan_time AS serial_scan_time1 
          FROM 
              speed_bulk_media_process_details d
          LEFT JOIN 
              asset m ON d.AssetScanID = m.AssetScanID
          LEFT JOIN 
              speed_server_recovery ss ON d.ServerID = ss.ServerID 
          LEFT JOIN 
              speed_media_recovery mm ON d.MediaID = mm.MediaID
          LEFT JOIN 
              custompallet cp 
              ON (
                  (d.ServerID > 0 AND ss.CustomPalletID = cp.CustomPalletID) OR 
                  (d.AssetScanID > 0 AND m.CustomPalletID = cp.CustomPalletID) OR
                  (d.MediaID > 0 AND mm.CustomPalletID = cp.CustomPalletID) 
              )
          WHERE 
              d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'";


          $q1 = mysqli_query($this->connectionlink,$query1);
          if(mysqli_error($this->connectionlink)) {
              $json['Success'] = false;
              $json['Result'] = mysqli_error($this->connectionlink);
              return json_encode($json);
          }			
          if(mysqli_affected_rows($this->connectionlink) > 0) {
              $media_details = '';
              $media_details_cus = '';
              $scanned_media = array();
              $i = 0;
              while($row1 = mysqli_fetch_assoc($q1)) {                  
                  $scanned_media[$i] = $row1;
                  $i++;
              }
              $media_details = substr($media_details, 0, -1);
              $media_details_cus = substr($media_details_cus, 0, -1);
          } else {
              $json['Success'] = false;
              $json['Result'] = "No Media Scanned";
              return json_encode($json);	
          }
      } else {
          $json['Success'] = false;
					$json['Result'] = "Invalid Details";
					return json_encode($json);
      }
                //End get scanned serials                
                if($to_cp['MaxLimitRequired'] == '1') {
                  if($to_available < count($scanned_media)) {
                    $json['Success'] = false;
                    $json['Result'] = 'Next Bin Max limit will be exceeded ';
                    return json_encode($json);
                  }
                }                

                for($j=0;$j<count($scanned_media);$j++) {
                    if($scanned_media[$j]['AssetScanID'] > 0) {

                      //Start update Media SN
                      //$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                      $query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."',RecentDispositionDate = NOW(),RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Created in Bulk Serial Destruction' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['AssetScanID'])."'";
                      $q1 = mysqli_query($this->connectionlink,$query1);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }			
                      //End update Media SN   


                      //Start update Custom Pallet Items
                      $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['AssetScanID'])."'";
                      $q2 = mysqli_query($this->connectionlink,$query2);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Items

                      //Start update Custom Pallet Counts
                      $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                      $q3 = mysqli_query($this->connectionlink,$query3);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }

                      $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."'";
                      $q4 = mysqli_query($this->connectionlink,$query4);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Counts



                      //Start insert into tracking
                      $action = 'Serial Number ('.$scanned_media[$j]['SerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') in Bulk Serial Dstruction';
                      $action .= " - Controller: " . $data['AuditController'];
                      $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
                      $q6 = mysqli_query($this->connectionlink,$query6);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into tracking

                      //start insert into destruction history
                      $query7 = "insert into destruction_history(AssetScanID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['AssetScanID'])."','".$scanned_media[$j]['CustomPalletID']."','".$scanned_media[$j]['BinName']."','".$scanned_media[$j]['disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
                      ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['serial_scan_time1'])."',
                      '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Bulk Serial Destruction')";
                      $q7 = mysqli_query($this->connectionlink,$query7);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into destruction history 

                    } else if($scanned_media[$j]['ServerID'] > 0) {

                      //Start update Media SN                      
                      $query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerID'])."'";
                      $q1 = mysqli_query($this->connectionlink,$query1);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }			
                      //End update Media SN   


                      //Start update Custom Pallet Items
                      $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerID'])."'";
                      $q2 = mysqli_query($this->connectionlink,$query2);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Items

                      //Start update Custom Pallet Counts
                      $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                      $q3 = mysqli_query($this->connectionlink,$query3);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }

                      $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ss_CustomPalletID'])."'";
                      $q4 = mysqli_query($this->connectionlink,$query4);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Counts




                      //Start insert into tracking          
                      $action = 'Serial Number ('.$scanned_media[$j]['ss_ServerSerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') in Bulk Serial Dstruction';
                      $action .= " - Controller: " . $data['AuditController'];
                      $query6 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ss_ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ss_Type'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ss_idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','".$scanned_media[$j]['ss_ServerSerialNumber']."')";
                      $q6 = mysqli_query($this->connectionlink,$query6);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into tracking


                      //start insert into destruction history
                      $query7 = "insert into destruction_history(ServerID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerID'])."','".$scanned_media[$j]['ss_CustomPalletID']."','".$scanned_media[$j]['BinName']."','".$scanned_media[$j]['ss_disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
                      ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".mysqli_real_escape_string($this->connectionlink,$BulkMediaProcessID)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['serial_scan_time1'])."',
                      '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Bulk Serial Destruction')";
                      $q7 = mysqli_query($this->connectionlink,$query7);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into destruction history
                    } else if($scanned_media[$j]['MediaID'] > 0) {

                      //Start update Media SN                                           
                      $query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '3' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                      $q1 = mysqli_query($this->connectionlink,$query1);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }			
                      //End update Media SN   


                      //Start update Custom Pallet Items
                      $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                      $q2 = mysqli_query($this->connectionlink,$query2);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Items

                      //Start update Custom Pallet Counts
                      $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                      $q3 = mysqli_query($this->connectionlink,$query3);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }

                      $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['mm_CustomPalletID'])."'";
                      $q4 = mysqli_query($this->connectionlink,$query4);
                      if(mysqli_error($this->connectionlink)) {
                          $json['Success'] = false;
                          $json['Result'] = mysqli_error($this->connectionlink);
                          return json_encode($json);
                      }
                      //End update Custom Pallet Counts




                      //Start insert into tracking          
                      $action = 'Serial Number ('.$scanned_media[$j]['mm_MediaSerialNumber'].') is Destroyed and Added to BIN ('.$to_cp['BinName'].') in Bulk Serial Dstruction';                      
                      $action .= " - Controller: " . $data['AuditController'];
                      $query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['mm_MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['mm_MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['mm_ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['mm_idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
                      $q6 = mysqli_query($this->connectionlink,$query6);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into tracking 


                      //start insert into destruction history
                      $query7 = "insert into destruction_history(MediaID,FromCustomPalletID,FromBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,serial_scan_time,container_scan_time,controller_scan_time,FacilityID,ToCustomPalletID,ToBinName,ModuleName) values('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".$scanned_media[$j]['mm_CustomPalletID']."','".$scanned_media[$j]['BinName']."','".$scanned_media[$j]['mm_disposition_id']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',NOW()
                      ,'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['serial_scan_time1'])."',
                      '".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."','Bulk Serial Destruction')";
                      $q7 = mysqli_query($this->connectionlink,$query7);
                      if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                      }
                      //End insert into destruction history
                    }
                }    
                
               

                $query77 = "update speed_bulk_media_process set CompletedDate = NOW(),CompletedBy = '".$_SESSION['user']['UserId']."',ProcessCompledControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',Status = 'Completed' where BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q77 = mysqli_query($this->connectionlink,$query77);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //Start unlock source bin
                // $query5 = "update custompallet set LockedForBulkMediaDestruction = '0',LockedTime = NULL,LockedBy = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."'";
                // $q5 = mysqli_query($this->connectionlink,$query5);
                // if(mysqli_error($this->connectionlink)) {
                //     $json['Success'] = false;
                //     $json['Result'] = mysqli_error($this->connectionlink);
                //     return json_encode($json);
                // }
                // $query4 = "insert into custompallet_lock_history (CustomPalletID,LockedType,LockedPage,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."','UnLocked','Bulk Media Destruction',NOW(),'".$_SESSION['user']['UserId']."')";
                // $q4 = mysqli_query($this->connectionlink,$query4);
                // if(mysqli_error($this->connectionlink)) {
                //     $json['Success'] = false;
                //     $json['Result'] = mysqli_error($this->connectionlink);
                //     return json_encode($json);
                // }
                //End unlock source bin
                $json['Success'] = true;
                $json['Result'] = "Bulk Destruction Process Completed for ".$i." Records";
                return json_encode($json);

            } else {
                $json['Success'] = false;
                $json['Result'] = "Invalid Next BIN ID";
                return json_encode($json);
            }
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>
