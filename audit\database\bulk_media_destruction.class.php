
<?php
session_start();
include_once("audit.class.php");
class bulkMediaClass extends AuditClass {
    
    public function StartBulkMediaProcess ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);        
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Media Destruction Page';
                return json_encode($json);
            }

            //Start validate source bin
            $query = "select c.*,d.ssd_disposition,d.hdd_disposition from custompallet c 
            left join disposition d on c.disposition_id = d.disposition_id 
            where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                //where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.ssd_disposition = 1 or d.hdd_disposition = 1) and c.LockedForBulkMediaDestruction = 0 order by c.BinName ";
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Source BIN Facility is different from User Facility';
                    return json_encode($json);    
                }

                if($row['StatusID'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'Source BIN Status is not Active';
                    return json_encode($json);    
                }

                if($row['LockedForBulkMediaDestruction'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'Source BIN is Locked';
                    return json_encode($json);    
                }

                if($row['ssd_disposition'] != '1' && $row['hdd_disposition'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'BIN disposition is not meant for Media';
                    return json_encode($json);    
                }
                $data['SourceBinID'] = $row['CustomPalletID'];
                $json['SourceCustomPalletID'] = $row['CustomPalletID'];
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Source BIN';
                return json_encode($json);
            }
            //End validate source bin

            //Start validate destination bin (NewCustomPalletID)
            if($data['NewCustomPalletID'] > 0) {
                // Note: Customer validation not needed here as we don't have specific media records yet
                // Only validate capacity and basic bin properties
                $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                    $data['NewCustomPalletID'],
                    null, // No customer ID needed at this stage
                    '', // Bin name will be retrieved from function
                    $_SESSION['user']['UserId'],
                    'Bulk Media Destruction'
                );

                if (!$binValidation['Success']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Destination BIN ID';
                return json_encode($json);
            }
            //End validate destination bin

            //Start validate destruction bin if applicable
            if($data['NextStatus'] == 'Degauss and Shreded' && isset($data['DestructionCustomPalletID']) && $data['DestructionCustomPalletID'] > 0) {
                $destructionBinValidation = $this->ValidateBinCapacityAndCustomerLock(
                    $data['DestructionCustomPalletID'],
                    null, // No customer ID needed at this stage
                    '', // Bin name will be retrieved from function
                    $_SESSION['user']['UserId'],
                    'Bulk Media Destruction'
                );

                if (!$destructionBinValidation['Success']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Destruction BIN validation failed: ' . $destructionBinValidation['Result'];
                    return json_encode($json);
                }
            }
            //End validate destruction bin

			$query3 = "insert into speed_bulk_media_process (MediaType,NextStatus,CreatedDate,CreatedBy,NewCustomPalletID,ControllerLoginID,FacilityID,SourceBinID";
            if($data['NextStatus'] == 'Degauss and Shreded') {
                $query3 = $query3.",DestructionCustomPalletID";
            }
            $query3 = $query3.") values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['NextStatus'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            if($data['NextStatus'] == 'Degauss and Shreded'){
                $query3 = $query3.",'".mysqli_real_escape_string($this->connectionlink,$data['DestructionCustomPalletID'])."'";
            }
            $query3 = $query3.")";
            $q3 = mysqli_query($this->connectionlink,$query3);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }            
            $ID = mysqli_insert_id($this->connectionlink);

            //Start lock bin
            $query4 = "insert into custompallet_lock_history (CustomPalletID,LockedType,LockedPage,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."','Locked','Bulk Media Destruction',NOW(),'".$_SESSION['user']['UserId']."')";
            $q4 = mysqli_query($this->connectionlink,$query4);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            $query5 = "update custompallet set LockedForBulkMediaDestruction = '1',LockedTime = NOW(),LockedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            $q5 = mysqli_query($this->connectionlink,$query5);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End lock bin

            $json['Success'] = true;
            $json['BulkMediaProcessID'] = $ID;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function AddMediaToBulkList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Media Destruction Page';
                return json_encode($json);
            }

            //Start get media details
            $query = "select m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName from speed_media_recovery m  
                left join speed_status ss on m.StatusID = ss.StatusID 
                left join disposition d on m.disposition_id = d.disposition_id 
                left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                left join users u on m.CreatedBy = u.UserId 
                left join facility f on m.FacilityID = f.FacilityID 
                where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'
            ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End get media details
			if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);

                if($row['CustomPalletID'] != $data['SourceCustomPalletID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Only Media from '.$row['BinName'].' location are allowed to scan';
                    return json_encode($json);
                }

                //Start check If media already added or not
                $query121 = "select * from speed_bulk_media_process_details where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."' and BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q121 = mysqli_query($this->connectionlink,$query121);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row121 = mysqli_fetch_assoc($q121);
                    $json['Success'] = false;
                    $json['Result'] = 'Media SN already scanned';
                    return json_encode($json);
                }
                //End check If media already added or not

                if($row['MediaType'] != $data['MediaType']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Media Type is not matching';
                    return json_encode($json);    
                }
                if($row['Status'] == 'Shreded') {
                    $json['Success'] = false;
                    $json['Result'] = 'Media SN already destroyed';
                    return json_encode($json);
                }
                if($row['Status'] == 'PendingDegauss') {
                    if($data['NextStatus'] != 'Degauss' && $data['NextStatus'] != 'Degauss and Shreded') {
                        $json['Success'] = false;
                        $json['Result'] = 'Media SN current status is PendingDegauss, Next Status should be "Degauss" or "Degauss and Shreded"';
                        return json_encode($json);
                    }                    
                }
                if($row['Status'] == 'PendingShred') {
                    if($data['NextStatus'] != 'Shreded') {
                        $json['Success'] = false;
                        $json['Result'] = 'Media SN current status is PendingShred, Next Status should be "Shreded"';
                        return json_encode($json);
                    }                    
                }
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Media SN Facility is not matching with user Facility';
                    return json_encode($json);
                }

                //Start insert into speed_bulk_media_process_details
                $query1 = "insert into speed_bulk_media_process_details (BulkMediaProcessID,MediaID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $ID = mysqli_insert_id($this->connectionlink);
                //End insert into speed_bulk_media_process_details
                
                //Start get inserted details
                $query2 = "select dd.*,m.*,ss.Status,d.disposition,c.BinName,u.UserName,f.FacilityName from speed_bulk_media_process_details dd 
                    left join speed_media_recovery m on dd.MediaID= m.MediaID  
                    left join speed_status ss on m.StatusID = ss.StatusID 
                    left join disposition d on m.disposition_id = d.disposition_id 
                    left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                    left join users u on m.CreatedBy = u.UserId 
                    left join facility f on m.FacilityID = f.FacilityID
                    where dd.DetailID = '".mysqli_real_escape_string($this->connectionlink,$ID)."' 
                ";
                $q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row2 = mysqli_fetch_assoc($q2);
                    $json['Success'] = true;
                    $json['MediaDetails'] = $row2;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid';
                    return json_encode($json);
                }
                //End get inserted details


            } else {
                $json['Success'] = false;
                $json['Result'] = 'Media SN not in ev';
                return json_encode($json);
            }
                                    
            $json['Success'] = true;
            $json['BulkMediaProcessID'] = $ID;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function RemoveScannedMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Media Destruction Page';
                return json_encode($json);
            }

			$query3 = "delete from speed_bulk_media_process_details where DetailID = '".mysqli_real_escape_string($this->connectionlink,$data['DetailID'])."' ";
            $q3 = mysqli_query($this->connectionlink,$query3);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }                        
            $json['Success'] = true;
            $json['Result'] = 'Media SN Removed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function ProcessBulkMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Media Destruction Page';
                return json_encode($json);
            }

            //Start validate Audit Controller

            if($data['AuditController'] != $data['ShippingControllerLoginID']) {
                $json['Success'] = false;
				$json['Result'] = 'Controller verified at the beginning of the process and the end should be same';
				return json_encode($json);
            }

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['PendingMediaController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Pending Media Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pending Media Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

            if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
                    if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
						$json['Success'] = false;
						$json['Result'] = "Mobility Name is not configured for Next BIN ID";
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);	
				}
				//End get to_cp_details

                //Start validate destination bin capacity and customer lock
                // Get media details for customer validation
                $mediaCustomerQuery = "SELECT DISTINCT m.AWSCustomerID
                                      FROM speed_bulk_media_process_details d
                                      LEFT JOIN speed_media_recovery m ON d.MediaID = m.MediaID
                                      WHERE d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'
                                      AND m.AWSCustomerID IS NOT NULL AND m.AWSCustomerID != ''";
                $mediaCustomerResult = mysqli_query($this->connectionlink, $mediaCustomerQuery);
                $mediaCustomerIDs = array();
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                while($customerRow = mysqli_fetch_assoc($mediaCustomerResult)) {
                    $mediaCustomerIDs[] = $customerRow['AWSCustomerID'];
                }

                // Validate bin for each unique customer (if customer lock is enabled)
                if(!empty($mediaCustomerIDs)) {
                    foreach(array_unique($mediaCustomerIDs) as $customerID) {
                        $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                            $data['NewCustomPalletID'],
                            $customerID,
                            $to_cp['BinName'],
                            $_SESSION['user']['UserId'],
                            'Bulk Media Destruction'
                        );

                        if (!$binValidation['Success']) {
                            $json['Success'] = false;
                            $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                            return json_encode($json);
                        }
                    }
                } else {
                    // No customer IDs found, validate basic bin properties only
                    $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                        $data['NewCustomPalletID'],
                        null,
                        $to_cp['BinName'],
                        $_SESSION['user']['UserId'],
                        'Bulk Media Destruction'
                    );

                    if (!$binValidation['Success']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Destination BIN validation failed: ' . $binValidation['Result'];
                        return json_encode($json);
                    }
                }
                //End validate destination bin

                $to_disposition_id = $to_cp['disposition_id'];
				if($data['NextStatus'] == 'Shreded') {
					$to_status = '3';
					$to_status_text = 'Shreded';
				} else if($data['NextStatus'] == 'Degauss') {
					$to_status = '2';
					$to_status_text = 'PendingShred';
				} else if($data['NextStatus'] == 'Degauss and Shreded') {
					$to_status = '3';
					$to_status_text = 'Shreded';
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				$to_CustomPalletID = $to_cp['CustomPalletID'];

                //Start get scanned serials
                if($data['BulkMediaProcessID'] > 0) {
                    $query1 = "select d.*,m.* from speed_bulk_media_process_details d 
                    left join speed_media_recovery m on d.MediaID = m.MediaID where d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $media_details = '';
                        $media_details_cus = '';
                        $scanned_media = array();
                        $i = 0;
                        while($row1 = mysqli_fetch_assoc($q1)) {
                            $from_custompallet_id = $row1['CustomPalletID'];
                            $media_details = $media_details . '{
                            "serial": "'.$row1['MediaSerialNumber'].'",
                            "type": "'.$row1['MediaType'].'",
                            "binId": "'.$to_cp['MobilityName'].'",
                            "timestamp": "'.time().'"
                            },';

                            $media_details_cus = $media_details_cus . '{
                            "serial": "'.$row1['MediaSerialNumber'].'",
                            "type": "'.$row1['MediaType'].'",                            
                            "timestamp": "'.time().'"
                            },';

                            $scanned_media[$i] = $row1;
                            $i++;
                        }
                        $media_details = substr($media_details, 0, -1);
                        $media_details_cus = substr($media_details_cus, 0, -1);
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = "No Media Scanned";
                        return json_encode($json);	
                    }
                } else {
                    $json['Success'] = false;
					$json['Result'] = "Invalid Details";
					return json_encode($json);
                }
                //End get scanned serials

                //Start send in custody sns
                $message_cus = '
                {
                    "eventType": "BATCH_MEDIA_DRIVES_IN_CUSTODY",
                    "data": {
                    "site": "'.$_SESSION['user']['FacilityName'].'",
                    "login": "'.$_SESSION['user']['UserName'].'",
                    "media": [
                        '.$media_details_cus.'
                    ]
                    }
                }';
                $event_type_cus = "BATCH_MEDIA_DRIVES_IN_CUSTODY";

                //Start check from cp type
                $query233 = "select * from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_custompallet_id)."'";
                $q233 = mysqli_query($this->connectionlink,$query233);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }			
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row233 = mysqli_fetch_assoc($q233);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Bin';
                    return json_encode($json);
                }
                
                if($row233['BinType'] == 'Physical') {
                    $SNS_Message_cus = $this->SendSNSMessage($message_cus,NULL,$event_type_cus,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
                    if($SNS_Message_cus['Success'] != true) {
                        $json['Success'] = false;
                        $json['Result'] = 'Custody SNS Message Failed, Holding on Processing Bulk Media';
                        return json_encode($json);	
                    } 
                    if($SNS_Message_cus['id'] > 0) {
                        $query19 = "select * from speed_sns_messages where id = '".$SNS_Message_cus['id']."' ";
                        $q19 = mysqli_query($this->connectionlink,$query19);				
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $sns_cus = mysqli_fetch_assoc($q19);
                        }
                    }
                }                

                //End send in custody sns

                if($data['NextStatus'] == 'Shreded') {
                    $message = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DESTROYED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							'.$media_details.'
                        ]
						}
					}';
					$event_type = "BATCH_MEDIA_DRIVES_DESTROYED";

                    $message_degauss = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							'.$media_details.'
                        ]
						}
					}';
					$event_type_degauss = "BATCH_MEDIA_DRIVES_DEGAUSSED";

                } else if($data['NextStatus'] == 'Degauss') {

                    $message = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							'.$media_details.'
                        ]
						}
					}';
					$event_type = "BATCH_MEDIA_DRIVES_DEGAUSSED";

                } else if($data['NextStatus'] == 'Degauss and Shreded') {

                    $message = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							'.$media_details.'
                        ]
						}
					}';
					$event_type = "BATCH_MEDIA_DRIVES_DEGAUSSED";


                    $message1 = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DESTROYED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							'.$media_details.'
                        ]
						}
					}';
					$event_type1 = "BATCH_MEDIA_DRIVES_DESTROYED";

                } else {
                    $json['Success'] = false;
					$json['Result'] = "Invalid Next Status";
					return json_encode($json);
                }
                if($row233['BinType'] == 'Physical') {
                    sleep(2);
                }
                if($data['MediaType'] == 'HDD' && $data['NextStatus'] == 'Shreded') {// This is not required, ticket 1985

                    // $SNS_Message_Degauss = $this->SendSNSMessage($message_degauss,NULL,$event_type_degauss,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
                    // if($SNS_Message_Degauss['Success'] != true) {
                    //     $json['Success'] = false;
                    //     $json['Result'] = 'Degauss SNS Message Failed, Holding on Processing Bulk Media';
                    //     return json_encode($json);	
                    // }
                    // if($SNS_Message_Degauss['id'] > 0) {
                    //     $query19 = "select * from speed_sns_messages where id = '".$SNS_Message_Degauss['id']."' ";
                    //     $q19 = mysqli_query($this->connectionlink,$query19);				
                    //     if(mysqli_affected_rows($this->connectionlink) > 0) {
                    //         $sns_degauss = mysqli_fetch_assoc($q19);
                    //     }
                    // }
                    // sleep(2);
                }
                $SNS_Message = $this->SendSNSMessage($message,NULL,$event_type,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
				if($SNS_Message['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'SNS Message Failed, Holding on Processing Bulk Media';
					return json_encode($json);	
				} 
                if($SNS_Message['id'] > 0) {
					$query19 = "select * from speed_sns_messages where id = '".$SNS_Message['id']."' ";
					$q19 = mysqli_query($this->connectionlink,$query19);				
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$sns = mysqli_fetch_assoc($q19);
					}
				}

                if($data['NextStatus'] == 'Degauss and Shreded') {
                    sleep(2);
                    $SNS_Message1 = $this->SendSNSMessage($message1,NULL,$event_type1,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
                    if($SNS_Message1['Success'] != true) {
                        $json['Success'] = false;
                        $json['Result'] = 'SNS Message Failed, Holding on Processing Bulk Media';
                        return json_encode($json);	
                    } 
                    if($SNS_Message1['id'] > 0) {
                        $query191 = "select * from speed_sns_messages where id = '".$SNS_Message1['id']."' ";
                        $q191 = mysqli_query($this->connectionlink,$query191);
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $sns1 = mysqli_fetch_assoc($q191);
                        }
                    }  
                }

                for($j=0;$j<count($scanned_media);$j++) {
                    //Start update Media SN
                    $query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End update Media SN   


                    //Start update Custom Pallet Items
                    $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q2 = mysqli_query($this->connectionlink,$query2);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Items

                    //Start update Custom Pallet Counts
                    $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                    $q3 = mysqli_query($this->connectionlink,$query3);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."'";
                    $q4 = mysqli_query($this->connectionlink,$query4);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Counts

                    //Start insert into media process
                    $scanned_media[$j]['OriSerialNumber'] = $scanned_media[$j]['ServerSerialNumber'];
                    $scanned_media[$j]['ServerSerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $scanned_media[$j]['ServerSerialNumber']);
                    $query5 = "insert into speed_media_process (MediaID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,from_disposition_id,to_disposition_id,from_status,to_status,from_CustomPalletID,to_CustomPalletID,AuditControllerID,CreatedDate,CreatedBy,ProcessType,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['StatusID'])."','".mysqli_real_escape_string($this->connectionlink,$to_status)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','".$scanned_media[$j]['OriSerialNumber']."')";
                    $q5 = mysqli_query($this->connectionlink,$query5);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink).$query5;
                        return json_encode($json);
                    }
                    //End insert into media process

                    //Start enter media tracking	
                    //$action = $scanned_media[$j]['MediaType']." Processed manually in Pending Media screen, moved from BIN (".$from_cp['BinName'].") to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $action = $scanned_media[$j]['MediaType']." Processed manually in Bulk Media Destruction screen, moved to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $action .= " - Controller: " . $data['AuditController'];	
                    $query66 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
                    $q66 = mysqli_query($this->connectionlink,$query66);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End enter media tracking

                    if($sns['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}

                    if($sns1['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns1['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}

                    if($sns_cus['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns_cus['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}

                    if($sns_degauss['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns_degauss['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns_degauss['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}
                }    
                
                if($SNS_Message['id'] > 0 && $sns['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}
                if($SNS_Message1['id'] > 0 && $sns1['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns1['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}

                if($SNS_Message_cus['id'] > 0 && $sns_cus['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns_cus['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}

                if($SNS_Message_Degauss['id'] > 0 && $sns_degauss['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns_degauss['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}

                $query77 = "update speed_bulk_media_process set CompletedDate = NOW(),CompletedBy = '".$_SESSION['user']['UserId']."',ProcessCompledControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',Status = 'Completed' where BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q77 = mysqli_query($this->connectionlink,$query77);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //Start unlock source bin
                $query5 = "update custompallet set LockedForBulkMediaDestruction = '0',LockedTime = NULL,LockedBy = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."'";
                $q5 = mysqli_query($this->connectionlink,$query5);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $query4 = "insert into custompallet_lock_history (CustomPalletID,LockedType,LockedPage,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."','UnLocked','Bulk Media Destruction',NOW(),'".$_SESSION['user']['UserId']."')";
                $q4 = mysqli_query($this->connectionlink,$query4);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //End unlock source bin
                $json['Success'] = true;
				$json['Result'] = "Bulk Media Process Completed for ".$i." Records";
				return json_encode($json);

            } else {
                $json['Success'] = false;
				$json['Result'] = "Invalid Next BIN ID";
				return json_encode($json);
            }
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ProcessBulkMediaDegaussAndShred ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Bulk Media Destruction Page';
                return json_encode($json);
            }

            //Start validate Audit Controller

            if($data['AuditController'] != $data['ShippingControllerLoginID']) {
                $json['Success'] = false;
				$json['Result'] = 'Controller verified at the beginning of the process and the end should be same';
				return json_encode($json);
            }

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['PendingMediaController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Pending Media Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pending Media Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

            if($data['NewCustomPalletID'] > 0) { //Start Degauss

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
                    if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
						$json['Success'] = false;
						$json['Result'] = "Mobility Name is not configured for Degauss BIN";
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Degauss BIN";
					return json_encode($json);	
				}
				//End get to_cp_details

                //Start validate destination bin capacity and customer lock for degauss
                // Get media details for customer validation
                $mediaCustomerQuery = "SELECT DISTINCT m.AWSCustomerID
                                      FROM speed_bulk_media_process_details d
                                      LEFT JOIN speed_media_recovery m ON d.MediaID = m.MediaID
                                      WHERE d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'
                                      AND m.AWSCustomerID IS NOT NULL AND m.AWSCustomerID != ''";
                $mediaCustomerResult = mysqli_query($this->connectionlink, $mediaCustomerQuery);
                $mediaCustomerIDs = array();
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                while($customerRow = mysqli_fetch_assoc($mediaCustomerResult)) {
                    $mediaCustomerIDs[] = $customerRow['AWSCustomerID'];
                }

                // Validate degauss bin for each unique customer (if customer lock is enabled)
                if(!empty($mediaCustomerIDs)) {
                    foreach(array_unique($mediaCustomerIDs) as $customerID) {
                        $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                            $data['NewCustomPalletID'],
                            $customerID,
                            $to_cp['BinName'],
                            $_SESSION['user']['UserId'],
                            'Bulk Media Destruction'
                        );

                        if (!$binValidation['Success']) {
                            $json['Success'] = false;
                            $json['Result'] = 'Degauss BIN validation failed: ' . $binValidation['Result'];
                            return json_encode($json);
                        }
                    }
                } else {
                    // No customer IDs found, validate basic bin properties only
                    $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                        $data['NewCustomPalletID'],
                        null,
                        $to_cp['BinName'],
                        $_SESSION['user']['UserId'],
                        'Bulk Media Destruction'
                    );

                    if (!$binValidation['Success']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Degauss BIN validation failed: ' . $binValidation['Result'];
                        return json_encode($json);
                    }
                }
                //End validate degauss bin

                $to_disposition_id = $to_cp['disposition_id'];
				$to_status = '2';
				$to_status_text = 'PendingShred';
				$to_CustomPalletID = $to_cp['CustomPalletID'];

                //Start get scanned serials
                if($data['BulkMediaProcessID'] > 0) {
                    $query1 = "select d.*,m.* from speed_bulk_media_process_details d 
                    left join speed_media_recovery m on d.MediaID = m.MediaID where d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $media_details = '';
                        $media_details_cus = '';
                        $scanned_media = array();
                        $i = 0;
                        while($row1 = mysqli_fetch_assoc($q1)) {
                            $media_details = $media_details . '{
                            "serial": "'.$row1['MediaSerialNumber'].'",
                            "type": "'.$row1['MediaType'].'",
                            "binId": "'.$to_cp['MobilityName'].'",
                            "timestamp": "'.time().'"
                            },';

                            $media_details_cus = $media_details_cus . '{
                            "serial": "'.$row1['MediaSerialNumber'].'",
                            "type": "'.$row1['MediaType'].'",                            
                            "timestamp": "'.time().'"
                            },';

                            $scanned_media[$i] = $row1;
                            $i++;
                        }
                        $media_details = substr($media_details, 0, -1);
                        $media_details_cus = substr($media_details_cus, 0, -1);
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = "No Media Scanned";
                        return json_encode($json);	
                    }
                } else {
                    $json['Success'] = false;
					$json['Result'] = "Invalid Details";
					return json_encode($json);
                }
                //End get scanned serials

                //Start send in custody sns
                $message_cus = '
                {
                    "eventType": "BATCH_MEDIA_DRIVES_IN_CUSTODY",
                    "data": {
                    "site": "'.$_SESSION['user']['FacilityName'].'",
                    "login": "'.$_SESSION['user']['UserName'].'",
                    "media": [
                        '.$media_details_cus.'
                    ]
                    }
                }';
                $event_type_cus = "BATCH_MEDIA_DRIVES_IN_CUSTODY";

                $SNS_Message_cus = $this->SendSNSMessage($message_cus,NULL,$event_type_cus,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
				if($SNS_Message_cus['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Bulk Media';
					return json_encode($json);	
				} 
                if($SNS_Message_cus['id'] > 0) {
					$query19 = "select * from speed_sns_messages where id = '".$SNS_Message_cus['id']."' ";
					$q19 = mysqli_query($this->connectionlink,$query19);				
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$sns_cus = mysqli_fetch_assoc($q19);
					}
				}

                //End send in custody sns

                $message = '
                {
                    "eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
                    "data": {
                    "site": "'.$_SESSION['user']['FacilityName'].'",
                    "login": "'.$_SESSION['user']['UserName'].'",
                    "media": [
                        '.$media_details.'
                    ]
                    }
                }';
                $event_type = "BATCH_MEDIA_DRIVES_DEGAUSSED";

                $SNS_Message = $this->SendSNSMessage($message,NULL,$event_type,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
				if($SNS_Message['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'SNS Message Failed, Holding on Processing Bulk Media';
					return json_encode($json);	
				} 
                if($SNS_Message['id'] > 0) {
					$query19 = "select * from speed_sns_messages where id = '".$SNS_Message['id']."' ";
					$q19 = mysqli_query($this->connectionlink,$query19);				
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$sns = mysqli_fetch_assoc($q19);
					}
				}

                
                for($j=0;$j<count($scanned_media);$j++) {
                    //Start update Media SN
                    $query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End update Media SN   


                    //Start update Custom Pallet Items
                    $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q2 = mysqli_query($this->connectionlink,$query2);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Items

                    //Start update Custom Pallet Counts
                    $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                    $q3 = mysqli_query($this->connectionlink,$query3);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."'";
                    $q4 = mysqli_query($this->connectionlink,$query4);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Counts

                    //Start insert into media process
                    $scanned_media[$j]['OriSerialNumber'] = $scanned_media[$j]['ServerSerialNumber'];
                    $scanned_media[$j]['ServerSerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $scanned_media[$j]['ServerSerialNumber']);
                    $query5 = "insert into speed_media_process (MediaID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,from_disposition_id,to_disposition_id,from_status,to_status,from_CustomPalletID,to_CustomPalletID,AuditControllerID,CreatedDate,CreatedBy,ProcessType,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['StatusID'])."','".mysqli_real_escape_string($this->connectionlink,$to_status)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','".$scanned_media[$j]['OriSerialNumber']."')";
                    $q5 = mysqli_query($this->connectionlink,$query5);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink).$query5;
                        return json_encode($json);
                    }
                    //End insert into media process

                    //Start enter media tracking	
                    //$action = $scanned_media[$j]['MediaType']." Processed manually in Pending Media screen, moved from BIN (".$from_cp['BinName'].") to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $action = $scanned_media[$j]['MediaType']." Processed manually in Bulk Media Destruction screen, moved to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $query66 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
                    $q66 = mysqli_query($this->connectionlink,$query66);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End enter media tracking

                    if($sns['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}

                    if($sns_cus['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns_cus['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns_cus['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}
                    
                }    
                
                if($SNS_Message['id'] > 0 && $sns['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}    
                
                if($SNS_Message_cus['id'] > 0 && $sns_cus['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns_cus['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}
				$json['Degauss'] = "1";				

            } else {
                $json['Success'] = false;
				$json['Result'] = "Invalid Degauss BIN";
				return json_encode($json);
            }

            
            if($data['DestructionCustomPalletID'] > 0) {//Start Destruction

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['DestructionCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
                    if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
						$json['Success'] = false;
						$json['Result'] = "Mobility Name is not configured for Destruction BIN";
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Destruction BIN";
					return json_encode($json);	
				}
				//End get to_cp_details

                //Start validate destruction bin capacity and customer lock
                // Get media details for customer validation
                $mediaCustomerQuery = "SELECT DISTINCT m.AWSCustomerID
                                      FROM speed_bulk_media_process_details d
                                      LEFT JOIN speed_media_recovery m ON d.MediaID = m.MediaID
                                      WHERE d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'
                                      AND m.AWSCustomerID IS NOT NULL AND m.AWSCustomerID != ''";
                $mediaCustomerResult = mysqli_query($this->connectionlink, $mediaCustomerQuery);
                $mediaCustomerIDs = array();
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                while($customerRow = mysqli_fetch_assoc($mediaCustomerResult)) {
                    $mediaCustomerIDs[] = $customerRow['AWSCustomerID'];
                }

                // Validate destruction bin for each unique customer (if customer lock is enabled)
                if(!empty($mediaCustomerIDs)) {
                    foreach(array_unique($mediaCustomerIDs) as $customerID) {
                        $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                            $data['DestructionCustomPalletID'],
                            $customerID,
                            $to_cp['BinName'],
                            $_SESSION['user']['UserId'],
                            'Bulk Media Destruction'
                        );

                        if (!$binValidation['Success']) {
                            $json['Success'] = false;
                            $json['Result'] = 'Destruction BIN validation failed: ' . $binValidation['Result'];
                            return json_encode($json);
                        }
                    }
                } else {
                    // No customer IDs found, validate basic bin properties only
                    $binValidation = $this->ValidateBinCapacityAndCustomerLock(
                        $data['DestructionCustomPalletID'],
                        null,
                        $to_cp['BinName'],
                        $_SESSION['user']['UserId'],
                        'Bulk Media Destruction'
                    );

                    if (!$binValidation['Success']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Destruction BIN validation failed: ' . $binValidation['Result'];
                        return json_encode($json);
                    }
                }
                //End validate destruction bin

                $to_disposition_id = $to_cp['disposition_id'];
				$to_status = '3';
				$to_status_text = 'Shreded';
				$to_CustomPalletID = $to_cp['CustomPalletID'];


                
                //Start get scanned serials
                if($data['BulkMediaProcessID'] > 0) {
                    $query1 = "select d.*,m.* from speed_bulk_media_process_details d 
                    left join speed_media_recovery m on d.MediaID = m.MediaID where d.BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $media_details = '';
                        $scanned_media = array();
                        $i = 0;
                        while($row1 = mysqli_fetch_assoc($q1)) {
                            $media_details = $media_details . '{
                            "serial": "'.$row1['MediaSerialNumber'].'",
                            "type": "'.$row1['MediaType'].'",
                            "binId": "'.$to_cp['MobilityName'].'",
                            "timestamp": "'.time().'"
                            },';
                            $scanned_media[$i] = $row1;
                            $i++;
                        }
                        $media_details = substr($media_details, 0, -1);
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = "No Media Scanned";
                        return json_encode($json);	
                    }
                } else {
                    $json['Success'] = false;
					$json['Result'] = "Invalid Details";
					return json_encode($json);
                }
                //End get scanned serials

                $message1 = '
                {
                    "eventType": "BATCH_MEDIA_DRIVES_DESTROYED",
                    "data": {
                    "site": "'.$_SESSION['user']['FacilityName'].'",
                    "login": "'.$_SESSION['user']['UserName'].'",
                    "media": [
                        '.$media_details.'
                    ]
                    }
                }';
                $event_type1 = "BATCH_MEDIA_DRIVES_DESTROYED";

                sleep(2);
                $SNS_Message1 = $this->SendSNSMessage($message1,NULL,$event_type1,'BULKMEDIA',$data['MediaType'],$to_cp['BinName'],NULL,NULL,NULL);
                if($SNS_Message1['Success'] != true) {
                    $json['Success'] = false;
                    $json['Result'] = 'SNS Message Failed, Holding on Processing Bulk Media';
                    return json_encode($json);	
                } 
                if($SNS_Message1['id'] > 0) {
                    $query191 = "select * from speed_sns_messages where id = '".$SNS_Message1['id']."' ";
                    $q191 = mysqli_query($this->connectionlink,$query191);
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $sns1 = mysqli_fetch_assoc($q191);
                    }
                } 

                for($j=0;$j<count($scanned_media);$j++) {
                    //Start update Media SN
                    $query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End update Media SN   


                    //Start update Custom Pallet Items
                    $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."'";
                    $q2 = mysqli_query($this->connectionlink,$query2);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Items

                    //Start update Custom Pallet Counts
                    $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                    $q3 = mysqli_query($this->connectionlink,$query3);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."'";
                    $q4 = mysqli_query($this->connectionlink,$query4);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update Custom Pallet Counts

                    //Start insert into media process
                    $scanned_media[$j]['OriSerialNumber'] = $scanned_media[$j]['ServerSerialNumber'];
                    $scanned_media[$j]['ServerSerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $scanned_media[$j]['ServerSerialNumber']);
                    $query5 = "insert into speed_media_process (MediaID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,from_disposition_id,to_disposition_id,from_status,to_status,from_CustomPalletID,to_CustomPalletID,AuditControllerID,CreatedDate,CreatedBy,ProcessType,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['StatusID'])."','".mysqli_real_escape_string($this->connectionlink,$to_status)."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','".$scanned_media[$j]['OriSerialNumber']."')";
                    $q5 = mysqli_query($this->connectionlink,$query5);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink).$query5;
                        return json_encode($json);
                    }
                    //End insert into media process

                    //Start enter media tracking	
                    //$action = $scanned_media[$j]['MediaType']." Processed manually in Pending Media screen, moved from BIN (".$from_cp['BinName'].") to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $action = $scanned_media[$j]['MediaType']." Processed manually in Bulk Media Destruction screen, moved to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
                    $query66 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
                    $q66 = mysqli_query($this->connectionlink,$query66);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }			
                    //End enter media tracking

                    if($sns1['id']) {
						$query68 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet,ServerSerialNumber,MediaID,MediaSerialNumber,MediaType,NextBinID) values ('".mysqli_real_escape_string($this->connectionlink,$sns1['Input'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['OutPut'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['CreatedDate'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['CreatedBy'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['event_type'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['MessageID'])."','".mysqli_real_escape_string($this->connectionlink,$sns1['SNSResult'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$scanned_media[$j]['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."')";
						$q68 = mysqli_query($this->connectionlink,$query68);						
					}
                }    
                                
                if($SNS_Message1['id'] > 0 && $sns1['id']) {
					$query69 = "delete from speed_sns_messages where id = '".$sns1['id']."' ";
					$q69 = mysqli_query($this->connectionlink,$query69);
				}
                $query77 = "update speed_bulk_media_process set CompletedDate = NOW(),CompletedBy = '".$_SESSION['user']['UserId']."',ProcessCompledControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',Status = 'Completed' where BulkMediaProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['BulkMediaProcessID'])."' ";
                $q77 = mysqli_query($this->connectionlink,$query77);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //Start unlock source bin
                $query5 = "update custompallet set LockedForBulkMediaDestruction = '0',LockedTime = NULL,LockedBy = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."'";
                $q5 = mysqli_query($this->connectionlink,$query5);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $query4 = "insert into custompallet_lock_history (CustomPalletID,LockedType,LockedPage,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."','UnLocked','Bulk Media Destruction',NOW(),'".$_SESSION['user']['UserId']."')";
                $q4 = mysqli_query($this->connectionlink,$query4);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //End unlock source bin
                $json['Shred'] = "1";                				

            } else {
                $json['Success'] = false;
				$json['Result'] = "Invalid Degauss BIN";
				return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = "Bulk Media Process Completed";
            $json['ProcessedRecords'] = $i;
            return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetBulkMediaDestructionSourceBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {			
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bulk Media Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bulk Media Destruction Page';
				return json_encode($json);
			}
                        
			$json = array(
				'Success' => false,
				'Result' => $data
			);	

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destroyed_disposition from custompallet c 
			left join disposition  d on  c.disposition_id = d.disposition_id 
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.ssd_disposition = 1 or d.hdd_disposition = 1) and c.LockedForBulkMediaDestruction = 0 order by c.BinName ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;				
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No BINs Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}

}
?>