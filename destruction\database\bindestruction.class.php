<?php
session_start();
include_once("destruction.class.php");
class BinDestructionClass extends DestructionClass {


    public function SelectWorkstationBinDestruction($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => ''
        );
        try {
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }

            //$sql = "Select SiteID,SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' order by SiteName";
			$sql = "Select SiteID,SiteName,LockedForUser from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' and (Locked = '0' or LockedForUser = '".$_SESSION['user']['UserId']."') order by SiteName";
            $query = mysqli_query($this->connectionlink, $sql);
            if (mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            } else {
                if (mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while ($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Results';
                }
            }
            return json_encode($json);

        } catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
  }

  public function GetDestrcutionRigByStation($data) {
      if (!isset($_SESSION['user'])) {
          $json['Success'] = false;
          $json['Result'] = 'Login to continue';
          return json_encode($json);
      }
      $json = array(
          'Success' => false,
          'Result' => ''
      );

      try {
          if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
              $json['Success'] = false;
              $json['Result'] = 'No Access to Bin Destruction Page';
              return json_encode($json);
          }

      //Start Unlock user stations
      $this->UnlockUserStations();
      //End Unlock user stations

      //Start check if Station is locked or not
      $query1 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
      $q1 = mysqli_query($this->connectionlink,$query1);
      if(mysqli_error($this->connectionlink)) {
        $json['Success'] = false;
        $json['Result'] = mysqli_error($this->connectionlink);
        return json_encode($json);
      }
      if(mysqli_affected_rows($this->connectionlink) > 0) {
        $row1 = mysqli_fetch_assoc($q1);
        if($row1['Locked'] == '1') {
          $json['Success'] = false;
          $json['Result'] = 'Station ('.$row1['SiteName'].') is Locked';
          return json_encode($json);
        }
        $query2 = "update site set Locked = '1', LockedForUser = '".$_SESSION['user']['UserId']."' where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
        $q2 = mysqli_query($this->connectionlink,$query2);
        if(mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
      } else {
        $json['Success'] = false;
        $json['Result'] = 'Invalid Station';
        return json_encode($json);
      }
      //End check if Station is locked or not


            $sql = "select R.RigID,R.SiteID,R.Rigname,S.SiteName from Rig R
            LEFT JOIN site S ON R.SiteID = S.SiteID
            WHERE R.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and R.Status = '1' ";
            $query = mysqli_query($this->connectionlink, $sql);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
            } else {
                if (mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while ($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Rigs Available';
                    return json_encode($json);
                }
            }
            return json_encode($json);
        } catch (Exception $e) {
      $json['Success'] = false;
      $json['Result'] = $e->getMessage();
      return json_encode($json);
    }
  }


    public function GetDestrcutionRig($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => ''
        );

        try {
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }

			//Start Unlock user stations
			$this->UnlockUserStations();
			//End Unlock user stations

			//Start check if Station is locked or not
			/*$query1 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['Locked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Station ('.$row1['SiteName'].') is Locked';
					return json_encode($json);
				}
				$query2 = "update site set Locked = '1', LockedForUser = '".$_SESSION['user']['UserId']."' where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Station';
				return json_encode($json);
			}*/
			//End check if Station is locked or not


            /*$sql = "select R.RigID,R.SiteID,R.Rigname,S.SiteName from Rig R
            LEFT JOIN site S ON R.SiteID = S.SiteID
            WHERE R.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and R.Status = '1' ";*/
            $sql = "select R.RigID,R.Rigname from Destruction_Configuration DC
            LEFT JOIN Rig R ON R.RigID = DC.RigID
            WHERE DC.parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['PartTypeId'])."'
            and DC.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'
            and R.Status=1 and DC.Status=1 and DC.FacilityID='".$_SESSION['user']['FacilityID']."' order by R.Rigname ASC ";
            $query = mysqli_query($this->connectionlink, $sql);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
            } else {
                if (mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while ($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Rigs Available';
                    return json_encode($json);
                }
            }
            return json_encode($json);
        } catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
    }



    public function ValidateBinDestructionSoureceBin($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => ''
        );
        try {
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access Bin Destruction Page';
				return json_encode($json);
			}

            $query = "select c.*,d.disposition,d.destruction_disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}
				if($row['destruction_disposition'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Bin Disposition ('.$row['disposition'].') is not eligible for Destruction';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				if($row['AssetsCount'] == 0) {
					$json['Success'] = false;
					$json['Result'] = 'Quantity of the BIN is 0';
					return json_encode($json);
				}

        		if($row['AuditLocked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN is locked for Audit';
					return json_encode($json);
				}

				$part_types = array();
				//Start get Bin Part Types
				// $query16 = "select distinct(parttypeid) as parttypeid from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
				// $q16 = mysqli_query($this->connectionlink,$query16);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$i = 0;
				// 	while($row16 = mysqli_fetch_assoc($q16)) {
				// 		array_push($part_types, $row16['parttypeid']);
				// 		$i++;
				// 	}
				// 	if($i > 1) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Mulitple Part Types exists for the BIN';
				// 		return json_encode($json);
				// 	}
				// }


				// $query16 = "select distinct(parttypeid) as parttypeid from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
				// $q16 = mysqli_query($this->connectionlink,$query16);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$i = 0;
				// 	while($row16 = mysqli_fetch_assoc($q16)) {
				// 		array_push($part_types, $row16['parttypeid']);
				// 		$i++;
				// 	}
				// 	if($i > 1) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Mulitple Part Types exists for the BIN';
				// 		return json_encode($json);
				// 	}
				// }

				// $query16 = "select distinct(parttypeid) as parttypeid from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
				// $q16 = mysqli_query($this->connectionlink,$query16);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$i = 0;
				// 	while($row16 = mysqli_fetch_assoc($q16)) {
				// 		array_push($part_types, $row16['parttypeid']);
				// 		$i++;
				// 	}
				// 	if($i > 1) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Mulitple Part Types exists for the BIN';
				// 		return json_encode($json);
				// 	}
				// }

				// $query16 = "select distinct(parttypeid) as parttypeid from unserialized_recovery_records where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
				// $q16 = mysqli_query($this->connectionlink,$query16);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$i = 0;
				// 	while($row16 = mysqli_fetch_assoc($q16)) {
				// 		array_push($part_types, $row16['parttypeid']);
				// 		$i++;
				// 	}
				// 	if($i > 1) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Mulitple Part Types exists for the BIN';
				// 		return json_encode($json);
				// 	}
				// }

				// $part_types = array_unique($part_types);

				// if(count($part_types) > 1) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Mulitple Part Types exists for the BIN';
				// 	return json_encode($json);
				// }

				// if(count($part_types) == 0) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'No Part Types available';
				// 	return json_encode($json);
				// }


				//End get Bin Part Types

				$json['Success'] = true;
				$json['parttypeid'] = $part_types[0];
				$json['CustomPalletID'] = $row['CustomPalletID'];
				$json['AssetsCount'] = $row['AssetsCount'];
				$json['BinName'] = $row['BinName'];
				$json['disposition_id'] = $row['disposition_id'];
				if($row['AcceptAllDisposition'] == '1') {
					$json['disposition'] = 'All Disposition';
				} else {
					$json['disposition'] = $row['disposition'];
				}
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			return json_encode($json);

        } catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
    }



	public function GetAllDestructionContainers($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }
			$json = array(
				'Success' => false,
				'Result' => $data
			);
      $postDispositionId = 0;
      $getValidDispositionQ = "select DC.Post_Destruction_Disposition_id from Destruction_Configuration DC WHERE DC.parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."' and DC.disposition_id='".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and DC.RigID='".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."' and DC.FacilityID = '".$_SESSION['user']['FacilityID']."'";
      $getValidDispositionQEx = mysqli_query($this->connectionlink,$getValidDispositionQ);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
      if(mysqli_affected_rows($this->connectionlink) > 0) {
        $postDisposition = mysqli_fetch_assoc($getValidDispositionQEx);
        $postDispositionId = $postDisposition['Post_Destruction_Disposition_id'];
      }else{
        $json['Success'] = false;
				$json['Result'] = "No destruction config available for selected part type, disposition and Rig combination";
				return json_encode($json);
      }

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destruction_disposition from shipping_containers c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and c.disposition_id = '".$postDispositionId."'  order by c.ShippingContainerID ";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Containers Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



  public function GetByProducts($data) {
    if(!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }

    try {
      if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }
      $json = array(
        'Success' => false,
        'Result' => $data
      );
      //$query = "select c.* from shipping_containers c	where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
      $query = "select DC.byproduct_id from Destruction_Configuration DC
      WHERE DC.parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['PartTypeId'])."' and DC.disposition_id='".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and DC.RigID='".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."'";
      $q = mysqli_query($this->connectionlink,$query);
      if(mysqli_error($this->connectionlink)) {
        $json['Success'] = false;
        $json['Result'] = mysqli_error($this->connectionlink);
        return json_encode($json);
      }
      if(mysqli_affected_rows($this->connectionlink) > 0) {
        $row = mysqli_fetch_assoc($q);

        //Start get all by products matching shipment container
        //$query1 = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid where b.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$row['disposition_id'])."' ";
        $query1 = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid  where b.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."'";
        $q1 = mysqli_query($this->connectionlink,$query1);
        if(mysqli_error($this->connectionlink)) {
          $json['Success'] = false;
          $json['Result'] = mysqli_error($this->connectionlink);
          return json_encode($json);
        }
        $by_products = array();
        $i = 0;
        if(mysqli_affected_rows($this->connectionlink) > 0) {
          while($row1 = mysqli_fetch_assoc($q1)) {
            $by_products[$i] = $row1;
            $i++;
          }
        }
        //End get all by products matching shipment container

        $json['Success'] = true;
        $json['Result'] = $by_products;
        return json_encode($json);
      } else {
        $json['Success'] = false;
        $json['Result'] = "Invalid Container".$query;
        return json_encode($json);
      }
      return json_encode($json);

    } catch (Exception $e) {
      $json['Success'] = false;
      $json['Result'] = $e->getMessage();
      return json_encode($json);
    }
  }



	public function ProcessBinDestruction($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
      				$json['Success'] = false;
      				$json['Result'] = 'You have Read only Access Bin Destruction Page';
      				return json_encode($json);
      			}

			$json = array(
				'Success' => false,
				'Result' => $data
			);
			//return json_encode($json);
			//Start validate Audit Controller

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['DestructionController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Destruction Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Audit Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

            $data['CustomPalletID'] = $data['SourceCustomPalletID'];
			if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c
				left join disposition  d on  c.disposition_id = d.disposition_id
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);
				}
				//End get to_cp_details

				//Start get from_cp_details
				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);
					if($from_cp['AuditLocked'] == '1') {
						$json['Success'] = false;
						$json['Result'] = 'BIN is locked for Audit';
						return json_encode($json);
					}
					$from_available = $from_cp['AssetsCount'];
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}
				//End get from_cp_details

				//Start check if destination bin has enough space to handle source bin assets (only if MaxLimitRequired = 1)
				if($to_cp['MaxLimitRequired'] == '1') {
					$currentAssetsInToBin = intval($to_cp['AssetsCount']);
					$maxAssetsAllowed = intval($to_cp['MaximumAssets']);
					$assetsToMove = intval($from_cp['AssetsCount']);

					// Check if destination bin has enough space
					if(($currentAssetsInToBin + $assetsToMove) > $maxAssetsAllowed) {
						$availableSpace = $maxAssetsAllowed - $currentAssetsInToBin;
						$json['Success'] = false;
						$json['Result'] = "Destination bin does not have enough space. Available space: $availableSpace, Assets to move: $assetsToMove. Maximum capacity: $maxAssetsAllowed";
						return json_encode($json);
					}
				}

				//Start Customer Lock Validation
				if($to_cp['CustomerLock'] == '1') {
					// Get destination bin's customer
					$destinationCustomerID = $to_cp['AWSCustomerID'];

					// Check if destination bin is empty (no customer assigned yet)
					if($destinationCustomerID == NULL || $destinationCustomerID == '' || $destinationCustomerID == '0') {
						// Destination bin is empty, get customer from source records
						$sourceCustomerQuery = "
							SELECT DISTINCT AWSCustomerID FROM (
								SELECT AWSCustomerID FROM asset WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
								UNION
								SELECT AWSCustomerID FROM speed_server_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
								UNION
								SELECT AWSCustomerID FROM speed_media_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
							) AS combined_customers
						";
						$sourceCustomerResult = mysqli_query($this->connectionlink, $sourceCustomerQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						$sourceCustomers = array();
						while($row = mysqli_fetch_assoc($sourceCustomerResult)) {
							$sourceCustomers[] = $row['AWSCustomerID'];
						}

						// Check if all source records belong to the same customer
						if(count($sourceCustomers) > 1) {
							// Get customer names for error message
							$customerNames = array();
							foreach($sourceCustomers as $custId) {
								$custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$custId)."'";
								$custResult = mysqli_query($this->connectionlink, $custQuery);
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$custRow = mysqli_fetch_assoc($custResult);
									$customerNames[] = $custRow['Customer'];
								}
							}
							$json['Success'] = false;
							$json['Result'] = 'Source bin contains records from multiple customers (' . implode(', ', $customerNames) . '). Cannot move to customer-locked bin.';
							return json_encode($json);
						}

						if(count($sourceCustomers) == 1) {
							// All source records belong to same customer, destination bin will be assigned to this customer
							$sourceCustomerID = $sourceCustomers[0];
							$custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$sourceCustomerID)."'";
							$custResult = mysqli_query($this->connectionlink, $custQuery);
							$customerName = 'Unknown Customer';
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$custRow = mysqli_fetch_assoc($custResult);
								$customerName = $custRow['Customer'];
							}

							// Update destination bin's customer
							$updateBinQuery = "UPDATE custompallet SET AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$sourceCustomerID)."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
							$updateBinResult = mysqli_query($this->connectionlink, $updateBinQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}

							// Add tracking record
							$trackingAction = "Bin customer assigned to " . $customerName . " during bin destruction from " . $from_cp['BinName'];
							$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName) VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."', 'Bin Destruction')";
							mysqli_query($this->connectionlink, $trackingQuery);
						}
					} else {
						// Destination bin has a customer, validate all source records match
						$mismatchQuery = "
							SELECT COUNT(*) as mismatch_count FROM (
								SELECT AWSCustomerID FROM asset WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND (AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0' OR AWSCustomerID != '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."')
								UNION ALL
								SELECT AWSCustomerID FROM speed_server_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND (AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0' OR AWSCustomerID != '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."')
								UNION ALL
								SELECT AWSCustomerID FROM speed_media_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND (AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0' OR AWSCustomerID != '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."')
							) AS mismatched_records
						";
						$mismatchResult = mysqli_query($this->connectionlink, $mismatchQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						$mismatchRow = mysqli_fetch_assoc($mismatchResult);
						if($mismatchRow['mismatch_count'] > 0) {
							// Get customer names for error message
							$destCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."'";
							$destCustResult = mysqli_query($this->connectionlink, $destCustQuery);
							$destCustomerName = 'Unknown Customer';
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$destCustRow = mysqli_fetch_assoc($destCustResult);
								$destCustomerName = $destCustRow['Customer'];
							}

							$json['Success'] = false;
							$json['Result'] = 'Destination bin is locked to ' . $destCustomerName . ', but source bin contains records from different customers. Cannot move for destruction.';
							return json_encode($json);
						}
					}
				}
				//End Customer Lock Validation

				$from_disposition_id = $data['disposition_id'];
				$from_status = $data['StatusID'];
				$from_CustomPalletID = $data['CustomPalletID'];

				$to_disposition_id = $to_cp['disposition_id'];

                $to_status = '12';
				$to_status_text = 'Shreded';


				$to_CustomPalletID = $to_cp['CustomPalletID'];				

				$bulkTransactionId = 0;
				//Start insert into bin destruction process
				$query5 = "insert into bin_distruction_process (SourceCustomPalletID, SourceBinName, SourceBinDisposition_id, AuditController, DestinationCustomPalletID,DestinationBinName, CreatedDate, CreatedBy,DestinationBinDisposition_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinDisposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."')";
				$q5 = mysqli_query($this->connectionlink,$query5);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$bulkTransactionId = mysqli_insert_id($this->connectionlink);
				//End insert into bin destruction process


				//Start insert in rack_recovery_records_tracking
				$action = "Serial from BIN (".$from_cp['BinName'].") is Destroyed and Moved to Bin (".$to_cp['BinName'].") in Bin Destruction";
				$action .= " - Controller: " . $data['AuditController'];
				// $query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)
				// Select r.AssetScanID,'".$action."','','',NOW(),'".$_SESSION['user']['UserId']."' from asset r 
				// where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' and r.StatusID=1 
				// ";

				$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)
				Select r.AssetScanID,'".$action."','','',NOW(),'".$_SESSION['user']['UserId']."' from asset r 
				where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' 
				";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert in rack_recovery_records_tracking

				//start insert into destruction history
				$query7 = "insert into destruction_history(AssetScanID,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,origin_bin_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName)
				Select r.AssetScanID,r.CustomPalletID,cp.BinName,'".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',r.disposition_id,'".$to_disposition_id."',NOW(),'".$_SESSION['user']['UserId']."',
				'".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".$bulkTransactionId."','".mysqli_real_escape_string($this->connectionlink,$data['origin_bin_scan_time'])."',
				'".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Bin Destruction' from asset r 
				left join custompallet cp on r.CustomPalletID = cp.CustomPalletID 
				where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' 
				";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
				}
				//End insert into destruction history

				//Start rack_recovery_records				
				$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '12',RecentDispositionDate = NOW(),RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Created in Bin Destruction' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' ";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End rack_recovery_records

				



				//Start insert in server_recovery_records_tracking
				$action = "Serial from BIN (".$from_cp['BinName'].") is Destroyed and moved to Bin (".$to_cp['BinName'].") in Bin Destruction";
				$action .= " - Controller: " . $data['AuditController'];
				$query2 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy)
				Select r.ServerID,r.ServerSerialNumber,r.Type,r.idPallet,'".$action."','','".$data['AuditController']."',NOW(),'".$_SESSION['user']['UserId']."' from speed_server_recovery r 
				 where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'  
				";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert in server_recovery_records_tracking

				//start insert into destruction history
				$query7 = "insert into destruction_history(ServerID,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,origin_bin_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName)
				Select r.ServerID,r.CustomPalletID,cp.BinName,'".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',r.disposition_id,'".$to_disposition_id."',NOW(),'".$_SESSION['user']['UserId']."'
				,'".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".$bulkTransactionId."','".mysqli_real_escape_string($this->connectionlink,$data['origin_bin_scan_time'])."',
				'".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Bin Destruction' from speed_server_recovery r 
				left join custompallet cp on r.CustomPalletID = cp.CustomPalletID 
				where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' ";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
				}
				//End insert into destruction history

                //Start assembly_recovery_records				
				$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '12' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' ";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End assembly_recovery_records


				//Start insert in media_recoverY_tracking				
				$action = "Serial from BIN (".$from_cp['BinName'].") is Destroyed and moved to Bin (".$to_cp['BinName'].") in Bin Destruction";
				$action .= " - Controller: " . $data['AuditController'];
				$query2 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy)
				Select r.MediaID,r.MediaSerialNumber,r.MediaType,r.ServerSerialNumber,r.idPallet,'".$action."','','".$data['AuditController']."',NOW(),'".$_SESSION['user']['UserId']."' from speed_media_recovery r 
				 where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' 
				";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert in media_recoverY_tracking

				//start insert into destruction history
				$query7 = "insert into destruction_history(MediaID,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,origin_bin_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName)
				Select r.MediaID,r.CustomPalletID,cp.BinName,'".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',r.disposition_id,'".$to_disposition_id."',NOW(),'".$_SESSION['user']['UserId']."'
				,'".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".$bulkTransactionId."','".mysqli_real_escape_string($this->connectionlink,$data['origin_bin_scan_time'])."',
				'".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Bin Destruction' from speed_media_recovery r 
				left join custompallet cp on r.CustomPalletID = cp.CustomPalletID 
				where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' 
				";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
				}
				//End insert into destruction history

                //Start component_recovery_records				
				$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DestructionControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditControllerName'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '3' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' ";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End component_recovery_records				


				//Start insert in unserialized_recovery_tracking				
				$action = "Item from BIN (".$from_cp['BinName'].") is Destroyed and moved to Bin (".$to_cp['BinName'].") in Bin Destruction";
				$action .= " - Controller: " . $data['AuditController'];
				$query2 = "insert into unserialized_recovery_tracking (UnserializedRecoveryRecordID,PartType,idPallet,Action,ControllerLoginID,CreatedDate,CreatedBy)
				Select r.UnserializedRecoveryRecordID,p.parttype,r.idPallet,'".$action."','".$data['AuditController']."',NOW(),'".$_SESSION['user']['UserId']."' from unserialized_recovery_records r
				left join parttype p on r.parttypeid = p.parttypeid where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' and r.IsCommitted=1
				";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert in unserialized_recovery_tracking

				//start insert into destruction history
				$query7 = "insert into destruction_history(UnserializedRecoveryRecordID,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,FromDispositionID,ToDispositionID,CreatedDate,CreatedBy,AuditController,bulk_transaction_flag,bulk_transaction_id,origin_bin_scan_time,workstation_scan_time,destruction_rig_scan_time,container_scan_time,controller_scan_time,FacilityID,ModuleName)
				Select r.UnserializedRecoveryRecordID,r.CustomPalletID,r.DispositionBin,'".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',r.DispositionID,'".$to_disposition_id."',NOW(),'".$_SESSION['user']['UserId']."'
				,'".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',1,'".$bulkTransactionId."','".mysqli_real_escape_string($this->connectionlink,$data['origin_bin_scan_time'])."',
				'".mysqli_real_escape_string($this->connectionlink,$data['destruction_workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['destruction_rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['container_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','".$_SESSION['user']['FacilityID']."','Bin Destruction' from unserialized_recovery_records r
				where r.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' and r.IsCommitted=1
				";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
				}
				//End insert into destruction history


				


                //Start unserialized_recovery_records				
				$query1 = "update unserialized_recovery_records set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '12' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."' and IsCommitted=1";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End unserialized_recovery_records				


				//Start update Custom Pallet Items
				//$query4 = "update `custompallet` SET StatusID = 5,StatusModifiedDate = NOW(),StatusModifiedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				//End update Custom Pallet Counts	
											

				//Start Delete items from custompallet_items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Delete items from custompallet_items


				//Start update custompallet count
				$query55 = "select count(*) from  custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
				$q55 = mysqli_query($this->connectionlink,$query55);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row55 = mysqli_fetch_assoc($q55);

					$query4 = "UPDATE `custompallet` SET `AssetsCount`= '".$row55['count(*)']."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

				}
				//End update custompallet count

				//Start writing in custompallet_tracking
				$tracking_action = "Bin Destroyed and moved to Bin (".$to_cp['BinName'].") in Bin Destruction";
				$tracking_action .= " - Controller: " . $data['AuditController'];
				$tracking_query = "insert into custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName) values ('".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."','".mysqli_real_escape_string($this->connectionlink,$tracking_action)."',NOW(),'".$_SESSION['user']['UserId']."','Bin Destruction')";	
				$tracking_q = mysqli_query($this->connectionlink,$tracking_query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}	

				$json['Success'] = true;
				$json['Result'] = "Bin Destruction Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Next BIN";
				return json_encode($json);
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ProcessBinDestruction_old($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Bin Destruction Page';
                return json_encode($json);
            }

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access Bin Destruction Page';
				return json_encode($json);
			}

			$json = array(
				'Success' => false,
				'Result' => $data
			);
			//return json_encode($json);
			//Start validate Audit Controller

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['AuditController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Audit Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Audit Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

            $data['CustomPalletID'] = $data['SourceCustomPalletID'];
			if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c
				left join disposition  d on  c.disposition_id = d.disposition_id
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
					// if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
					// 	$json['Success'] = false;
					// 	$json['Result'] = "Mobility Name is not configured for Next BIN ID";
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);
				}
				//End get to_cp_details

				//Start get from_cp_details
				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}
				//End get from_cp_details

				$from_disposition_id = $data['disposition_id'];
				$from_status = $data['StatusID'];
				$from_CustomPalletID = $data['CustomPalletID'];

				$to_disposition_id = $to_cp['disposition_id'];

                $to_status = '5';
				$to_status_text = 'Destructed';

				// if($data['StatusID'] == '1') {
				// 	$to_status = '2';
				// 	$to_status_text = 'PendingShred';
				// } else if($data['StatusID'] == '2') {
				// 	$to_status = '3';
				// 	$to_status_text = 'Shreded';
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = "Invalid Media Status";
				// 	return json_encode($json);
				// }
				$to_CustomPalletID = $to_cp['CustomPalletID'];


				//Start rack_recovery_records
				$query1 = "update rack_recovery_records set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End rack_recovery_records

                //Start assembly_recovery_records
				$query1 = "update assembly_recovery_records set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End assembly_recovery_records


                //Start component_recovery_records
				$query1 = "update component_recovery_records set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End component_recovery_records

                //Start unserialized_recovery_records
				$query1 = "update unserialized_recovery_records set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',ModifiedDate = NOW(),ModifiedBy = '".$_SESSION['user']['UserId']."',DispositionID = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End unserialized_recovery_records




				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items

				//Start update Custom Pallet Counts

                $query3 = "select sum(Quantity) as CPCount from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
                if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
                    $to_CustomPalletID_Count = $row3['CPCount'];
				} else {
					$to_CustomPalletID_Count = 0;
				}


				$query3 = "UPDATE `custompallet` SET `AssetsCount`= ".$to_CustomPalletID_Count." WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Start insert into bin destruction process
				$query5 = "insert into bin_distruction_process (SiteID, RigID, SourceCustomPalletID, SourceBinName, SourceBinDisposition_id, AuditController, DestinationCustomPalletID, DestinationBinName, CreatedDate, CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinDisposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q5 = mysqli_query($this->connectionlink,$query5);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink).$query5;
					return json_encode($json);
				}
				//End insert into bin destruction process

				$json['Success'] = true;
				$json['Result'] = "Bin Destruction Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Next BIN ID";
				return json_encode($json);
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}







    public function GetPendingMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Destruction Page';
				return json_encode($json);
			}
      //,u.FirstName,u.LastName,si.SiteName,p.LoadId,p.ReceivedDate
      //left join users u on rr.CreatedBy = u.UserId
      //left join pallets p on rr.idPallet = p.idPallet
      //left join custompallet c on rr.CustomPalletID = c.CustomPalletID
            $query = "select * FROM (";
            $query .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from rack_recovery_records rr
            left join parttype pt on rr.parttypeid = pt.parttypeid
            left join disposition d on rr.DispositionID = d.disposition_id
            left join statusses s on rr.StatusID = s.StatusID
            left join facility f on rr.FacilityID = f.FacilityID
            left join site si on rr.SiteID = si.SiteID
            where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

            if(count($data[0]) > 0) {
              foreach ($data[0] as $key => $value) {
                if($value != '') {

                  if($key == 'idPallet') {
                    $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'ServerSerialNumber') {
                    $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'MediaSerialNumber') {
                    $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'PartType') {
                    $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'disposition') {
                    $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                  if($key == 'Status') {
                    $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                              if($key == 'BinName') {
                    $query = $query . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                              if($key == 'FirstName') {
                    $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                              if($key == 'AuditControllerID') {
                    $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }


                              if($key == 'FacilityName') {
                    $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                              if($key == 'SiteName') {
                    $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }
                              /*if($key == 'LoadId') {
                    $query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }*/
                              if($key == 'ReceivedDate') {
                    $query = $query . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }

                  if($key == 'CreatedDate') {
                    $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                  }

                }
              }
            }

      $query .= " UNION ALL ";

      $query .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from assembly_recovery_records rr
      left join parttype pt on rr.parttypeid = pt.parttypeid
      left join disposition d on rr.DispositionID = d.disposition_id
      left join statusses s on rr.StatusID = s.StatusID
      left join facility f on rr.FacilityID = f.FacilityID
      left join site si on rr.SiteID = si.SiteID
      where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

      if(count($data[0]) > 0) {
        foreach ($data[0] as $key => $value) {
          if($value != '') {

            if($key == 'idPallet') {
              $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'ServerSerialNumber') {
              $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MediaSerialNumber') {
              $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'PartType') {
              $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'disposition') {
              $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'Status') {
              $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'BinName') {
              $query = $query . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'FirstName') {
              $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'AuditControllerID') {
              $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }


                        if($key == 'FacilityName') {
              $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'SiteName') {
              $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        /*if($key == 'LoadId') {
              $query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }*/
                        if($key == 'ReceivedDate') {
              $query = $query . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

            if($key == 'CreatedDate') {
              $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

          }
        }
      }

      $query .= " UNION ALL ";

      $query .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from component_recovery_records rr
      left join parttype pt on rr.parttypeid = pt.parttypeid
      left join disposition d on rr.DispositionID = d.disposition_id
      left join statusses s on rr.StatusID = s.StatusID
      left join facility f on rr.FacilityID = f.FacilityID
      left join site si on rr.SiteID = si.SiteID
      where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

      if(count($data[0]) > 0) {
        foreach ($data[0] as $key => $value) {
          if($value != '') {

            if($key == 'idPallet') {
              $query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'ServerSerialNumber') {
              $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'MediaSerialNumber') {
              $query = $query . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'PartType') {
              $query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'disposition') {
              $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
            if($key == 'Status') {
              $query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'BinName') {
              $query = $query . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'FirstName') {
              $query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'AuditControllerID') {
              $query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }


                        if($key == 'FacilityName') {
              $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        if($key == 'SiteName') {
              $query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }
                        /*if($key == 'LoadId') {
              $query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }*/
                        if($key == 'ReceivedDate') {
              $query = $query . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

            if($key == 'CreatedDate') {
              $query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
            }

          }
        }
      }

    $query .= ") AS result ";

    if($data['OrderBy'] != '') {
      if($data['OrderByType'] == 'asc') {
        $order_by_type = 'asc';
      } else {
        $order_by_type = 'desc';
      }

      if($data['OrderBy'] == 'idPallet') {
        $query = $query . " order by m.idPallet ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'ServerSerialNumber') {
        $query = $query . " order by rr.SerialNumber ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'MediaSerialNumber') {
        $query = $query . " order by rr.SerialNumber ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'MediaType') {
        $query = $query . " order by m.MediaType ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'disposition') {
        $query = $query . " order by d.disposition ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'Status') {
        $query = $query . " order by s.Status ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'BinName') {
        $query = $query . " order by rr.DispositionBin ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'FirstName') {
        $query = $query . " order by u.FirstName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'AuditControllerID') {
        $query = $query . " order by m.AuditControllerID ".$order_by_type." ";
      }

              else if($data['OrderBy'] == 'FacilityName') {
        $query = $query . " order by f.FacilityName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'SiteName') {
        $query = $query . " order by si.SiteName ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'LoadId') {
        $query = $query . " order by p.LoadId ".$order_by_type." ";
      }
              else if($data['OrderBy'] == 'ReceivedDate') {
        $query = $query . " order by p.ReceivedDate ".$order_by_type." ";
      }
      else if($data['OrderBy'] == 'CreatedDate') {
        $query = $query . " order by rr.CreatedDate ".$order_by_type." ";
      }

    } else {
      $query = $query . " order by CreatedDate asc ";
    }

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

      //echo $query;exit;

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				//$assets = array();
				while($row = mysqli_fetch_assoc($q)) {
					$assets[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
				//return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Assets Available';
				//return json_encode($json);
			}
		      if($data['skip'] == 0) {

            $query1 = "select count(*) FROM (";
            $query1 .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from rack_recovery_records rr
            left join parttype pt on rr.parttypeid = pt.parttypeid
            left join disposition d on rr.DispositionID = d.disposition_id
            left join statusses s on rr.StatusID = s.StatusID
            left join facility f on rr.FacilityID = f.FacilityID
            left join site si on rr.SiteID = si.SiteID
            where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

            if(count($data[0]) > 0) {
              foreach ($data[0] as $key => $value) {
                if($value != '') {

                  if($key == 'idPallet') {
                                    $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'ServerSerialNumber') {
                                    $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'MediaSerialNumber') {
                                    $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'PartType') {
                                    $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'disposition') {
                                    $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'Status') {
                                    $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'BinName') {
                                    $query1 = $query1 . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'FirstName') {
                                    $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'AuditControllerID') {
                                    $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }

                                if($key == 'FacilityName') {
                                    $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                if($key == 'SiteName') {
                                    $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                                /*if($key == 'LoadId') {
                                    $query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }*/
                                if($key == 'ReceivedDate') {
                                    $query1 = $query1 . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                  if($key == 'CreatedDate') {
                                    $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                                }
                }
              }
            }

          $query1 .= " UNION ALL ";

          $query1 .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from assembly_recovery_records rr
          left join parttype pt on rr.parttypeid = pt.parttypeid
          left join disposition d on rr.DispositionID = d.disposition_id
          left join statusses s on rr.StatusID = s.StatusID
          left join facility f on rr.FacilityID = f.FacilityID
          left join site si on rr.SiteID = si.SiteID
          where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

          if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
              if($value != '') {

                if($key == 'idPallet') {
                                  $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'ServerSerialNumber') {
                                  $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'MediaSerialNumber') {
                                  $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'PartType') {
                                  $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'disposition') {
                                  $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'Status') {
                                  $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'BinName') {
                                  $query1 = $query1 . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'FirstName') {
                                  $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'AuditControllerID') {
                                  $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }

                              if($key == 'FacilityName') {
                                  $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'SiteName') {
                                  $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              /*if($key == 'LoadId') {
                                  $query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }*/
                              if($key == 'ReceivedDate') {
                                  $query1 = $query1 . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                if($key == 'CreatedDate') {
                                  $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
              }
            }
          }

          $query1 .= " UNION ALL ";

          $query1 .= "select rr.*,d.disposition,f.FacilityName,pt.parttype from component_recovery_records rr
          left join parttype pt on rr.parttypeid = pt.parttypeid
          left join disposition d on rr.DispositionID = d.disposition_id
          left join statusses s on rr.StatusID = s.StatusID
          left join facility f on rr.FacilityID = f.FacilityID
          left join site si on rr.SiteID = si.SiteID
          where rr.StatusID = 1 and rr.DispositionID = 52 and rr.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

          if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
              if($value != '') {

                if($key == 'idPallet') {
                                  $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'ServerSerialNumber') {
                                  $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'MediaSerialNumber') {
                                  $query1 = $query1 . " AND rr.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'PartType') {
                                  $query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'disposition') {
                                  $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'Status') {
                                  $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'BinName') {
                                  $query1 = $query1 . " AND rr.DispositionBin like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'FirstName') {
                                  $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'AuditControllerID') {
                                  $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }

                              if($key == 'FacilityName') {
                                  $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              if($key == 'SiteName') {
                                  $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                              /*if($key == 'LoadId') {
                                  $query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }*/
                              if($key == 'ReceivedDate') {
                                  $query1 = $query1 . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
                if($key == 'CreatedDate') {
                                  $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                              }
              }
            }
          }

        $query1 .= ") AS result ";

                /*$query1 = "select count(*) from speed_media_recovery m
                left join disposition d on m.disposition_id = d.disposition_id
                left join speed_status s on m.StatusID = s.StatusID
                left join custompallet c on m.CustomPalletID = c.CustomPalletID
                left join users u on m.CreatedBy = u.UserId
                left join facility f on m.FacilityID = f.FacilityID
                left join site si on m.SiteID = si.SiteID
                left join pallets p on m.idPallet = p.idPallet
                where m.StatusID in (1,2,4) and m.FacilityID = '".$_SESSION['user']['FacilityID']."' ";*/


				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GeneratePendingMediaPannelxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Bin Destruction Page';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['PendingMediaPannelxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function ValidateNextBinID($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Destruction Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$query = "select c.*,d.disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['InventoryBased'] == '1') {
				// 	$json['Success'] = false;
				// 	$json['Error'] = 'BIN is dedicated for Sub Component';
				// 	return json_encode($json);
				// }
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Error'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Error'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['CustomPalletID'] = $row['CustomPalletID'];
				$json['BinName'] = $row['BinName'];
				$json['disposition_id'] = $row['disposition_id'];
				if($row['AcceptAllDisposition'] == '1') {
					$json['disposition'] = 'All Disposition';
				} else {
					$json['disposition'] = $row['disposition'];
				}
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ProcessMediaSN($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Destruction Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access Bin Destruction Page';
				return json_encode($json);
			}

			$json = array(
				'Success' => false,
				'Result' => $data
			);
			//return json_encode($json);
			//Start validate Audit Controller

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['PendingMediaController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Pending Media Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pending Media Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

			if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c
				left join disposition  d on  c.disposition_id = d.disposition_id
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
					if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
						$json['Success'] = false;
						$json['Result'] = "Mobility Name is not configured for Next BIN ID";
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);
				}
				//End get to_cp_details

				//Start get from_cp_details
				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}
				//End get from_cp_details

				$from_disposition_id = $data['disposition_id'];
				$from_status = $data['StatusID'];
				$from_CustomPalletID = $data['CustomPalletID'];

				$to_disposition_id = $to_cp['disposition_id'];
				if($data['StatusID'] == '1') {
					$to_status = '2';
					$to_status_text = 'PendingShred';
				} else if($data['StatusID'] == '2') {
					$to_status = '3';
					$to_status_text = 'Shreded';
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				$to_CustomPalletID = $to_cp['CustomPalletID'];

				//Start send Custody SNS
				$message_cus = '
				{
					"eventType": "BATCH_MEDIA_DRIVES_IN_CUSTODY",
					"data": {
					"site": "'.$_SESSION['user']['FacilityName'].'",
					"login": "'.$_SESSION['user']['UserName'].'",
					"media": [
						{
						"serial": "'.$data['MediaSerialNumber'].'",
						"type": "'.$data['MediaType'].'",
						"timestamp": "'.time().'"
						}
						]
					}
				}';
				$event_type_cus = "BATCH_MEDIA_DRIVES_IN_CUSTODY";

				$SNS_Message_cus = $this->SendSNSMessage($message_cus,$data['MediaSerialNumber'],$event_type_cus,'MEDIA',$data['MediaType'],NULL,$data['idPallet'],NULL,$data['MediaID']);
				if($SNS_Message_cus['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Media';
					return json_encode($json);
				}
				//End send Custody SNS

				//Start calling SNS function
				if($data['StatusID'] == '1') { //IF current status is Pending Degauss
					$message = '
					{
						"eventType": "SINGLE_MEDIA_DRIVE_DEGAUSSED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media":
							{
							"serial": "'.$data['MediaSerialNumber'].'",
							"type": "'.$data['MediaType'].'",
							"binId": "'.$to_cp['BinName'].'",
							"timestamp": "'.time().'"
							}
						}
					}';
					$event_type = "SINGLE_MEDIA_DRIVE_DEGAUSSED";
				} else if($data['StatusID'] == '2') { //IF current status is Pending Degauss
					$message = '
					{
						"eventType": "SINGLE_MEDIA_DRIVE_DESTROYED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media":
							{
							"serial": "'.$data['MediaSerialNumber'].'",
							"type": "'.$data['MediaType'].'",
							"binId": "'.$to_cp['MobilityName'].'",
							"timestamp": "'.time().'"
							}
						}
					}';
					$event_type = "SINGLE_MEDIA_DRIVE_DESTROYED";
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				sleep(2);
				$SNS_Message = $this->SendSNSMessage($message,$data['MediaSerialNumber'],$event_type,'MEDIA',$data['MediaType'],$to_cp['BinName'],$data['idPallet'],NULL,$data['MediaID']);
				if($SNS_Message['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'SNS Message Failed, Holding on Processing Media';
					return json_encode($json);
				}

				//End calling SNS function

				//Start update Media SN
				$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Media SN

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Start insert into media process
				$data['OriSerialNumber'] = $data['ServerSerialNumber'];
				$data['ServerSerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $data['ServerSerialNumber']);
				$query5 = "insert into speed_media_process (MediaID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,from_disposition_id,to_disposition_id,from_status,to_status,from_CustomPalletID,to_CustomPalletID,AuditControllerID,CreatedDate,CreatedBy,ProcessType,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$from_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$from_status)."','".mysqli_real_escape_string($this->connectionlink,$to_status)."','".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','".$data['OriSerialNumber']."')";
				$q5 = mysqli_query($this->connectionlink,$query5);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink).$query5;
					return json_encode($json);
				}
				//End insert into media process

				//Start enter media tracking
				$action = $data['MediaType']." Processed manually in Pending Media screen, moved from BIN (".$from_cp['BinName'].") to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
				$query66 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q66 = mysqli_query($this->connectionlink,$query66);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End enter media tracking

				$json['Success'] = true;
				$json['Result'] = "Media Process Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Next BIN ID";
				return json_encode($json);
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetAllNextBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Destruction')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Destruction Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destruction_disposition from custompallet c
			left join disposition  d on  c.disposition_id = d.disposition_id
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and d.destruction_disposition = 1 order by c.BinName ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No BINs Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

  public function SelectWorkstationDestruction($data)
  {
    if (!isset($_SESSION['user'])) {
      $json['Success'] = false;
      $json['Result'] = 'Login to continue';
      return json_encode($json);
    }
    $json = array(
      'Success' => false,
      'Result' => ''
    );
    $sql = "Select SiteID, SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' order by SiteName";

    //$sql = "select SiteID,SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' and (Locked = '0' or LockedForUser = '".$_SESSION['user']['UserId']."') and order by SiteName";

    $query = mysqli_query($this->connectionlink, $sql);
    if (mysqli_error($this->connectionlink)) {
      $json['Success'] = false;
      $json['Result'] = mysqli_error($this->connectionlink);
    } else {
      if (mysqli_affected_rows($this->connectionlink) > 0) {
        $i = 0;
        while ($row = mysqli_fetch_assoc($query)) {
          $result[$i] = $row;
          $i++;
        }
        $json['Success'] = true;
        $json['Result'] = $result;
      } else {
        $json['Success'] = false;
        $json['Result'] = 'No Results';
      }
    }
    return json_encode($json);
  }


}
?>
