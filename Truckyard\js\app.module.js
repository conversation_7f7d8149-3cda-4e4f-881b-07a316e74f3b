(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
         'app.core'

        // Custom Feature modules
        ,'app.ui'
        ,'app.ui.form'
        ,'app.ui.form.validation'


        // 3rd party feature modules
        ,'md.data.table'
        ,'global'
        ,'angularFileUpload'
        ,'angularMoment'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
            function($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('Truck', {
                    url: '/Truck',
                    templateUrl: "templates/Truck.html"
                })
                .state('Truck/:TruckID', {
                    url: '/Truck/:TruckID',
                    templateUrl: "templates/Truck.html"
                })
                .state('TruckList', {
                    url: '/TruckList',
                    templateUrl: "templates/TruckList.html"
                })
                .state('TruckBookingCalender', {
                    url: '/TruckBookingCalender',
                    templateUrl: "templates/TruckBookingCalender.html"
                })
                .state('TrailerDockRelease', {
                    url: '/TrailerDockRelease',
                    templateUrl: "templates/TrailerDockRelease.html"
                })

                .state('CompleteNoShowTrucks', {
                    url: '/CompleteNoShowTrucks',
                    templateUrl: "templates/CompleteNoShowTrucks.html"
                })

            $urlRouterProvider
                .when('/', '/Truck')
                .otherwise('/Truck');
        }
    ]);


module.controller("Truck", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,UserFacility,$upload) {
    $rootScope.$broadcast('preloader:active');
    jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=CheckIfPagePermission&Page=Truck Booking',
        success: function (data) {
            $rootScope.$broadcast('preloader:hide');
            if (data.Success) {                
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-info md-block')
                );  
                window.location = host;             
            }
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.error = data;
            initSessionTime(); $scope.$apply();
        }
    });

    var today = new Date();
    var adjusteddate = today.toISOString().substring(0, 10);
    $scope.Truck = {'ArrivalDate': adjusteddate};

    $scope.Facilities = [];
    $scope.ParkTypes = [];
    $scope.ParkingLocations = [];
    $scope.FilteredParkingLocations = [];
    $scope.Carriers = [];
    $scope.TruckTypes = [];
    $scope.timeSlots = [];
    $scope.filteredTimeSlots = [];

    // Date and time validation
    $scope.minDate = new Date(); // Set minimum date to today
    $scope.showDateError = false;
    $scope.showTimeError = false;

    // Vehicle identification validation
    $scope.showVehicleIdentificationError = false;

    $scope.validateVehicleIdentification = function() {
        var truckReg = ($scope.Truck && $scope.Truck.TruckReg) ? $scope.Truck.TruckReg.trim() : '';
        var trailerNumber = ($scope.Truck && $scope.Truck.TrailerNumber) ? $scope.Truck.TrailerNumber.trim() : '';

        // Show error if both fields are empty
        $scope.showVehicleIdentificationError = (truckReg === '' && trailerNumber === '');

        return !$scope.showVehicleIdentificationError;
    };

    // Initialize validation on page load
    $scope.$watch('Truck', function() {
        if ($scope.Truck) {
            $scope.validateVehicleIdentification();
        }
    }, true);

    // Generate 30-minute time slots
    $scope.generateTimeSlots = function() {
        $scope.timeSlots = [];
        for (var hour = 0; hour < 24; hour++) {
            for (var minute = 0; minute < 60; minute += 30) {
                var timeValue = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0') + ':00';
                var displayTime = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0');
                $scope.timeSlots.push({
                    value: timeValue,
                    display: displayTime
                });
            }
        }
    };

    // Initialize time slots
    $scope.generateTimeSlots();

    // Date validation function
    $scope.onDateChange = function() {
        $scope.showDateError = false;

        if ($scope.Truck.ArrivalDate) {
            var selectedDate = new Date($scope.Truck.ArrivalDate);
            var today = new Date();

            // Set time to start of day for comparison
            selectedDate.setHours(0, 0, 0, 0);
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                $scope.showDateError = true;
                $scope.Truck.ArrivalDate = null; // Clear invalid date
                return;
            }
        }

        // Update filtered time slots when date changes
        $scope.updateFilteredTimeSlots();
    };

    // Time validation function
    $scope.onTimeChange = function() {
        $scope.showTimeError = false;

        if ($scope.Truck.ArrivalDate && $scope.Truck.ArrivalTime) {
            var selectedDate = new Date($scope.Truck.ArrivalDate);
            var today = new Date();

            // Set dates to start of day for comparison
            selectedDate.setHours(0, 0, 0, 0);
            today.setHours(0, 0, 0, 0);

            // If selected date is today, check if time is in the past
            if (selectedDate.getTime() === today.getTime()) {
                var currentTime = new Date();
                var selectedTime = $scope.Truck.ArrivalTime.split(':');
                var selectedDateTime = new Date();
                selectedDateTime.setHours(parseInt(selectedTime[0]), parseInt(selectedTime[1]), parseInt(selectedTime[2] || 0), 0);

                if (selectedDateTime <= currentTime) {
                    $scope.showTimeError = true;
                    $scope.Truck.ArrivalTime = null; // Clear invalid time
                    return;
                }
            }
        }
    };

    // Update filtered time slots based on selected date
    $scope.updateFilteredTimeSlots = function() {
        if (!$scope.Truck.ArrivalDate) {
            $scope.filteredTimeSlots = $scope.timeSlots;
            return;
        }

        var selectedDate = new Date($scope.Truck.ArrivalDate);
        var today = new Date();

        // Set dates to start of day for comparison
        selectedDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        // If selected date is today, filter out past times
        if (selectedDate.getTime() === today.getTime()) {
            var currentTime = new Date();
            $scope.filteredTimeSlots = $scope.timeSlots.filter(function(timeSlot) {
                var timeParts = timeSlot.value.split(':');
                var slotDateTime = new Date();
                slotDateTime.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]), parseInt(timeParts[2] || 0), 0);
                return slotDateTime > currentTime;
            });
        } else {
            // For future dates, show all time slots
            $scope.filteredTimeSlots = $scope.timeSlots;
        }

        // Clear selected time if it's no longer available
        if ($scope.Truck.ArrivalTime) {
            var isTimeAvailable = $scope.filteredTimeSlots.some(function(slot) {
                return slot.value === $scope.Truck.ArrivalTime;
            });
            if (!isTimeAvailable) {
                $scope.Truck.ArrivalTime = null;
            }
        }
    };

    // Initialize filtered time slots
    $scope.updateFilteredTimeSlots();

    // Watch for changes in arrival date to update time slots
    $scope.$watch('Truck.ArrivalDate', function(newVal, oldVal) {
        if (newVal !== oldVal) {
            $scope.updateFilteredTimeSlots();
        }
    });

    // Handle park type change
    $scope.onParkTypeChange = function() {
        // Clear parking location when park type changes
        $scope.Truck.ParkingLocationID = '';
        $scope.FilteredParkingLocations = [];

        if ($scope.Truck.ParkTypeID) {
            $scope.loadParkingLocations();
        }
    };

    // Load parking locations for selected park type (used for editing existing records)
    $scope.loadParkingLocationsForEdit = function() {
        if ($scope.Truck.ParkTypeID) {
            $scope.loadParkingLocations();
        }
    };

    // Common function to load parking locations
    $scope.loadParkingLocations = function() {
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkingLocations&ParkTypeID=' + $scope.Truck.ParkTypeID,
            success: function (data) {
                if (data.Success) {
                    $scope.FilteredParkingLocations = data.Result;
                } else {
                    $scope.FilteredParkingLocations = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.FilteredParkingLocations = [];
                initSessionTime(); $scope.$apply();
            }
        });
    };

    UserFacility.async().then(function (d) { //2. so you can use .then()
        $scope.UserFacility = d.data;
        $scope.Truck.FacilityID = $scope.UserFacility;
    });
    jQuery.ajax({
        url: host + 'shipping/includes/shipping_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetFacilities',
        success: function (data) {
            if (data.Success) {
                $scope.Facilities = data.Result;
            } else {
                $scope.Facilities = [];
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }
    });

    jQuery.ajax({
        url: host+'Truckyard/includes/Truck_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetParkTypes',
        success: function (data) {
            if (data.Success) {
                $scope.ParkTypes = data.Result;
            } else {
                $scope.ParkTypes = [];
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }
    });


    jQuery.ajax({
        url: host+'Truckyard/includes/Truck_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetCarriers',
        success: function (data) {
            if (data.Success) {
                $scope.Carriers = data.Result;
            } else {
                $scope.Carriers = [];
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }
    });

    jQuery.ajax({
        url: host+'Truckyard/includes/Truck_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetTruckTypes',
        success: function (data) {
            if (data.Success) {
                $scope.TruckTypes = data.Result;
            } else {
                $scope.TruckTypes = [];
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }
    });

   /* $scope.onCarrierChange = function () {
        // Always populate WasteCollectionPermit from Carrier
        var selectedCarrier = $scope.Carriers.find(function (c) {
            return c.CarrierID === $scope.Truck.CarrierID;
        });

        if (selectedCarrier && selectedCarrier.WasteCollectionPermit) {
            $scope.Truck.WasteCollectionPermit = selectedCarrier.WasteCollectionPermit;
        } else {
            $scope.Truck.WasteCollectionPermit = '';
        }
    };*/

    $scope.onCarrierChange = function () {
        const selected = $scope.Carriers.find(c => c.CarrierID === $scope.Truck.CarrierID);
        $scope.selectedCarrier = selected || null;

        // Auto-fill Waste Collection Permit
        if (selected && selected.WasteCollectionPermit) {
            $scope.Truck.WasteCollectionPermit = selected.WasteCollectionPermit;
        } else {
            $scope.Truck.WasteCollectionPermit = '';
        }

        // Clear WEEE if selected and carrier is not eligible (WasteCollectionEligible != 1)
        if ($scope.Truck.ClassificationType === 'WEEE' && (!selected || selected.WasteCollectionEligible != 1)) {
            $scope.Truck.ClassificationType = null;
        }
    };



    $scope.TruckSave = function () {
        // Validate vehicle identification before saving
        if (!$scope.validateVehicleIdentification()) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please provide either Truck Registration or Trailer Number.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
            );
            return;
        }

        // Validate date and time before saving
        if ($scope.Truck.ArrivalDate) {
            var selectedDate = new Date($scope.Truck.ArrivalDate);
            var today = new Date();

            // Set time to start of day for comparison
            selectedDate.setHours(0, 0, 0, 0);
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Past dates are not allowed. Please select today\'s date or a future date.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            // If date is today, validate time is not in the past
            if ($scope.Truck.ArrivalTime && selectedDate.getTime() === today.getTime()) {
                var currentTime = new Date();
                var selectedTime = $scope.Truck.ArrivalTime.split(':');
                var selectedDateTime = new Date();
                selectedDateTime.setHours(parseInt(selectedTime[0]), parseInt(selectedTime[1]), parseInt(selectedTime[2] || 0), 0);

                if (selectedDateTime <= currentTime) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Past times are not allowed for today\'s date. Please select a future time.')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    return;
                }
            }
        }

        $scope.Truck.busy = true;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=TruckSave&' + $.param($scope.Truck),
            success: function (data) {
                /*console.log('sssssss');
                console.log( data.Result);
                console.log('sssssss');*/
                $rootScope.$broadcast('preloader:hide');
                $scope.Truck.busy = false;
                if (data.Success) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                    );
                    window.location = "#!/TruckList";
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );

                    var op = data.Result.split(' ');                            
                    if( op[0] == "No" && op[1] == 'Access') {
                        window.location = host;
                    }
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.Truck.busy = false;
                alert('error');
                initSessionTime(); $scope.$apply();
            }
        });
    };

    if ($stateParams.TruckID) {
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetTruckDetails&TruckID=' + $stateParams.TruckID,
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.data = data;
                if (data.Success) {
                    $scope.Truck = data.Result;
                        if(data.Result.ArrivalTime != '') {
                            // For the new dropdown, we just need the time string in HH:MM:SS format
                            $scope.Truck.ArrivalTime = data.Result.ArrivalTime;
                        }
                        // Initialize vehicle identification validation for existing data
                        $scope.validateVehicleIdentification();

                        // Update filtered time slots for existing data
                        $scope.updateFilteredTimeSlots();

                        // Load parking locations for the selected park type (without clearing ParkingLocationID)
                        if ($scope.Truck.ParkTypeID) {
                            $scope.loadParkingLocationsForEdit();
                        }
                } else {

                    $scope.TruckID = {};
                    var op = data.Result.split(' ');                            
                    if( op[0] == "No" && op[1] == 'Access') {
                        window.location = host;
                    }
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });
    }


    $scope.onFileSelect = function($files) {
        if($("#TruckBookingFile").val() != '') {
            //$files: an array of files selected, each file has name, size, and type.
            $rootScope.$broadcast('preloader:active');
            $scope.busy = true;
            for (var i = 0; i < $files.length; i++) {
                var file = $files[i];
                $scope.upload = $upload.upload({
                //url: host+'administration/includes/admin_extended_submit.php',
                url: host+'Truckyard/includes/Truck_submit.php',
                data: {ajax: 'UploadTruckBookingFile'},
                file: file, // or list of files ($files) for html5 only
                }).success(function(data, status, headers, config) {
                $rootScope.$broadcast('preloader:hide');
                $scope.busy = false;
                if(data.Success) {
                    $("#TruckBookingFile").val('');
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                    );
                    $scope.CallServerFunction(0);
                       window.location = "templates/UploadDataExport.php";
                    //location.reload();
                } else {
                    $("#TruckBookingFile").val('');
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
                // file is uploaded successfully
                }).error(function(data, status, headers, config) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    alert(data);
                });
            }
        }
    };
});

module.controller("TruckList", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog,$upload) {
    $rootScope.$broadcast('preloader:active');
    jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=CheckIfPagePermission&Page=Truck Booking',
        success: function (data) {
            $rootScope.$broadcast('preloader:hide');
            if (data.Success) {                
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-info md-block')
                );  
                window.location = host;             
            }
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.error = data;
            initSessionTime(); $scope.$apply();
        }
    });

    $scope.ChangeStatus = function(product){
        // Store original status for rollback if validation fails
        var originalStatus = product.OriginalStatus || product.Status;

        // Validation: Check if status change is allowed
        var validationResult = $scope.validateStatusChange(originalStatus, product.Status);
        if (!validationResult.valid) {
            // Rollback to original status
            product.Status = originalStatus;

            $mdToast.show (
                $mdToast.simple()
                .content(validationResult.message)
                .action('OK')
                .position('right')
                .hideDelay(5000)
                .toastClass('md-toast-danger md-block')
            );
            $scope.$apply();
            return;
        }

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=ChangeStatus&TruckID='+product.TruckID+'&Status='+product.Status+'&OriginalStatus='+originalStatus,
            success: function(data){
                if(data.Success == true){
                    // Update the original status to the new status for future validations
                    product.OriginalStatus = product.Status;

                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(5000)
                        .toastClass('md-toast-success md-block')
                    );

                    // Just refresh the list after status change
                    $scope.CallServerFunction($scope.currentPage);
                }
                else{
                    // Rollback to original status on server error
                    product.Status = originalStatus;

                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(5000)
                        .toastClass('md-toast-danger md-block')
                    );
                }

                $scope.$apply();
            }, error : function (data) {
                // Rollback to original status on network error
                product.Status = originalStatus;
                $scope.data = data;
                $scope.$apply();
            }
        });
    };

    // Handle status cell click for redirect to TrailerDockRelease
    $scope.onStatusCellClick = function(product) {
        var status = product.Status.toLowerCase();

        // Only allow clicking on Arrived and In Progress statuses
        if (status !== 'arrived' && status !== 'in progress') {
            $mdToast.show(
                $mdToast.simple()
                    .content('Only "Arrived" and "In Progress" trucks can be processed. Current status: ' + product.Status)
                    .action('OK')
                    .position('right')
                    .hideDelay(4000)
                    .toastClass('md-toast-warning md-block')
            );
            return;
        }

        // Store the truck data for TrailerDockRelease page
        var vehicleData = {
            TruckReg: product.TruckReg || '',
            TrailerNumber: product.TrailerNumber || '',
            autoSearch: true
        };

        // Store in sessionStorage to pass to TrailerDockRelease
        sessionStorage.setItem('vehicleSearchData', JSON.stringify(vehicleData));

        // Show redirect info toast
        $mdToast.show(
            $mdToast.simple()
                .content('Redirecting to Trailer Dock/Release page...')
                .action('OK')
                .position('right')
                .hideDelay(2000)
                .toastClass('md-toast-info md-block')
        );

        // Navigate to TrailerDockRelease page
        $location.path('/TrailerDockRelease');
    };

    // Get cursor style for status cells
    $scope.getStatusCellCursor = function(status) {
        var statusLower = status.toLowerCase();
        // Only Arrived and In Progress are clickable
        if (statusLower === 'arrived' || statusLower === 'in progress') {
            return 'cursor: pointer;';
        }
        return 'cursor: default;';
    };

    // Validation function for status changes
    $scope.validateStatusChange = function(originalStatus, newStatus) {
        // If status hasn't changed, allow it
        if (originalStatus === newStatus) {
            return { valid: true };
        }

        // Define allowed transitions
        var allowedTransitions = {
            'Requested': ['Arrived', 'No Show'],
            'Reserved': ['Arrived', 'No Show']
        };

        // Check if the original status allows the new status
        if (allowedTransitions[originalStatus] && allowedTransitions[originalStatus].includes(newStatus)) {
            return { valid: true };
        }

        // Generate appropriate error message
        var message = '';
        if (newStatus === 'No Show') {
            message = 'No Show status can only be set for bookings with "Requested" or "Reserved" status. Current status: ' + originalStatus;
        } else if (newStatus === 'Arrived') {
            message = 'Arrived status can only be set for bookings with "Requested" or "Reserved" status. Current status: ' + originalStatus;
        } else {
            message = 'Status change from "' + originalStatus + '" to "' + newStatus + '" is not allowed.';
        }

        return {
            valid: false,
            message: message
        };
    };

    // Function to get status CSS class for color coding
    $scope.getStatusClass = function(status) {
        if (!status) return '';

        switch (status.toLowerCase()) {
            case 'complete': return 'complete';
            case 'no show': return 'noshow';
            case 'failed': return 'failed';
            case 'reserved': return 'reserved';
            case 'requested': return 'requested';
            case 'in progress': return 'inprogress';
            case 'arrived': return 'arrived';
            default: return '';
        }
    };

       $scope.busy = false;
       $scope.TruckList = [];
       $scope.pagedItems = [];
       $scope.CurrentStatus = 'Active';

       //Start Pagination Logic
       $scope.itemsPerPage = 20;
       $scope.currentPage = 0;
       $scope.OrderBy = '';
       $scope.OrderByType = '';
       $scope.filter_text = [{}];
       $scope.range = function() {
           var rangeSize = 10;
           var ret = [];
           var start;
           start = $scope.currentPage;
           if ( start > $scope.pageCount()-rangeSize ) {
               start = $scope.pageCount()-rangeSize;
           }
           for (var i=start; i<start+rangeSize; i++) {
               ret.push(i);
           }
           return ret;
       };
       $scope.prevPage = function() {
           if ($scope.currentPage > 0) {
               $scope.currentPage--;
           }
       };
       $scope.firstPage = function () {
           $scope.currentPage = 0;
       };
       $scope.prevPageDisabled = function() {
           return $scope.currentPage === 0 ? "disabled" : "";
       };
       $scope.nextPage = function() {
           if ($scope.currentPage < $scope.pageCount() - 1) {
               $scope.currentPage++;
           }
       };
       $scope.lastPage = function() {
           $scope.currentPage =  $scope.pageCount() - 1;
       };
       $scope.nextPageDisabled = function() {
           return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
       };
       $scope.pageCount = function() {
           return Math.ceil($scope.total/$scope.itemsPerPage);
       };
       $scope.setPage = function(n) {
           if (n >= 0 && n < $scope.pageCount()) {
               $scope.currentPage = n;
           }
       };
       $scope.CallServerFunction = function (newValue) {
           if($scope.CurrentStatus != '' )  {
               $scope.busy = true;
               $rootScope.$broadcast('preloader:active');
               jQuery.ajax({
                   url: host+'Truckyard/includes/Truck_submit.php',
                   dataType: 'json',
                   type: 'post',
                   data: 'ajax=GetTruckList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                   success: function(data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       if(data.Success) {
                           $scope.pagedItems = data.Result;
                           data.Result.forEach(function(truck) {
                            // Format ArrivalTime
                            if (truck.ArrivalTime) {
                                var arrivalParts = truck.ArrivalTime.split(':');
                                var arrivalDate = new Date(1970, 0, 1, parseInt(arrivalParts[0]), parseInt(arrivalParts[1]), parseInt(arrivalParts[2] || 0));
                                truck.ArrivalTimeFormatted = arrivalDate.toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });

                                // Calculate and format DepartureTime (+30 mins)
                                var departureDate = new Date(arrivalDate.getTime() + 30 * 60000); // 30 mins later
                                truck.DepartureTimeFormatted = departureDate.toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });
                            } else {
                                truck.ArrivalTimeFormatted = '';
                                truck.DepartureTimeFormatted = '';
                            }

                            // Store original status for validation
                            truck.OriginalStatus = truck.Status;
                        });

                           if(data.total) {
                               $scope.total = data.total;
                           }
                       } else {
                           $mdToast.show(
                               $mdToast.simple()
                                   .content(data.Result)
                                   .position('right')
                                   .hideDelay(3000)
                           );

                           var op = data.Result.split(' ');                            
                            if( op[0] == "No" && op[1] == 'Access') {
                                window.location = host;
                            }
                       }
                       initSessionTime(); $scope.$apply();
                   }, error : function (data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       alert(data.Result);
                       $scope.error = data;
                       initSessionTime(); $scope.$apply();
                   }
               });
           }
       };
       $scope.$watch("currentPage", function(newValue, oldValue) {
           $scope.CallServerFunction(newValue);
       });
       $scope.convertSingle = function (multiarray) {
           var result = {};
           for(var i=0;i<multiarray.length;i++) {
               result[i] = multiarray[i];
           }
           //alert(result);
           return result;
       };
       $scope.MakeOrderBy = function (orderby) {
           $scope.OrderBy = orderby;
           if($scope.OrderByType == 'asc') {
               $scope.OrderByType = 'desc';
           } else {
               $scope.OrderByType = 'asc';
           }
           $scope.busy = true;
           $rootScope.$broadcast('preloader:active');

           jQuery.ajax({
               url: host+'Truckyard/includes/Truck_submit.php',
               dataType: 'json',
               type: 'post',
               data: 'ajax=GetTruckList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
               success: function(data) {
                   $scope.busy = false;
                   $rootScope.$broadcast('preloader:hide');
                   if(data.Success) {
                       $scope.pagedItems = data.Result;
                       data.Result.forEach(function(truck) {
                        // Format ArrivalTime
                        if (truck.ArrivalTime) {
                            var arrivalParts = truck.ArrivalTime.split(':');
                            var arrivalDate = new Date(1970, 0, 1, parseInt(arrivalParts[0]), parseInt(arrivalParts[1]), parseInt(arrivalParts[2] || 0));
                            truck.ArrivalTimeFormatted = arrivalDate.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            });

                            // Calculate and format DepartureTime (+30 mins)
                            var departureDate = new Date(arrivalDate.getTime() + 30 * 60000); // 30 mins later
                            truck.DepartureTimeFormatted = departureDate.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            });
                        } else {
                            truck.ArrivalTimeFormatted = '';
                            truck.DepartureTimeFormatted = '';
                        }

                        // Store original status for validation
                        truck.OriginalStatus = truck.Status;
                    });

                       if(data.total) {
                           $scope.total = data.total;
                       }
                   } else {
                       $mdToast.show(
                           $mdToast.simple()
                               .content(data.Result)
                               .position('right')
                               .hideDelay(3000)
                       );

                       var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                   }
                   initSessionTime(); $scope.$apply();
               }, error : function (data) {
                   $scope.busy = false;
                   $rootScope.$broadcast('preloader:hide');
                   $scope.error = data;
                   initSessionTime(); $scope.$apply();
               }
           });
       };
       $scope.MakeFilter = function () {
           if($scope.currentPage == 0) {
               $scope.CallServerFunction($scope.currentPage);
           } else {
               $scope.currentPage = 0;
           }
       };

       $scope.DeleteTruck = function (id) {
            var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
            $mdDialog.show(confirm).then(function () {
                jQuery.ajax({
                    url: host+'Truckyard/includes/Truck_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteTruck&id='+id,
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            //alert("4");
                            $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }, function () {

        });
    };

     $scope.ExportTruckListxls = function () {
       //alert("1");
       jQuery.ajax({
           url: host+'Truckyard/includes/Truck_submit.php',
           dataType: 'json',
           type: 'post',
           data: 'ajax=GenerateTruckListxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

           success: function(data) {
               if(data.Success) {
                   //alert("2");
                   //console.log(data.Result);
                   window.location="templates/TruckListxls.php";
               } else {
                  // alert("4");
                   $mdToast.show (
                       $mdToast.simple()
                       .content(data.Result)
                       .action('OK')
                       .position('right')
                       .hideDelay(0)
                       .toastClass('md-toast-danger md-block')
                   );

                   var op = data.Result.split(' ');                            
                    if( op[0] == "No" && op[1] == 'Access') {
                        window.location = host;
                    }
               }
               
               initSessionTime(); $scope.$apply();
           }, error : function (data) {
               //alert(data.Result);
               //alert("3");
               $scope.error = data;
               initSessionTime(); $scope.$apply();
           }
       });
   };

   // Truck Tracking History Functions
   $scope.selectedTruck = {};
   $scope.trackingHistory = [];
   $scope.trackingHistoryLoading = false;

   $scope.showTruckTrackingHistory = function(truck) {
       $mdDialog.show({
           templateUrl: 'truckTrackingDialog.html',
           parent: angular.element(document.body),
           clickOutsideToClose: true,
           fullscreen: false,
           controller: function($scope, $mdDialog) {
               $scope.selectedTruck = truck;
               $scope.trackingHistory = [];
               $scope.trackingHistoryLoading = true;

               $scope.closeDialog = function() {
                   $mdDialog.hide();
               };

               // Fetch tracking history
               jQuery.ajax({
                   url: host + 'Truckyard/includes/Truck_submit.php',
                   dataType: 'json',
                   type: 'get',
                   data: 'ajax=GetTruckTrackingHistory&TruckID=' + truck.TruckID,
                   success: function(data) {
                       $scope.trackingHistoryLoading = false;
                       if (data.Success) {
                           $scope.trackingHistory = data.Result;
                       } else {
                           $scope.trackingHistory = [];
                           $mdToast.show(
                               $mdToast.simple()
                                   .content('Error loading truck history: ' + data.Result)
                                   .action('OK')
                                   .position('right')
                                   .hideDelay(3000)
                                   .toastClass('md-toast-danger md-block')
                           );
                       }
                       $scope.$apply();
                   },
                   error: function(xhr, status, error) {
                       $scope.trackingHistoryLoading = false;
                       $scope.trackingHistory = [];
                       $mdToast.show(
                           $mdToast.simple()
                               .content('Error loading truck history')
                               .action('OK')
                               .position('right')
                               .hideDelay(3000)
                               .toastClass('md-toast-danger md-block')
                       );
                       $scope.$apply();
                   }
               });
           }
       });
   };

   // Truck Images Management Functions
   $scope.selectedTruck = {};
   $scope.truckImages = [];
   $scope.imagesLoading = false;
   $scope.imageUploadBusy = false;

   $scope.showTruckImages = function(truck) {
       $mdDialog.show({
           templateUrl: 'truckImagesDialog.html',
           parent: angular.element(document.body),
           clickOutsideToClose: true,
           fullscreen: false,
           controller: function($scope, $mdDialog, $upload) {
               $scope.selectedTruck = truck;
               $scope.truckImages = [];
               $scope.imagesLoading = true;
               $scope.imageUploadBusy = false;

               // Hyperlink management variables
               $scope.truckHyperLink = '';
               $scope.newHyperLink = '';
               $scope.editingHyperLink = false;
               $scope.hyperlinkSaving = false;

               $scope.closeImageDialog = function() {
                   $mdDialog.hide();
               };

               // Load truck images
               $scope.loadTruckImages = function() {
                   $scope.imagesLoading = true;
                   jQuery.ajax({
                       url: host + 'Truckyard/includes/Truck_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=GetTruckImages&TruckID=' + truck.TruckID,
                       success: function(data) {
                           $scope.$evalAsync(function() {
                               $scope.imagesLoading = false;
                               if (data.Success) {
                                   $scope.truckImages = data.Result;
                               } else {
                                   $scope.truckImages = [];
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('Error loading images: ' + data.Result)
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-danger md-block')
                                   );
                               }
                           });
                       },
                       error: function(xhr, status, error) {
                           $scope.$evalAsync(function() {
                               $scope.imagesLoading = false;
                               $scope.truckImages = [];
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content('Error loading images')
                                       .action('OK')
                                       .position('right')
                                       .hideDelay(3000)
                                       .toastClass('md-toast-danger md-block')
                               );
                           });
                       }
                   });
               };

               // Upload image/document
               $scope.onTruckImageSelect = function($files) {
                   if($files && $files.length > 0) {
                       $scope.imageUploadBusy = true;
                       var file = $files[0];
                       $scope.upload = $upload.upload({
                           url: host + 'Truckyard/includes/Truck_submit.php',
                           data: {ajax: 'UploadTruckImage', TruckID: truck.TruckID},
                           file: file
                       }).success(function(data, status, headers, config) {
                           $scope.$evalAsync(function() {
                               $scope.imageUploadBusy = false;
                               if(data.Success) {
                                   $("#TruckImageFile").val('');
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('File uploaded successfully')
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-success md-block')
                                   );
                                   $scope.loadTruckImages(); // Reload images
                               } else {
                                   $("#TruckImageFile").val('');
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content(data.Result)
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-danger md-block')
                                   );
                               }
                           });
                       }).error(function(data, status, headers, config) {
                           $scope.$evalAsync(function() {
                               $scope.imageUploadBusy = false;
                               $("#TruckImageFile").val('');
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content('Upload failed')
                                       .action('OK')
                                       .position('right')
                                       .hideDelay(3000)
                                       .toastClass('md-toast-danger md-block')
                               );
                           });
                       });
                   }
               };

               // Delete image
               $scope.deleteImage = function(image) {
                   var confirm = $mdDialog.confirm()
                       .title('Delete File')
                       .textContent('Are you sure you want to delete this file?')
                       .ariaLabel('Delete file')
                       .ok('Delete')
                       .cancel('Cancel');

                   $mdDialog.show(confirm).then(function() {
                       jQuery.ajax({
                           url: host + 'Truckyard/includes/Truck_submit.php',
                           dataType: 'json',
                           type: 'post',
                           data: 'ajax=DeleteTruckImage&ImageID=' + image.ImageID,
                           success: function(data) {
                               $scope.$evalAsync(function() {
                                   if (data.Success) {
                                       $mdToast.show(
                                           $mdToast.simple()
                                               .content('File deleted successfully')
                                               .action('OK')
                                               .position('right')
                                               .hideDelay(3000)
                                               .toastClass('md-toast-success md-block')
                                       );
                                       $scope.loadTruckImages(); // Reload images
                                   } else {
                                       $mdToast.show(
                                           $mdToast.simple()
                                               .content('Error deleting file: ' + data.Result)
                                               .action('OK')
                                               .position('right')
                                               .hideDelay(3000)
                                               .toastClass('md-toast-danger md-block')
                                       );
                                   }
                               });
                           },
                           error: function(xhr, status, error) {
                               $scope.$evalAsync(function() {
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('Error deleting file')
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-danger md-block')
                                   );
                               });
                           }
                       });
                   });
               };

               // Download image
               $scope.downloadImage = function(image) {
                   window.open(host + 'download_s3.php?key=' + encodeURIComponent(image.ImagePath), '_blank');
               };

               // Helper functions
               $scope.isImageFile = function(filename) {
                   if (!filename) return false;
                   var ext = filename.toLowerCase().split('.').pop();
                   return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(ext) !== -1;
               };

               $scope.getFileName = function(path) {
                   if (!path) return '';
                   return path.split('/').pop();
               };

               // Check if file is printable
               $scope.isPrintable = function(filename) {
                   if (!filename) return false;
                   var ext = filename.toLowerCase().split('.').pop();
                   return ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'txt', 'doc', 'docx'].indexOf(ext) !== -1;
               };

               // Print image/document
               $scope.printImage = function(image) {
                   var filename = $scope.getFileName(image.ImagePath);
                   var ext = filename.toLowerCase().split('.').pop();

                   // Create a new window for printing
                   var printWindow = window.open('', '_blank', 'width=800,height=600');

                   if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(ext) !== -1) {
                       // For images, create a simple HTML page with the image
                       printWindow.document.write(`
                           <html>
                               <head>
                                   <title>Print: ${filename}</title>
                                   <style>
                                       body { margin: 0; padding: 20px; text-align: center; }
                                       img { max-width: 100%; height: auto; }
                                       @media print {
                                           body { margin: 0; padding: 0; }
                                           img { max-width: 100%; page-break-inside: avoid; }
                                       }
                                   </style>
                               </head>
                               <body>
                                   <img src="${host}download_s3.php?key=${encodeURIComponent(image.ImagePath)}" onload="window.print(); window.close();" />
                               </body>
                           </html>
                       `);
                   } else if (ext === 'pdf') {
                       // For PDFs, embed in iframe and print
                       printWindow.document.write(`
                           <html>
                               <head>
                                   <title>Print: ${filename}</title>
                                   <style>
                                       body { margin: 0; padding: 0; }
                                       iframe { width: 100%; height: 100vh; border: none; }
                                   </style>
                               </head>
                               <body>
                                   <iframe src="${host}download_s3.php?key=${encodeURIComponent(image.ImagePath)}" onload="setTimeout(function(){ window.print(); }, 1000);"></iframe>
                               </body>
                           </html>
                       `);
                   } else {
                       // For other file types, try to display content
                       printWindow.location.href = host + 'download_s3.php?key=' + encodeURIComponent(image.ImagePath);
                       setTimeout(function() {
                           printWindow.print();
                       }, 1000);
                   }

                   printWindow.document.close();
               };

               // Load truck hyperlink
               $scope.loadTruckHyperLink = function() {
                   jQuery.ajax({
                       url: host + 'Truckyard/includes/Truck_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=GetTruckHyperLink&TruckID=' + truck.TruckID,
                       success: function(data) {
                           $scope.$evalAsync(function() {
                               if (data.Success && data.Result) {
                                   $scope.truckHyperLink = data.Result;
                               } else {
                                   $scope.truckHyperLink = '';
                               }
                           });
                       },
                       error: function(xhr, status, error) {
                           $scope.$evalAsync(function() {
                               $scope.truckHyperLink = '';
                           });
                       }
                   });
               };

               // Save/Update hyperlink
               $scope.saveHyperLink = function() {
                   if (!$scope.newHyperLink) return;

                   $scope.hyperlinkSaving = true;
                   jQuery.ajax({
                       url: host + 'Truckyard/includes/Truck_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=SaveTruckHyperLink&TruckID=' + truck.TruckID + '&HyperLink=' + encodeURIComponent($scope.newHyperLink),
                       success: function(data) {
                           $scope.$evalAsync(function() {
                               $scope.hyperlinkSaving = false;
                               if (data.Success) {
                                   $scope.truckHyperLink = $scope.newHyperLink;
                                   $scope.newHyperLink = '';
                                   $scope.editingHyperLink = false;
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('Hyperlink saved successfully')
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-success md-block')
                                   );
                               } else {
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('Error saving hyperlink: ' + data.Result)
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-danger md-block')
                                   );
                               }
                           });
                       },
                       error: function(xhr, status, error) {
                           $scope.$evalAsync(function() {
                               $scope.hyperlinkSaving = false;
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content('Error saving hyperlink')
                                       .action('OK')
                                       .position('right')
                                       .hideDelay(3000)
                                       .toastClass('md-toast-danger md-block')
                               );
                           });
                       }
                   });
               };

               // Edit hyperlink
               $scope.editHyperLink = function() {
                   $scope.newHyperLink = $scope.truckHyperLink;
                   $scope.editingHyperLink = true;
               };

               // Cancel hyperlink edit
               $scope.cancelHyperLinkEdit = function() {
                   $scope.newHyperLink = '';
                   $scope.editingHyperLink = false;
               };

               // Delete hyperlink
               $scope.deleteHyperLink = function() {
                   var confirm = $mdDialog.confirm()
                       .title('Delete Hyperlink')
                       .textContent('Are you sure you want to delete this hyperlink?')
                       .ariaLabel('Delete hyperlink')
                       .ok('Delete')
                       .cancel('Cancel');

                   $mdDialog.show(confirm).then(function() {
                       jQuery.ajax({
                           url: host + 'Truckyard/includes/Truck_submit.php',
                           dataType: 'json',
                           type: 'post',
                           data: 'ajax=DeleteTruckHyperLink&TruckID=' + truck.TruckID,
                           success: function(data) {
                               $scope.$evalAsync(function() {
                                   if (data.Success) {
                                       $scope.truckHyperLink = '';
                                       $scope.newHyperLink = '';
                                       $scope.editingHyperLink = false;
                                       $mdToast.show(
                                           $mdToast.simple()
                                               .content('Hyperlink deleted successfully')
                                               .action('OK')
                                               .position('right')
                                               .hideDelay(3000)
                                               .toastClass('md-toast-success md-block')
                                       );
                                   } else {
                                       $mdToast.show(
                                           $mdToast.simple()
                                               .content('Error deleting hyperlink: ' + data.Result)
                                               .action('OK')
                                               .position('right')
                                               .hideDelay(3000)
                                               .toastClass('md-toast-danger md-block')
                                       );
                                   }
                               });
                           },
                           error: function(xhr, status, error) {
                               $scope.$evalAsync(function() {
                                   $mdToast.show(
                                       $mdToast.simple()
                                           .content('Error deleting hyperlink')
                                           .action('OK')
                                           .position('right')
                                           .hideDelay(3000)
                                           .toastClass('md-toast-danger md-block')
                                   );
                               });
                           }
                       });
                   });
               };

               // Load images and hyperlink on modal open
               $scope.loadTruckImages();
               $scope.loadTruckHyperLink();
           }
       });
   };

       //End Pagination Logic
   });

module.controller("TruckBookingCalender", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,UserFacility) {
    $rootScope.$broadcast('preloader:active');
    jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=CheckIfPagePermission&Page=Truck Booking Calender',
        success: function (data) {
            $rootScope.$broadcast('preloader:hide');
            if (data.Success) {
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-info md-block')
                );
                window.location = host;
            }
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.error = data;
            initSessionTime(); $scope.$apply();
        }
    });

    // Initialize variables
    var today = new Date();
    $scope.Calendar = {
        SelectedDate: today,
        ParkTypeID: ''
    };

    $scope.ParkTypes = [];
    $scope.ParkingLocations = [];
    $scope.TimeSlots = [];
    $scope.CalendarData = {};
    $scope.isLoading = false;

    // Generate 30-minute time slots
    $scope.generateTimeSlots = function() {
        $scope.TimeSlots = [];
        for (var hour = 0; hour < 24; hour++) {
            for (var minute = 0; minute < 60; minute += 30) {
                var timeValue = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0') + ':00';
                var displayTime = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0');
                $scope.TimeSlots.push({
                    value: timeValue,
                    display: displayTime
                });
            }
        }
    };

    // Initialize time slots
    $scope.generateTimeSlots();

    // Load Park Types
    jQuery.ajax({
        url: host+'Truckyard/includes/Truck_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetParkTypes',
        success: function (data) {
            if (data.Success) {
                $scope.ParkTypes = data.Result;
            } else {
                $scope.ParkTypes = [];
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }
    });

    // Handle park type change
    $scope.onParkTypeChange = function() {
        if ($scope.Calendar.ParkTypeID) {
            $scope.loadParkingLocations();
        } else {
            $scope.ParkingLocations = [];
            $scope.CalendarData = {};
        }
    };

    // Handle date change
    $scope.onDateChange = function() {
        if ($scope.Calendar.SelectedDate && $scope.Calendar.ParkTypeID) {
            $scope.loadCalendarData();
        }
    };

    // Load parking locations for selected park type
    $scope.loadParkingLocations = function() {
        $scope.isLoading = true;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkingLocations&ParkTypeID=' + $scope.Calendar.ParkTypeID,
            success: function (data) {
                if (data.Success) {
                    $scope.ParkingLocations = data.Result;
                    // Load calendar data if date is also selected
                    if ($scope.Calendar.SelectedDate) {
                        $scope.loadCalendarData();
                    }
                } else {
                    $scope.ParkingLocations = [];
                    $scope.CalendarData = {};
                }
                $scope.isLoading = false;
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.isLoading = false;
                $rootScope.$broadcast('preloader:hide');
                $scope.ParkingLocations = [];
                $scope.CalendarData = {};
                initSessionTime(); $scope.$apply();
            }
        });
    };

    // Load calendar data for selected date and park type
    $scope.loadCalendarData = function() {
        if (!$scope.Calendar.SelectedDate || !$scope.Calendar.ParkTypeID) {
            return;
        }

        var selectedDate = $scope.Calendar.SelectedDate.toISOString().substring(0, 10);

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCalendarData&Date=' + selectedDate + '&ParkTypeID=' + $scope.Calendar.ParkTypeID,
            success: function (data) {
                if (data.Success) {
                    $scope.CalendarData = data.Result;
                } else {
                    $scope.CalendarData = {};
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.CalendarData = {};
                initSessionTime(); $scope.$apply();
            }
        });
    };

    // Get status class for a specific time slot and location
    $scope.getStatusClass = function(timeValue, locationId) {
        var key = timeValue + '_' + locationId;
        if ($scope.CalendarData[key]) {
            var status = $scope.CalendarData[key].Status.toLowerCase();
            return status.replace(' ', '');
        }
        return '';
    };

    // Get status text for a specific time slot and location
    $scope.getStatusText = function(timeValue, locationId) {
        var key = timeValue + '_' + locationId;
        if ($scope.CalendarData[key]) {
            return $scope.CalendarData[key].Status;
        }
        return '';
    };

    // Get status tooltip for a specific time slot and location
    $scope.getStatusTooltip = function(timeValue, locationId) {
        var key = timeValue + '_' + locationId;
        if ($scope.CalendarData[key]) {
            var booking = $scope.CalendarData[key];
            return 'Truck: ' + (booking.TruckReg || booking.TrailerNumber || 'N/A') +
                   '\nDriver: ' + (booking.DriverName || 'N/A') +
                   '\nCarrier: ' + (booking.CarrierName || 'N/A') +
                   '\nStatus: ' + booking.Status +
                   '\n\nClick to open Trailer Dock/Release';
        }
        return 'Available';
    };

    // Get cursor style for calendar cells
    $scope.getCellCursor = function(timeValue, locationId) {
        var key = timeValue + '_' + locationId;
        if ($scope.CalendarData[key]) {
            var status = $scope.CalendarData[key].Status.toLowerCase();
            // Only Arrived and In Progress are clickable
            if (status === 'arrived' || status === 'in progress') {
                return 'cursor: pointer;';
            }
            return 'cursor: not-allowed;';
        }
        return 'cursor: default;';
    };

    // Handle cell click to redirect to TrailerDockRelease
    $scope.onCellClick = function(timeValue, locationId) {
        var key = timeValue + '_' + locationId;
        if ($scope.CalendarData[key]) {
            var booking = $scope.CalendarData[key];
            var status = booking.Status.toLowerCase();

            // Only allow clicking on Arrived and In Progress bookings
            if (status !== 'arrived' && status !== 'in progress') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Only "Arrived" and "In Progress" bookings can be processed. Current status: ' + booking.Status)
                        .action('OK')
                        .position('right')
                        .hideDelay(4000)
                        .toastClass('md-toast-warning md-block')
                );
                return;
            }

            // Store the booking data for TrailerDockRelease page
            var vehicleData = {
                TruckReg: booking.TruckReg || '',
                TrailerNumber: booking.TrailerNumber || '',
                autoSearch: true
            };

            // Store in sessionStorage to pass to TrailerDockRelease
            sessionStorage.setItem('vehicleSearchData', JSON.stringify(vehicleData));

            // Navigate to TrailerDockRelease page
            $location.path('/TrailerDockRelease');
        }
    };


});

module.controller("TrailerDockRelease", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,UserFacility,$mdDialog) {
    $rootScope.$broadcast('preloader:active');
    jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=CheckIfPagePermission&Page=Trailer Dock/Release',
        success: function (data) {
            $rootScope.$broadcast('preloader:hide');
            if (data.Success) {
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-info md-block')
                );
                window.location = host;
            }
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.error = data;
            initSessionTime(); $scope.$apply();
        }
    });

    // Initialize variables
    $scope.Search = {
        TruckReg: '',
        TrailerNumber: '',
        busy: false
    };

    // Initialize unbooked booking variables
    $scope.ShowUnbookedForm = false;
    $scope.LoadingUnbookedData = false;
    $scope.UnbookedBooking = {
        TruckReg: '',
        TrailerNumber: '',
        DriverName: '',
        DriverID: '',
        FacilityID: '',
        FacilityName: '',
        ParkTypeID: '',
        ParkingLocationID: '',
        CarrierID: '',
        TruckTypeID: '',
        LoadType: '',
        LoadNumber: '',
        LoadQuantity: 1,
        DockLockEngaged: '',
        Notes: '',
        busy: false
    };

    $scope.UnbookedCheckIn = {
        Date: '',
        Time: ''
    };

    $scope.UnbookedParkingLocations = [];
    $scope.showUnbookedVehicleError = false;

    // Initialize dropdown arrays
    $scope.ParkTypes = [];
    $scope.Carriers = [];
    $scope.VehicleTypes = [];

    // Load user facility
    UserFacility.async().then(function (d) {
        $scope.UserFacility = d.data;
        console.log('User facility loaded:', $scope.UserFacility);
    });

    // Load facilities list for facility name lookup
    jQuery.ajax({
        url: host + 'shipping/includes/shipping_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetFacilities',
        success: function (data) {
            if (data.Success) {
                $scope.Facilities = data.Result;
                console.log('Facilities loaded:', $scope.Facilities.length, 'items');
            } else {
                $scope.Facilities = [];
            }
            $scope.$apply();
        },
        error: function (xhr, status, error) {
            console.error('Facilities AJAX Error:', error);
            $scope.Facilities = [];
            $scope.$apply();
        }
    });

    // Load dropdown data for unbooked form
    $scope.loadUnbookedDropdownData = function() {
        console.log('Loading unbooked dropdown data...');
        $scope.LoadingUnbookedData = true;

        // Initialize arrays
        $scope.ParkTypes = [];
        $scope.Carriers = [];
        $scope.VehicleTypes = [];

        // Counter to track completed requests
        var completedRequests = 0;
        var totalRequests = 3;

        function checkAllDataLoaded() {
            completedRequests++;
            console.log('Completed requests:', completedRequests, 'of', totalRequests);
            if (completedRequests >= totalRequests) {
                $scope.LoadingUnbookedData = false;
                $scope.ShowUnbookedForm = true;
                console.log('All dropdown data loaded, showing form');
                $scope.$apply();
            }
        }

        // Load Park Types
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkTypes',
            success: function (data) {
                console.log('ParkTypes response:', data);
                if (data.Success && data.Result) {
                    $scope.ParkTypes = data.Result;
                    console.log('ParkTypes loaded:', $scope.ParkTypes.length, 'items');
                } else {
                    $scope.ParkTypes = [];
                    console.log('No ParkTypes data received');
                }
                checkAllDataLoaded();
            },
            error: function (xhr, status, error) {
                console.error('ParkTypes AJAX Error:', error);
                console.error('Status:', status);
                console.error('Response Text:', xhr.responseText);
                console.error('Response Status:', xhr.status);

                // Try to show first 500 characters of response for debugging
                if (xhr.responseText && xhr.responseText.length > 0) {
                    console.error('Response Preview:', xhr.responseText.substring(0, 500));
                }

                $scope.ParkTypes = [];
                checkAllDataLoaded();
            }
        });

        // Load Carriers
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCarriers',
            success: function (data) {
                console.log('Carriers response:', data);
                if (data.Success && data.Result) {
                    $scope.Carriers = data.Result;
                    console.log('Carriers loaded:', $scope.Carriers.length, 'items');
                } else {
                    $scope.Carriers = [];
                    console.log('No Carriers data received');
                }
                checkAllDataLoaded();
            },
            error: function (xhr, status, error) {
                console.error('Carriers AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                $scope.Carriers = [];
                checkAllDataLoaded();
            }
        });

        // Load Vehicle Types
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetVehicleTypes',
            success: function (data) {
                console.log('VehicleTypes response:', data);
                if (data.Success && data.Result) {
                    $scope.VehicleTypes = data.Result;
                    console.log('VehicleTypes loaded:', $scope.VehicleTypes.length, 'items');
                } else {
                    $scope.VehicleTypes = [];
                    console.log('No VehicleTypes data received');
                }
                checkAllDataLoaded();
            },
            error: function (xhr, status, error) {
                console.error('VehicleTypes AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                $scope.VehicleTypes = [];
                checkAllDataLoaded();
            }
        });
    };

    // Check if we came from calendar with vehicle data
    var vehicleSearchData = sessionStorage.getItem('vehicleSearchData');
    if (vehicleSearchData) {
        try {
            var vehicleData = JSON.parse(vehicleSearchData);
            if (vehicleData.autoSearch) {
                // Populate search fields
                $scope.Search.TruckReg = vehicleData.TruckReg || '';
                $scope.Search.TrailerNumber = vehicleData.TrailerNumber || '';

                // Clear the session storage
                sessionStorage.removeItem('vehicleSearchData');

                // Auto-trigger search after a short delay to ensure page is loaded
                setTimeout(function() {
                    $scope.searchVehicle();
                }, 500);
            }
        } catch (e) {
            console.error('Error parsing vehicle search data:', e);
            sessionStorage.removeItem('vehicleSearchData');
        }
    }

    $scope.Booking = {};
    $scope.CheckIn = {};
    $scope.DockingSteps = [];
    $scope.ReleaseSteps = [];
    $scope.BookingFound = false;
    $scope.BookingProcessed = false;
    $scope.DockingCompleted = false;
    $scope.SearchPerformed = false;
    $scope.showSearchError = false;
    $scope.ProcessingBooking = false;

    // Dropdown data
    $scope.ParkingLocations = [];
    $scope.Carriers = [];
    $scope.VehicleTypes = [];

    // Initialize check-in date and time
    var now = new Date();
    $scope.CheckIn.Date = now.toISOString().substring(0, 10);
    $scope.CheckIn.Time = now.toTimeString().substring(0, 8);

    // Load dropdown data on page load
    $scope.loadDropdownData = function() {
        $rootScope.$broadcast('preloader:active');

        // Load Parking Locations
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkingLocations',
            success: function (data) {
                console.log('Parking Locations Response:', data);
                if (data.Success) {
                    $scope.ParkingLocations = data.Result;
                    console.log('Parking Locations Loaded:', $scope.ParkingLocations);
                } else {
                    console.error('Parking Locations Error:', data.Result);
                }
                $scope.$apply();
            },
            error: function(xhr, status, error) {
                console.error('Parking Locations AJAX Error:', error);
                console.error('Response:', xhr.responseText);
            }
        });

        // Load Carriers
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCarriers',
            success: function (data) {
                console.log('Carriers Response:', data);
                if (data.Success) {
                    $scope.Carriers = data.Result;
                    console.log('Carriers Loaded:', $scope.Carriers);
                } else {
                    console.error('Carriers Error:', data.Result);
                }
                $scope.$apply();
            },
            error: function(xhr, status, error) {
                console.error('Carriers AJAX Error:', error);
                console.error('Response:', xhr.responseText);
            }
        });

        // Load Vehicle Types
        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetTruckTypes',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');

                console.log('Vehicle Types Response:', data);
                if (data.Success) {
                    $scope.VehicleTypes = data.Result;
                    console.log('Vehicle Types Loaded:', $scope.VehicleTypes);
                } else {
                    console.error('Vehicle Types Error:', data.Result);
                }
                $scope.$apply();
            },
            error: function(xhr, status, error) {
                $rootScope.$broadcast('preloader:hide');
                console.error('Vehicle Types AJAX Error:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    };

    // Test connection first
    jQuery.ajax({
        url: host+'Truckyard/includes/Truck_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=TestConnection',
        success: function (data) {
            console.log('Connection Test:', data);
            // Load dropdown data on initialization
            $scope.loadDropdownData();
        },
        error: function(xhr, status, error) {
            console.error('Connection Test Failed:', error);
            console.error('Response:', xhr.responseText);
        }
    });

    // Vehicle search validation
    $scope.validateVehicleSearch = function() {
        var truckReg = ($scope.Search.TruckReg || '').trim();
        var trailerNumber = ($scope.Search.TrailerNumber || '').trim();

        $scope.showSearchError = (truckReg === '' && trailerNumber === '');
        return !$scope.showSearchError;
    };

    // Search for vehicle booking
    $scope.searchVehicle = function() {
        if (!$scope.validateVehicleSearch()) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please enter either Truck Registration or Trailer Number.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
            );
            return;
        }

        $scope.Search.busy = true;
        $scope.SearchPerformed = false;
        $scope.BookingFound = false;
        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SearchActiveBooking&TruckReg=' + encodeURIComponent($scope.Search.TruckReg || '') +
                  '&TrailerNumber=' + encodeURIComponent($scope.Search.TrailerNumber || ''),
            success: function (data) {
                $scope.Search.busy = false;
                $scope.SearchPerformed = true;
                $rootScope.$broadcast('preloader:hide');

                if (data.Success && data.Result) {
                    $scope.BookingFound = true;
                    $scope.Booking = data.Result;

                    // Store original booking data for change tracking
                    $scope.OriginalBooking = angular.copy(data.Result);

                    // Auto-populate dropdown selections based on booking data
                    $scope.Booking.ParkingLocationID = data.Result.ParkingLocationID;
                    $scope.Booking.CarrierID = data.Result.CarrierID;
                    $scope.Booking.TruckTypeID = data.Result.TruckTypeID;

                    // Store the vehicle type name for TDR steps loading
                    $scope.Booking.VehicleType = data.Result.VehicleTypeName;

                    // Check if booking is already processed
                    if (data.Result.BookingProcessed) {
                        $scope.BookingProcessed = true;
                        $scope.Booking.DockingCompleted = data.Result.DockingCompleted;
                        $scope.Booking.ReleaseCompleted = data.Result.ReleaseCompleted;

                        // Load processed booking details with original times
                        $scope.Booking.SealID = data.Result.SealID || '';
                        $scope.Booking.DockLockEngaged = data.Result.DockLockEngaged || '';
                        $scope.Booking.ProcessedID = data.Result.ProcessedID;

                        // Set check-in fields with saved values
                        $scope.CheckIn.Date = data.Result.CheckinDate || '';
                        $scope.CheckIn.Time = data.Result.CheckinTime || '';

                        // Load TDR SOP steps and mark completed ones
                        $scope.loadTDRSteps(data.Result.CompletedSteps);

                        // Show resume message with original check-in time
                        var checkinInfo = '';
                        if (data.Result.CheckinDate && data.Result.CheckinTime) {
                            checkinInfo = ' (Originally processed: ' + data.Result.CheckinDate + ' ' + data.Result.CheckinTime + ')';
                        }

                        $mdToast.show(
                            $mdToast.simple()
                                .content('Resuming processed booking. Continue from where you left off.' + checkinInfo)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-info md-block')
                        );
                    } else {
                        $scope.BookingProcessed = false;
                        $scope.Booking.DockingCompleted = false;
                        $scope.Booking.ReleaseCompleted = false;

                        // For unprocessed bookings, set current date and time
                        var now = new Date();
                        $scope.CheckIn.Date = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                        $scope.CheckIn.Time = now.toTimeString().split(' ')[0].substring(0, 5); // HH:MM format
                    }
                } else {
                    $scope.BookingFound = false;
                    $scope.BookingProcessed = false;
                    $scope.DockingCompleted = false;
                    $scope.Booking = {};
                    $scope.DockingSteps = [];
                    $scope.ReleaseSteps = [];
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $scope.Search.busy = false;
                $scope.SearchPerformed = true;
                $scope.BookingFound = false;
                $rootScope.$broadcast('preloader:hide');

                $mdToast.show(
                    $mdToast.simple()
                        .content('Error searching for booking. Please try again.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );

                initSessionTime();
                $scope.$apply();
            }
        });
    };

    // Proceed without booking - show unbooked form
    $scope.proceedWithoutBooking = function() {
        console.log('Proceeding without booking...');

        // Pre-populate vehicle information from search
        $scope.UnbookedBooking.TruckReg = $scope.Search.TruckReg || '';
        $scope.UnbookedBooking.TrailerNumber = $scope.Search.TrailerNumber || '';

        // Set current user's facility (read-only)
        if ($scope.UserFacility) {
            $scope.UnbookedBooking.FacilityID = $scope.UserFacility;
            console.log('User facility ID set:', $scope.UserFacility);

            // Get facility name from the facilities list
            if ($scope.Facilities && $scope.Facilities.length > 0) {
                var userFacility = $scope.Facilities.find(function(f) {
                    return f.FacilityID == $scope.UserFacility;
                });
                if (userFacility) {
                    $scope.UnbookedBooking.FacilityName = userFacility.FacilityName;
                    console.log('User facility name set:', userFacility.FacilityName);
                } else {
                    $scope.UnbookedBooking.FacilityName = 'Facility ID: ' + $scope.UserFacility;
                }
            } else {
                $scope.UnbookedBooking.FacilityName = 'Facility ID: ' + $scope.UserFacility;
            }
        } else {
            console.log('User facility not available');
            $scope.UnbookedBooking.FacilityName = 'Facility not set';
        }

        // Set current date and time for check-in
        var now = new Date();
        $scope.UnbookedCheckIn.Date = now.toISOString().split('T')[0]; // YYYY-MM-DD
        $scope.UnbookedCheckIn.Time = now.toTimeString().split(' ')[0]; // HH:MM:SS

        // Validate vehicle information
        $scope.validateUnbookedVehicle();

        // Start loading dropdown data (this will show the form when complete)
        $scope.loadUnbookedDropdownData();
    };

    // Reset search form
    $scope.resetSearch = function() {
        $scope.Search.TruckReg = '';
        $scope.Search.TrailerNumber = '';
        $scope.SearchPerformed = false;
        $scope.BookingFound = false;
        $scope.showSearchError = false;
    };

    // Cancel unbooked form
    $scope.cancelUnbookedForm = function() {
        $scope.ShowUnbookedForm = false;
        $scope.resetSearch();
    };

    // Validate unbooked vehicle information
    $scope.validateUnbookedVehicle = function() {
        var truckReg = ($scope.UnbookedBooking.TruckReg || '').trim();
        var trailerNumber = ($scope.UnbookedBooking.TrailerNumber || '').trim();

        $scope.showUnbookedVehicleError = (truckReg === '' && trailerNumber === '');
        return !$scope.showUnbookedVehicleError;
    };

    // Handle unbooked facility change (not used since facility is read-only)
    $scope.onUnbookedFacilityChange = function() {
        // Facility is read-only, so this function is not needed
        // but kept for consistency
    };

    // Handle unbooked park type change
    $scope.onUnbookedParkTypeChange = function() {
        $scope.UnbookedBooking.ParkingLocationID = '';
        $scope.UnbookedParkingLocations = [];

        if ($scope.UnbookedBooking.ParkTypeID) {
            // Load parking locations for selected park type
            jQuery.ajax({
                url: host+'Truckyard/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetParkingLocationsByType&ParkTypeID=' + $scope.UnbookedBooking.ParkTypeID,
                success: function (data) {
                    if (data.Success) {
                        $scope.UnbookedParkingLocations = data.Result;
                    } else {
                        $scope.UnbookedParkingLocations = [];
                    }
                    $scope.$apply();
                },
                error: function (data) {
                    $scope.UnbookedParkingLocations = [];
                    $scope.$apply();
                }
            });
        }
    };

    // Handle unbooked vehicle type change
    $scope.onUnbookedVehicleTypeChange = function() {
        if ($scope.UnbookedBooking.TruckTypeID) {
            // Find the selected vehicle type name
            var selectedVehicleType = $scope.VehicleTypes.find(function(vt) {
                return vt.TruckTypeID == $scope.UnbookedBooking.TruckTypeID;
            });

            if (selectedVehicleType) {
                $scope.UnbookedBooking.VehicleType = selectedVehicleType.TruckTypeName;
            }
        }
    };

    // Create unbooked booking
    $scope.createUnbookedBooking = function() {
        if (!$scope.validateUnbookedVehicle()) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please enter either Truck Registration or Trailer Number.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
            );
            return;
        }

        $scope.UnbookedBooking.busy = true;
        $rootScope.$broadcast('preloader:active');

        // Prepare data for creating unbooked booking
        var bookingData = {
            ajax: 'CreateUnbookedBooking',
            TruckReg: $scope.UnbookedBooking.TruckReg || '',
            TrailerNumber: $scope.UnbookedBooking.TrailerNumber || '',
            DriverName: $scope.UnbookedBooking.DriverName || '',
            DriverID: $scope.UnbookedBooking.DriverID || '',
            FacilityID: $scope.UnbookedBooking.FacilityID,
            ParkTypeID: $scope.UnbookedBooking.ParkTypeID,
            ParkingLocationID: $scope.UnbookedBooking.ParkingLocationID,
            CarrierID: $scope.UnbookedBooking.CarrierID,
            TruckTypeID: $scope.UnbookedBooking.TruckTypeID,
            LoadType: $scope.UnbookedBooking.LoadType || '',
            LoadNumber: $scope.UnbookedBooking.LoadNumber || '',
            LoadQuantity: $scope.UnbookedBooking.LoadQuantity || 1,
            DockLockEngaged: $scope.UnbookedBooking.DockLockEngaged,
            Notes: $scope.UnbookedBooking.Notes || '',
            CheckinDate: $scope.UnbookedCheckIn.Date,
            CheckinTime: $scope.UnbookedCheckIn.Time
        };

        console.log('Sending unbooked booking data:', bookingData);

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: bookingData,
            success: function (data) {
                $scope.UnbookedBooking.busy = false;
                $rootScope.$broadcast('preloader:hide');

                if (data.Success) {
                    // Successfully created and processed unbooked booking
                    $scope.BookingFound = true;
                    $scope.ShowUnbookedForm = false;

                    // Ensure Result is an object
                    if (typeof data.Result === 'object' && data.Result !== null) {
                        $scope.Booking = data.Result;
                    } else {
                        // Fallback if Result is not an object
                        $scope.Booking = {
                            TruckID: data.TruckID,
                            BookingProcessed: true,
                            Processed: 1,
                            ProcessedID: data.ProcessedID
                        };
                    }

                    $scope.BookingProcessed = true; // Mark as processed since backend processed it

                    // Set check-in information
                    $scope.CheckIn.Date = $scope.UnbookedCheckIn.Date;
                    $scope.CheckIn.Time = $scope.UnbookedCheckIn.Time;

                    // Set processed information
                    $scope.ProcessedID = data.ProcessedID;
                    if (typeof $scope.Booking === 'object') {
                        $scope.Booking.ProcessedID = data.ProcessedID;
                    }

                    // Load TDR steps for the vehicle type
                    if ($scope.UnbookedBooking.VehicleType && typeof $scope.Booking === 'object') {
                        $scope.Booking.VehicleType = $scope.UnbookedBooking.VehicleType;
                        $scope.loadTDRSteps();
                    }

                    // Show steps section directly (skip the processing form)
                    $scope.showStepsSection();

                    $mdToast.show(
                        $mdToast.simple()
                            .content('Unbooked vehicle processed successfully. Ready for dock/release operations.')
                            .action('OK')
                            .position('right')
                            .hideDelay(5000)
                            .toastClass('md-toast-success md-block')
                    );
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result || 'Error creating unbooked booking. Please try again.')
                            .action('OK')
                            .position('right')
                            .hideDelay(5000)
                            .toastClass('md-toast-danger md-block')
                    );
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $scope.UnbookedBooking.busy = false;
                $rootScope.$broadcast('preloader:hide');

                $mdToast.show(
                    $mdToast.simple()
                        .content('Error creating unbooked booking. Please try again.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );

                initSessionTime();
                $scope.$apply();
            }
        });
    };

    // Show steps section (used after unbooked vehicle processing)
    $scope.showStepsSection = function() {
        // Scroll to steps section or trigger any UI updates needed
        setTimeout(function() {
            var stepsElement = document.getElementById('steps-section');
            if (stepsElement) {
                stepsElement.scrollIntoView({ behavior: 'smooth' });
            }
        }, 500);
    };

    // Handle vehicle type change
    $scope.onVehicleTypeChange = function() {
        if ($scope.Booking.TruckTypeID) {
            // Find the selected vehicle type name
            var selectedVehicleType = $scope.VehicleTypes.find(function(vt) {
                return vt.TruckTypeID == $scope.Booking.TruckTypeID;
            });

            if (selectedVehicleType) {
                $scope.Booking.VehicleType = selectedVehicleType.TruckTypeName;
                $scope.loadTDRSteps();
            }
        }
    };

    // Process booking - save to truck_docking_release and update truck table
    $scope.processBooking = function() {
        // Validate status for pre-booked trucks
        if ($scope.Booking.Status) {
            var currentStatus = $scope.Booking.Status.toLowerCase();
            if (currentStatus !== 'arrived' && currentStatus !== 'in progress') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Only "Arrived" or "In Progress" bookings can be processed. Current status: ' + $scope.Booking.Status)
                        .action('OK')
                        .position('right')
                        .hideDelay(4000)
                        .toastClass('md-toast-warning md-block')
                );
                return;
            }
        }

        if (!$scope.Booking.TruckID || !$scope.Booking.DockLockEngaged) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please complete all required fields before processing.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
            );
            return;
        }

        // Validate Seal ID if entered
        if ($scope.Booking.SealID && $scope.Booking.SealID.trim() !== '') {
            $scope.validateSealID($scope.Booking.SealID, function(isValid) {
                if (isValid) {
                    $scope.proceedWithBookingProcess();
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Invalid Seal ID. Please check and try again.')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }
            });
        } else {
            $scope.proceedWithBookingProcess();
        }
    };

    // Validate Seal ID against truck booking record
    $scope.validateSealID = function(sealID, callback) {
        jQuery.ajax({
            url: host + 'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=ValidateSealID&TruckID=' + $scope.Booking.TruckID + '&SealID=' + encodeURIComponent(sealID),
            success: function(data) {
                callback(data.Success);
            },
            error: function() {
                callback(false);
            }
        });
    };

    // Proceed with booking process after validation
    $scope.proceedWithBookingProcess = function() {
        $scope.ProcessingBooking = true;
        $rootScope.$broadcast('preloader:active');

        // Prepare tracking data for any changes made
        var trackingChanges = [];

        // Check for Parking Location changes
        if ($scope.OriginalBooking && $scope.Booking.ParkingLocationID != $scope.OriginalBooking.ParkingLocationID) {
            var newParkingLocation = $scope.ParkingLocations.find(function(pl) {
                return pl.ParkingLocationID == $scope.Booking.ParkingLocationID;
            });
            trackingChanges.push('Parking Location changed to: ' + (newParkingLocation ? newParkingLocation.ParkingLocationName : 'Unknown'));
        }

        // Check for Carrier changes
        if ($scope.OriginalBooking && $scope.Booking.CarrierID != $scope.OriginalBooking.CarrierID) {
            var newCarrier = $scope.Carriers.find(function(c) {
                return c.CarrierID == $scope.Booking.CarrierID;
            });
            trackingChanges.push('Carrier changed to: ' + (newCarrier ? newCarrier.CarrierName : 'Unknown'));
        }

        // Check for Vehicle Type changes
        if ($scope.OriginalBooking && $scope.Booking.TruckTypeID != $scope.OriginalBooking.TruckTypeID) {
            var newVehicleType = $scope.VehicleTypes.find(function(vt) {
                return vt.TruckTypeID == $scope.Booking.TruckTypeID;
            });
            trackingChanges.push('Vehicle Type changed to: ' + (newVehicleType ? newVehicleType.TruckTypeName : 'Unknown'));
        }

        // Always track Seal ID if provided
        if ($scope.Booking.SealID && $scope.Booking.SealID.trim() !== '') {
            trackingChanges.push('Seal ID: ' + $scope.Booking.SealID);
        }

        var processData = {
            TruckID: $scope.Booking.TruckID,
            ParkingLocationID: $scope.Booking.ParkingLocationID,
            CarrierID: $scope.Booking.CarrierID,
            TruckTypeID: $scope.Booking.TruckTypeID,
            DockLockEngaged: $scope.Booking.DockLockEngaged,
            SealID: $scope.Booking.SealID || '',
            CheckinDate: $scope.CheckIn.Date,
            CheckinTime: $scope.CheckIn.Time,
            TrackingChanges: trackingChanges.join(' | ')
        };

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=ProcessTruckBooking&' + $.param(processData),
            success: function (data) {
                $scope.ProcessingBooking = false;
                $rootScope.$broadcast('preloader:hide');

                if (data.Success) {
                    $scope.BookingProcessed = true;
                    $scope.Booking.ProcessedID = data.ProcessedID;

                    // Load TDR SOP steps now that booking is processed
                    $scope.loadTDRSteps();

                    $mdToast.show(
                        $mdToast.simple()
                            .content('Booking processed successfully. You can now proceed with dock/release operations.')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-success md-block')
                    );
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error processing booking: ' + (data.Result || 'Unknown error'))
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $scope.ProcessingBooking = false;
                $rootScope.$broadcast('preloader:hide');

                $mdToast.show(
                    $mdToast.simple()
                        .content('Error processing booking. Please try again.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );

                initSessionTime();
                $scope.$apply();
            }
        });
    };

    // Load TDR SOP steps based on booking details
    $scope.loadTDRSteps = function(completedSteps) {
        if (!$scope.Booking.VehicleType) {
            return;
        }

        // Use the Dock Lock Engaged value from the form, fallback to booking data
        var dockLockEngaged = $scope.Booking.DockLockEngaged || 'No';

        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetTDRSteps&VehicleType=' + encodeURIComponent($scope.Booking.VehicleType) +
                  '&DockLockEngaged=' + encodeURIComponent(dockLockEngaged),
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');

                if (data.Success) {
                    $scope.DockingSteps = data.Result.docking || [];
                    $scope.ReleaseSteps = data.Result.release || [];

                    // Initialize completion status for each step
                    $scope.DockingSteps.forEach(function(step) {
                        step.completed = false;
                    });
                    $scope.ReleaseSteps.forEach(function(step) {
                        step.completed = false;
                    });

                    // Mark completed steps if provided
                    if (completedSteps && completedSteps.length > 0) {
                        completedSteps.forEach(function(completedStep) {
                            // Mark docking steps as completed
                            $scope.DockingSteps.forEach(function(step) {
                                if (step.TDRSOPID == completedStep.TDRSOPID && completedStep.TdrType === 'Trailer Docking') {
                                    step.completed = true;
                                    step.StepID = completedStep.StepID || true; // Mark as saved
                                    step.savedToDatabase = true;
                                }
                            });

                            // Mark release steps as completed
                            $scope.ReleaseSteps.forEach(function(step) {
                                if (step.TDRSOPID == completedStep.TDRSOPID && completedStep.TdrType === 'Trailer Release') {
                                    step.completed = true;
                                    step.StepID = completedStep.StepID || true; // Mark as saved
                                    step.savedToDatabase = true;
                                }
                            });
                        });
                    }
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.DockingSteps = [];
                $scope.ReleaseSteps = [];
                initSessionTime();
                $scope.$apply();
            }
        });
    };    

    // Get completed steps count
    $scope.getCompletedSteps = function(type) {
        var steps = type === 'docking' ? $scope.DockingSteps : $scope.ReleaseSteps;
        return steps.filter(function(step) { return step.completed; }).length;
    };

    // Check if all steps are completed
    $scope.allStepsCompleted = function(type) {
        var steps = type === 'docking' ? $scope.DockingSteps : $scope.ReleaseSteps;
        return steps.length > 0 && steps.every(function(step) { return step.completed; });
    };

    // Handle step change with TPVR check
    $scope.handleStepChange = function(step, type) {
        if (step.completed && step.SpotterTPVRRequired == 1) {
            // If step is being checked and requires TPVR, show modal
            step.completed = false; // Temporarily uncheck until TPVR is verified
            $scope.showTPVRModal(step, type);
        } else if (step.completed) {
            // Regular step completion - save to database
            $scope.saveStepCompletion(step, type);
        } else {
            // Prevent unchecking completed steps
            if (step.StepID) {
                step.completed = true; // Revert back to checked
                $mdToast.show(
                    $mdToast.simple()
                        .content('Cannot uncheck completed step. Step ' + step.StepNo + ' has been saved to the database.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-warning md-block')
                );
            } else {
                // Step unchecked - update progress (only for non-saved steps)
                $scope.updateStepProgress();
            }
        }
    };

    // Save step completion to database
    $scope.saveStepCompletion = function(step, type) {
        $rootScope.$broadcast('preloader:active');

        var stepData = {
            TDRSOPID: step.TDRSOPID,
            TruckID: $scope.Booking.TruckID,
            TdrType: type === 'docking' ? 'Trailer Docking' : 'Trailer Release',
            StepNo: step.StepNo,
            StepDescription: step.Description,
            TDRSpotterController: step.tpvrDetails ? step.tpvrDetails.TDRSpotter : '',
            ProcessedID: $scope.Booking.ProcessedID
        };

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SaveStepCompletion&' + $.param(stepData),
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');

                if (data.Success) {
                    step.StepID = data.StepID;
                    step.savedToDatabase = true; // Mark as saved to prevent unchecking
                    $scope.updateStepProgress();

                    $mdToast.show(
                        $mdToast.simple()
                            .content('Step ' + step.StepNo + ' completed successfully')
                            .action('OK')
                            .position('right')
                            .hideDelay(2000)
                            .toastClass('md-toast-success md-block')
                    );
                } else {
                    step.completed = false; // Revert if save failed
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error saving step completion: ' + (data.Result || 'Unknown error'))
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $rootScope.$broadcast('preloader:hide');

                step.completed = false; // Revert if save failed
                $mdToast.show(
                    $mdToast.simple()
                        .content('Error saving step completion. Please try again.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );

                initSessionTime();
                $scope.$apply();
            }
        });
    };

    // Update step progress
    $scope.updateStepProgress = function() {
        // This function can be used to track progress or trigger other actions
    };

    // Show TPVR Modal
    $scope.showTPVRModal = function(step, type) {
        $mdDialog.show({
            controller: TPVRController,
            templateUrl: 'tpvr-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: false,
            resolve: {
                stepData: function() {
                    return {
                        step: step,
                        type: type,
                        stepNo: step.StepNo,
                        stepDescription: step.Description
                    };
                }
            }
        }).then(function(result) {
            if (result && result.verified) {
                // TPVR verified, mark step as completed and save to database
                step.completed = true;
                step.tpvrVerified = true;
                step.tpvrDetails = result.tpvrDetails;

                // Save step completion to database
                $scope.saveStepCompletion(step, type);

                $mdToast.show(
                    $mdToast.simple()
                        .content('TPVR verified successfully for Step ' + step.StepNo)
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-success md-block')
                );
            }
        }, function() {
            // TPVR cancelled, keep step unchecked
            step.completed = false;
        });
    };

    // TPVR Modal Controller
    function TPVRController($scope, $mdDialog, stepData, $mdToast, $rootScope) {
        $scope.tpvrData = stepData;
        $scope.tpvrDetails = {
            TDRSpotter: '',
            Password: ''
        };
        $scope.tpvrBusy = false;
        $scope.tpvrErrorMessage = '';

        // Focus on first field when modal opens
        setTimeout(function() {
            document.getElementById('TDRSpotter').focus();
        }, 100);

        $scope.cancel = function() {
            $mdDialog.cancel();
        };

        $scope.verifyTPVR = function() {
            $scope.tpvrBusy = true;
            $scope.tpvrErrorMessage = ''; // Clear previous error
            $rootScope.$broadcast('preloader:active');

            var tpvrData = {
                TDRSpotter: $scope.tpvrDetails.TDRSpotter,
                Password: $scope.tpvrDetails.Password,
                StepNo: $scope.tpvrData.stepNo,
                StepDescription: $scope.tpvrData.stepDescription,
                OperationType: $scope.tpvrData.type
            };

            jQuery.ajax({
                url: host+'Truckyard/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=VerifyTPVR&' + $.param(tpvrData),
                success: function (data) {
                    $scope.tpvrBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        $mdDialog.hide({
                            verified: true,
                            tpvrDetails: $scope.tpvrDetails
                        });
                    } else {
                        // Show error message inside the modal instead of toast
                        $scope.tpvrErrorMessage = 'TPVR verification failed: ' + (data.Result || 'Invalid credentials');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function (data) {
                    $scope.tpvrBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    // Show error message inside the modal instead of toast
                    $scope.tpvrErrorMessage = 'Error verifying TPVR. Please try again.';

                    initSessionTime();
                    $scope.$apply();
                }
            });
        };
    }

    // Show Completion TPVR Modal
    $scope.showCompletionTPVR = function(operationType) {
        $mdDialog.show({
            controller: CompletionTPVRController,
            templateUrl: 'completion-tpvr-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: false,
            resolve: {
                operationData: function() {
                    return {
                        operationType: operationType === 'docking' ? 'Docking' : 'Release',
                        truckID: $scope.Booking.TruckID
                    };
                }
            }
        }).then(function(result) {
            if (result && result.completed) {
                // Update completion status
                if (operationType === 'docking') {
                    $scope.Booking.DockingCompleted = true;
                } else {
                    $scope.Booking.ReleaseCompleted = true;
                }

                $scope.$apply();

                $mdToast.show(
                    $mdToast.simple()
                        .content(result.message)
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-success md-block')
                );
            }
        }, function() {
            // TPVR cancelled
        });
    };

    // Completion TPVR Modal Controller
    function CompletionTPVRController($scope, $mdDialog, operationData, $mdToast, $rootScope) {
        $scope.completionData = operationData;
        $scope.completionDetails = {
            TDRLead: '',
            Password: ''
        };
        $scope.completionBusy = false;
        $scope.completionErrorMessage = '';

        // Focus on first field when modal opens
        setTimeout(function() {
            document.getElementById('TDRLead').focus();
        }, 100);

        $scope.cancel = function() {
            $mdDialog.cancel();
        };

        $scope.verifyCompletion = function() {
            $scope.completionBusy = true;
            $scope.completionErrorMessage = ''; // Clear previous error
            $rootScope.$broadcast('preloader:active');

            var completionData = {
                TruckID: $scope.completionData.truckID,
                TDRLead: $scope.completionDetails.TDRLead,
                Password: $scope.completionDetails.Password
            };

            var endpoint = $scope.completionData.operationType === 'Docking' ? 'CompleteDocking' : 'CompleteRelease';

            jQuery.ajax({
                url: host+'Truckyard/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=' + endpoint + '&' + $.param(completionData),
                success: function (data) {
                    $scope.completionBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        $mdDialog.hide({
                            completed: true,
                            message: data.Result + ' (Lead: ' + (data.LeadUser || $scope.completionDetails.TDRLead) + ')'
                        });
                    } else {
                        // Show error message inside the modal instead of toast
                        $scope.completionErrorMessage = 'Completion failed: ' + (data.Result || 'Invalid credentials');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function (data) {
                    $scope.completionBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    // Show error message inside the modal instead of toast
                    $scope.completionErrorMessage = 'Error completing operation. Please try again.';

                    initSessionTime();
                    $scope.$apply();
                }
            });
        };
    }

    // Report unsafe condition with TPVR
    $scope.reportUnsafe = function(type) {
        $mdDialog.show({
            controller: UnsafeTPVRController,
            templateUrl: 'unsafe-tpvr-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: false,
            resolve: {
                operationData: function() {
                    return {
                        operationType: type === 'docking' ? 'Docking' : 'Release',
                        truckID: $scope.Booking.TruckID
                    };
                }
            }
        }).then(function(result) {
            if (result && result.verified) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Unsafe condition reported and authorized. Booking marked as Failed and closed. Manager: ' + (result.managerName || result.tdrManager))
                        .action('OK')
                        .position('right')
                        .hideDelay(5000)
                        .toastClass('md-toast-warning md-block')
                );

                // Clear the current booking and reset the form
                $scope.resetBookingForm();

                // Optionally redirect to booking list or show completion message
                setTimeout(function() {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Booking has been closed due to unsafe condition. You can search for a new truck.')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-info md-block')
                    );
                }, 2000);
            }
        }, function() {
            // TPVR cancelled
        });
    };

    // Unsafe TPVR Modal Controller
    function UnsafeTPVRController($scope, $mdDialog, operationData, $mdToast, $rootScope) {
        $scope.operationData = operationData;
        $scope.unsafeDetails = {
            TDRManager: '',
            Password: '',
            C3POEntryID: ''
        };
        $scope.unsafeBusy = false;
        $scope.unsafeErrorMessage = '';

        // Focus on first field when modal opens
        setTimeout(function() {
            document.getElementById('TDRManager').focus();
        }, 100);

        $scope.cancel = function() {
            $mdDialog.cancel();
        };

        $scope.verifyUnsafe = function() {
            $scope.unsafeBusy = true;
            $scope.unsafeErrorMessage = ''; // Clear previous error
            $rootScope.$broadcast('preloader:active');

            var unsafeData = {
                TruckID: $scope.operationData.truckID,
                TDRManager: $scope.unsafeDetails.TDRManager,
                Password: $scope.unsafeDetails.Password,
                C3POEntryID: $scope.unsafeDetails.C3POEntryID,
                OperationType: $scope.operationData.operationType
            };

            jQuery.ajax({
                url: host+'Truckyard/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=VerifyUnsafeTPVR&' + $.param(unsafeData),
                success: function (data) {
                    $scope.unsafeBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        $mdDialog.hide({
                            verified: true,
                            tdrManager: $scope.unsafeDetails.TDRManager,
                            managerName: data.ManagerName || '',
                            c3poEntryID: $scope.unsafeDetails.C3POEntryID
                        });
                    } else {
                        // Show error message inside the modal instead of toast
                        $scope.unsafeErrorMessage = 'Unsafe TPVR verification failed: ' + (data.Result || 'Invalid credentials');
                    }

                    initSessionTime();
                    $scope.$apply();
                },
                error: function (data) {
                    $scope.unsafeBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    // Show error message inside the modal instead of toast
                    $scope.unsafeErrorMessage = 'Error verifying unsafe TPVR. Please try again.';

                    initSessionTime();
                    $scope.$apply();
                }
            });
        };
    }

    // Reset booking form after unsafe condition
    $scope.resetBookingForm = function() {
        // Clear booking data
        $scope.Booking = {
            TruckID: '',
            TruckReg: '',
            TrailerNumber: '',
            ParkingLocationID: '',
            CarrierID: '',
            TruckTypeID: '',
            VehicleType: '',
            DockLockEngaged: '',
            SealID: '',
            Status: '',
            DockingCompleted: false,
            ReleaseCompleted: false,
            ProcessedID: ''
        };

        // Clear search data
        $scope.Search = {
            TruckReg: '',
            TrailerNumber: '',
            busy: false
        };

        // Clear check-in data
        var now = new Date();
        $scope.CheckIn.Date = now.toISOString().split('T')[0];
        $scope.CheckIn.Time = now.toTimeString().substring(0, 8);

        // Reset form states
        $scope.BookingFound = false;
        $scope.SearchPerformed = false;
        $scope.BookingProcessed = false;
        $scope.ProcessingBooking = false;

        // Clear steps
        $scope.DockingSteps = [];
        $scope.ReleaseSteps = [];

        // Reset progress
        $scope.DockingProgress = 0;
        $scope.ReleaseProgress = 0;
    };

    // Complete docking operation
    $scope.completeDocking = function() {
        if (!$scope.allStepsCompleted('docking')) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please complete all docking steps before proceeding.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-warning md-block')
            );
            return;
        }

        $scope.logOperation('docking', 'COMPLETED', 'All docking steps completed successfully');
    };

    // Complete release operation
    $scope.completeRelease = function() {
        if (!$scope.allStepsCompleted('release')) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please complete all release steps before proceeding.')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-warning md-block')
            );
            return;
        }

        $scope.logOperation('release', 'COMPLETED', 'All release steps completed successfully');
    };

    // Log operation
    $scope.logOperation = function(type, status, notes) {
        $rootScope.$broadcast('preloader:active');

        var operationData = {
            TruckID: $scope.Booking.TruckID,
            OperationType: type,
            Status: status,
            Notes: notes,
            CheckInDate: $scope.CheckIn.Date,
            CheckInTime: $scope.CheckIn.Time,
            CompletedSteps: type === 'docking' ? $scope.DockingSteps : $scope.ReleaseSteps
        };

        jQuery.ajax({
            url: host+'Truckyard/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=LogTDROperation&' + $.param(operationData),
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');

                if (data.Success) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Operation logged successfully.')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-success md-block')
                    );

                    // Reset form for next operation
                    $scope.resetForm();
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error logging operation: ' + (data.Result || 'Unknown error'))
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }

                initSessionTime();
                $scope.$apply();
            },
            error: function (data) {
                $rootScope.$broadcast('preloader:hide');

                $mdToast.show(
                    $mdToast.simple()
                        .content('Error logging operation. Please try again.')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                );

                initSessionTime();
                $scope.$apply();
            }
        });
    };

    // Reset form for next operation
    $scope.resetForm = function() {
        $scope.Search = {
            TruckReg: '',
            TrailerNumber: '',
            busy: false
        };
        $scope.Booking = {};
        $scope.DockingSteps = [];
        $scope.ReleaseSteps = [];
        $scope.BookingFound = false;
        $scope.SearchPerformed = false;
        $scope.showSearchError = false;

        // Update check-in time
        var now = new Date();
        $scope.CheckIn.Date = now.toISOString().substring(0, 10);
        $scope.CheckIn.Time = now.toTimeString().substring(0, 8);
    };
});


module.controller("CompletedTruckList", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog) {
    $rootScope.$broadcast('preloader:active');
    jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=CheckIfPagePermission&Page=Truck Booking',
        success: function (data) {
            $rootScope.$broadcast('preloader:hide');
            if (data.Success) {                
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-info md-block')
                );  
                window.location = host;             
            }
            initSessionTime(); $scope.$apply();
        }, error: function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.error = data;
            initSessionTime(); $scope.$apply();
        }
    });
    

       $scope.busy = false;
       $scope.TruckList = [];
       $scope.pagedItems = [];
       $scope.CurrentStatus = 'Complete';

       //Start Pagination Logic
       $scope.itemsPerPage = 20;
       $scope.currentPage = 0;
       $scope.OrderBy = '';
       $scope.OrderByType = '';
       $scope.filter_text = [{}];
       $scope.range = function() {
           var rangeSize = 10;
           var ret = [];
           var start;
           start = $scope.currentPage;
           if ( start > $scope.pageCount()-rangeSize ) {
               start = $scope.pageCount()-rangeSize;
           }
           for (var i=start; i<start+rangeSize; i++) {
               ret.push(i);
           }
           return ret;
       };
       $scope.prevPage = function() {
           if ($scope.currentPage > 0) {
               $scope.currentPage--;
           }
       };
       $scope.firstPage = function () {
           $scope.currentPage = 0;
       };
       $scope.prevPageDisabled = function() {
           return $scope.currentPage === 0 ? "disabled" : "";
       };
       $scope.nextPage = function() {
           if ($scope.currentPage < $scope.pageCount() - 1) {
               $scope.currentPage++;
           }
       };
       $scope.lastPage = function() {
           $scope.currentPage =  $scope.pageCount() - 1;
       };
       $scope.nextPageDisabled = function() {
           return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
       };
       $scope.pageCount = function() {
           return Math.ceil($scope.total/$scope.itemsPerPage);
       };
       $scope.setPage = function(n) {
           if (n >= 0 && n < $scope.pageCount()) {
               $scope.currentPage = n;
           }
       };
       $scope.CallServerFunction = function (newValue) {
           if($scope.CurrentStatus != '' )  {
               $scope.busy = true;
               $rootScope.$broadcast('preloader:active');
               jQuery.ajax({
                   url: host+'Truckyard/includes/Truck_submit.php',
                   dataType: 'json',
                   type: 'post',
                   data: 'ajax=GetCompleteNoShowTruckList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                   success: function(data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       if(data.Success) {
                           $scope.pagedItems = data.Result;
                           data.Result.forEach(function(truck) {
                            // Format ArrivalTime
                            if (truck.ArrivalTime) {
                                var arrivalParts = truck.ArrivalTime.split(':');
                                var arrivalDate = new Date(1970, 0, 1, parseInt(arrivalParts[0]), parseInt(arrivalParts[1]), parseInt(arrivalParts[2] || 0));
                                truck.ArrivalTimeFormatted = arrivalDate.toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });

                                // Calculate and format DepartureTime (+30 mins)
                                var departureDate = new Date(arrivalDate.getTime() + 30 * 60000); // 30 mins later
                                truck.DepartureTimeFormatted = departureDate.toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true
                                });
                            } else {
                                truck.ArrivalTimeFormatted = '';
                                truck.DepartureTimeFormatted = '';
                            }
                        });

                           if(data.total) {
                               $scope.total = data.total;
                           }
                       } else {
                           $mdToast.show(
                               $mdToast.simple()
                                   .content(data.Result)
                                   .position('right')
                                   .hideDelay(3000)
                           );

                           var op = data.Result.split(' ');                            
                            if( op[0] == "No" && op[1] == 'Access') {
                                window.location = host;
                            }
                       }
                       initSessionTime(); $scope.$apply();
                   }, error : function (data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       alert(data.Result);
                       $scope.error = data;
                       initSessionTime(); $scope.$apply();
                   }
               });
           }
       };
       $scope.$watch("currentPage", function(newValue, oldValue) {
           $scope.CallServerFunction(newValue);
       });
       $scope.convertSingle = function (multiarray) {
           var result = {};
           for(var i=0;i<multiarray.length;i++) {
               result[i] = multiarray[i];
           }
           //alert(result);
           return result;
       };
       $scope.MakeOrderBy = function (orderby) {
           $scope.OrderBy = orderby;
           if($scope.OrderByType == 'asc') {
               $scope.OrderByType = 'desc';
           } else {
               $scope.OrderByType = 'asc';
           }
           $scope.busy = true;
           $rootScope.$broadcast('preloader:active');

           jQuery.ajax({
               url: host+'Truckyard/includes/Truck_submit.php',
               dataType: 'json',
               type: 'post',
               data: 'ajax=GetCompleteNoShowTruckList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
               success: function(data) {
                   $scope.busy = false;
                   $rootScope.$broadcast('preloader:hide');
                   if(data.Success) {
                       $scope.pagedItems = data.Result;
                       data.Result.forEach(function(truck) {
                        // Format ArrivalTime
                        if (truck.ArrivalTime) {
                            var arrivalParts = truck.ArrivalTime.split(':');
                            var arrivalDate = new Date(1970, 0, 1, parseInt(arrivalParts[0]), parseInt(arrivalParts[1]), parseInt(arrivalParts[2] || 0));
                            truck.ArrivalTimeFormatted = arrivalDate.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            });

                            // Calculate and format DepartureTime (+30 mins)
                            var departureDate = new Date(arrivalDate.getTime() + 30 * 60000); // 30 mins later
                            truck.DepartureTimeFormatted = departureDate.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            });
                        } else {
                            truck.ArrivalTimeFormatted = '';
                            truck.DepartureTimeFormatted = '';
                        }
                    });

                       if(data.total) {
                           $scope.total = data.total;
                       }
                   } else {
                       $mdToast.show(
                           $mdToast.simple()
                               .content(data.Result)
                               .position('right')
                               .hideDelay(3000)
                       );

                       var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                   }
                   initSessionTime(); $scope.$apply();
               }, error : function (data) {
                   $scope.busy = false;
                   $rootScope.$broadcast('preloader:hide');
                   $scope.error = data;
                   initSessionTime(); $scope.$apply();
               }
           });
       };
       $scope.MakeFilter = function () {
           if($scope.currentPage == 0) {
               $scope.CallServerFunction($scope.currentPage);
           } else {
               $scope.currentPage = 0;
           }
       };

       $scope.DeleteTruck = function (id) {
            var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
            $mdDialog.show(confirm).then(function () {
                jQuery.ajax({
                    url: host+'Truckyard/includes/Truck_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteTruck&id='+id,
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.CallServerFunction($scope.currentPage);
                        } else {
                            //alert("4");
                            $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }, function () {

        });
    };

     $scope.ExportTruckListxls = function () {
       //alert("1");
       jQuery.ajax({
           url: host+'Truckyard/includes/Truck_submit.php',
           dataType: 'json',
           type: 'post',
           data: 'ajax=GenerateCompleteNoShowTruckListxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

           success: function(data) {
               if(data.Success) {
                   //alert("2");
                   //console.log(data.Result);
                   window.location="templates/CompleteNoShowTruckListxls.php";
               } else {
                  // alert("4");
                   $mdToast.show (
                       $mdToast.simple()
                       .content(data.Result)
                       .action('OK')
                       .position('right')
                       .hideDelay(0)
                       .toastClass('md-toast-danger md-block')
                   );

                   var op = data.Result.split(' ');                            
                    if( op[0] == "No" && op[1] == 'Access') {
                        window.location = host;
                    }
               }
               
               initSessionTime(); $scope.$apply();
           }, error : function (data) {
               //alert(data.Result);
               //alert("3");
               $scope.error = data;
               initSessionTime(); $scope.$apply();
           }
       });
   };

   // Truck Tracking History Functions
   $scope.selectedTruck = {};
   $scope.trackingHistory = [];
   $scope.trackingHistoryLoading = false;

   $scope.showTruckTrackingHistory = function(truck) {
       $mdDialog.show({
           templateUrl: 'truckTrackingDialog.html',
           parent: angular.element(document.body),
           clickOutsideToClose: true,
           fullscreen: false,
           controller: function($scope, $mdDialog) {
               $scope.selectedTruck = truck;
               $scope.trackingHistory = [];
               $scope.trackingHistoryLoading = true;

               $scope.closeDialog = function() {
                   $mdDialog.hide();
               };

               // Fetch tracking history
               jQuery.ajax({
                   url: host + 'Truckyard/includes/Truck_submit.php',
                   dataType: 'json',
                   type: 'get',
                   data: 'ajax=GetTruckTrackingHistory&TruckID=' + truck.TruckID,
                   success: function(data) {
                       $scope.trackingHistoryLoading = false;
                       if (data.Success) {
                           $scope.trackingHistory = data.Result;
                       } else {
                           $scope.trackingHistory = [];
                           $mdToast.show(
                               $mdToast.simple()
                                   .content('Error loading truck history: ' + data.Result)
                                   .action('OK')
                                   .position('right')
                                   .hideDelay(3000)
                                   .toastClass('md-toast-danger md-block')
                           );
                       }
                       $scope.$apply();
                   },
                   error: function(xhr, status, error) {
                       $scope.trackingHistoryLoading = false;
                       $scope.trackingHistory = [];
                       $mdToast.show(
                           $mdToast.simple()
                               .content('Error loading truck history')
                               .action('OK')
                               .position('right')
                               .hideDelay(3000)
                               .toastClass('md-toast-danger md-block')
                       );
                       $scope.$apply();
                   }
               });
           }
       });
   };

    // Function to get status CSS class for color coding
    $scope.getStatusClass = function(status) {
        if (!status) return '';

        switch (status.toLowerCase()) {
            case 'complete': return 'complete';
            case 'no show': return 'noshow';
            case 'failed': return 'failed';
            case 'reserved': return 'reserved';
            case 'requested': return 'requested';
            case 'in progress': return 'inprogress';
            case 'arrived': return 'arrived';
            default: return '';
        }
    };



       //End Pagination Logic
   });


})();
