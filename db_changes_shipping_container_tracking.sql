-- Create shipping_container_tracking table for tracking shipping container operations
-- This table will track all operations performed on shipping containers including closures with controller information

CREATE TABLE IF NOT EXISTS `shipping_container_tracking` (
  `TrackingID` INT NOT NULL AUTO_INCREMENT,
  `ShippingContainerID` VARCHAR(50) NOT NULL,
  `Action` TEXT NOT NULL,
  `Description` TEXT NULL,
  `CreatedDate` DATETIME NOT NULL,
  `CreatedBy` INT NOT NULL,
  `Mo<PERSON>leName` VARCHAR(100) NULL,
  PRIMARY KEY (`TrackingID`),
  INDEX `idx_shipping_container_id` (`ShippingContainerID` ASC),
  INDEX `idx_created_date` (`CreatedDate` ASC),
  INDEX `idx_created_by` (`CreatedBy` ASC),
  INDEX `idx_module_name` (`ModuleName` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraint to link with shipping_containers table
ALTER TABLE `shipping_container_tracking` 
ADD CONSTRAINT `fk_shipping_container_tracking_container`
  FOREIGN KEY (`Shipping<PERSON>ontainerID`)
  REFERENCES `shipping_containers` (`ShippingContainerID`)
  ON DELETE CASCADE
  ON UPDATE CASCADE;

-- Add foreign key constraint to link with users table
ALTER TABLE `shipping_container_tracking` 
ADD CONSTRAINT `fk_shipping_container_tracking_user`
  FOREIGN KEY (`CreatedBy`)
  REFERENCES `users` (`UserId`)
  ON DELETE RESTRICT
  ON UPDATE CASCADE;
