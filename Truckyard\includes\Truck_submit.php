<?php
session_start();
include_once("../database/Truck.class.php");
$obj = new TruckClass();
	
	if($_POST['ajax'] == "GetParkTypes"){
		$result = $obj->GetParkTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkingLocations"){
		$result = $obj->GetParkingLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCalendarData"){
		$result = $obj->GetCalendarData($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SearchActiveBooking"){
		$result = $obj->SearchActiveBooking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateUnbookedBooking"){
		$result = $obj->CreateUnbookedBooking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetVehicleTypes"){
		$result = $obj->GetVehicleTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkingLocationsByType"){
		$result = $obj->GetParkingLocationsByType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTDRSteps"){
		$result = $obj->GetTDRSteps($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckTypes"){
		$result = $obj->GetTruckTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCarriers"){
  		$result = $obj->GetCarriers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "TruckSave") {
		$result = $obj->TruckSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckDetails") {
		$result = $obj->GetTruckDetails($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "GetTruckList") {
		$result = $obj->GetTruckList($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "DeleteTruck"){
		$result = $obj->DeleteTruck($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ChangeStatus"){
		$result = $obj->ChangeStatus($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateTruckListxls"){
  		$result = $obj->GenerateTruckListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCompleteNoShowTruckList") {
		$result = $obj->GetCompleteNoShowTruckList($_POST);
		echo $result;
  	}
	if($_POST['ajax'] == "GenerateCompleteNoShowTruckListxls"){
  		$result = $obj->GenerateCompleteNoShowTruckListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UploadTruckBookingFile") {
		$result = $obj->UploadTruckBookingFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "TestConnection") {
		$result = $obj->TestConnection($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "VerifyTPVR") {
		$result = $obj->VerifyTPVR($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ProcessTruckBooking") {
		$result = $obj->ProcessTruckBooking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveStepCompletion") {
		$result = $obj->SaveStepCompletion($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CompleteDocking") {
		$result = $obj->CompleteDocking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CompleteRelease") {
		$result = $obj->CompleteRelease($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "VerifyUnsafeTPVR") {
		$result = $obj->VerifyUnsafeTPVR($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetTruckTrackingHistory") {
		$result = $obj->GetTruckTrackingHistory($_GET);
		echo $result;
	}
	if($_POST['ajax'] == "ValidateSealID") {
		$result = $obj->ValidateSealID($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UploadTruckImage") {
		$result = $obj->UploadTruckImage(array_merge($_POST, array('file' => $_FILES['file'])));
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckImages") {
		$result = $obj->GetTruckImages($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeleteTruckImage") {
		$result = $obj->DeleteTruckImage($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckHyperLink") {
		$result = $obj->GetTruckHyperLink($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveTruckHyperLink") {
		$result = $obj->SaveTruckHyperLink($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeleteTruckHyperLink") {
		$result = $obj->DeleteTruckHyperLink($_POST);
		echo $result;
	}