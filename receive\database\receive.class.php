<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
include_once("../../excel_reader/SimpleXLSX.php");
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

//require '../../vendor/finalvendor/autoload.php';
//include_once("../../config.php");

class ReceiveClass extends CommonClass {
	public $responseParameters;	
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}
	
	public function CreateLoad ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);

		$chkdt = $data['EstimatedCompletionDate'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);					
		$data['EstimatedCompletionDate'] = date("Y-m-d",$newdt);

		$chkdt = $data['DateReceived'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);					
		$data['DateReceived'] = date("Y-m-d",$newdt);


		$chkdt = $data['DatePickup'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);					
		$data['DatePickup'] = date("Y-m-d",$newdt);
		
		//return json_encode($json);
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idTruckingCompany'] = (mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) : 0 ;
		$data['idRefCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer']) : 0 ;
		$data['idServiceType'] = (mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) : 0 ;
		$data['LotStatus'] = (mysqli_real_escape_string($this->connectionlink,$data['LotStatus']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['LotStatus']) : 0 ;
		$data['ProcessID'] = (mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) : 0 ;
		$data['ReceivedHours'] = (mysqli_real_escape_string($this->connectionlink,$data['ReceivedHours']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['ReceivedHours']) : 0 ;
		
		$data['TotalTransportationCost'] = (mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost']) : 0 ;
		$data['AdvancedPayment'] = (mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment']) : 0 ;
		
		$data['LogisticCharges'] = (mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges']) : 0 ;
		$data['TransportationCommissionPercent'] = (mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent']) : 0 ;
		/*$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;*/
		if($data['LoadId'] == '') { // If New Load
			//Start getting Receive Price per hour
			$query11 = "select ReceivePricePerHour from facility where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
			$q11 = mysqli_query($this->connectionlink,$query11);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row11 = mysqli_fetch_assoc($q11);
				$ReceivePricePerHour = $row11['ReceivePricePerHour'];
				$ReceiveCost = $ReceivePricePerHour * $data['ReceivedHours'];
			} else {
				$ReceivePricePerHour = 0;
				$ReceiveCost = 0;
			}
			//End getting Receive Price per hour
			
			$LoadId = $this->GenerateLoadID($data['idCustomer']);
			$query = "insert into loads (idCustomer,LoadId,idTruckingCompany,idRefCustomer,idServiceType,LoadDescription,SpecialInstructions,DateReceived,DatePickup,EstimatedCompletionDate,DateCreated,DateUpdated,CustomerTag,idUser,FacilityID,Damaged,DamagedReason,ProcessID,LotStatus,ReceivedHours,ReceivePricePerHour,ReceiveCost,JabilInternalOrder,TotalTransportationCost,LogisticCharges,TransportationCommissionPercent,AdvancedPayment,DamagedDescription,SealNo,LoadCreationSecurityGuardID,LoadCreationReceivingOperatorID,LoadCreationAuditorID,SealNo2,TrailerID,SealNo3) values ('".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany'])."','".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['idServiceType'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."','".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."','".mysqli_real_escape_string($this->connectionlink,$data['DateReceived'])."','".mysqli_real_escape_string($this->connectionlink,$data['DatePickup'])."','".mysqli_real_escape_string($this->connectionlink,$data['EstimatedCompletionDate'])."',NOW(),NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['CustomerTag'])."','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."','".mysqli_real_escape_string($this->connectionlink,$data['DamagedReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LotStatus'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReceivedHours'])."','".mysqli_real_escape_string($this->connectionlink,$ReceivePricePerHour)."','".mysqli_real_escape_string($this->connectionlink,$ReceiveCost)."','".mysqli_real_escape_string($this->connectionlink,$data['JabilInternalOrder'])."','".mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges'])."','".mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent'])."','".mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment'])."','".mysqli_real_escape_string($this->connectionlink,$data['DamagedDescription'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationSecurityGuardID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."')";
		} else {
			$ReceiveCost = $data['ReceivedHours'] * $data['ReceivePricePerHour'];
			$LoadId = $data['LoadId'];
			$query = "update loads set idServiceType='".mysqli_real_escape_string($this->connectionlink,$data['idServiceType'])."',idTruckingCompany='".mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany'])."',LoadDescription='".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."',SpecialInstructions='".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."',DateReceived='".mysqli_real_escape_string($this->connectionlink,$data['DateReceived'])."',DatePickup='".mysqli_real_escape_string($this->connectionlink,$data['DatePickup'])."',EstimatedCompletionDate='".mysqli_real_escape_string($this->connectionlink,$data['EstimatedCompletionDate'])."',DateUpdated=NOW(),CustomerTag='".mysqli_real_escape_string($this->connectionlink,$data['CustomerTag'])."',idUser='".$_SESSION['user']['UserId']."',LotStatus='0',Damaged = '".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."',DamagedReason = '".mysqli_real_escape_string($this->connectionlink,$data['DamagedReason'])."',idCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."',idRefCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."',LotStatus = '".mysqli_real_escape_string($this->connectionlink,$data['LotStatus'])."',ProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."',ReceivedHours = '".mysqli_real_escape_string($this->connectionlink,$data['ReceivedHours'])."',ReceiveCost = '".mysqli_real_escape_string($this->connectionlink,$ReceiveCost)."',JabilInternalOrder = '".mysqli_real_escape_string($this->connectionlink,$data['JabilInternalOrder'])."',TotalTransportationCost = '".mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost'])."',LogisticCharges = '".mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges'])."',TransportationCommissionPercent = '".mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent'])."',AdvancedPayment = '".mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment'])."',DamagedDescription = '".mysqli_real_escape_string($this->connectionlink,$data['DamagedDescription'])."',SealNo = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."',LoadCreationSecurityGuardID = '".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationSecurityGuardID'])."',LoadCreationReceivingOperatorID = '".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."',LoadCreationAuditorID = '".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',TrailerID = '".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
		}		
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {			
			$json['Success'] = true;
			if($data['LoadId'] == '') {
				$json['Result'] = 'Load Created with LoadID '.$LoadId;
			} else {
				$json['Result'] = 'Load Updated';
			}
			if($data['LoadId'] == '') {
				$json['LoadId'] = $LoadId;
			}
			
			//start updating facility for lot
			if($data['LotStatus'] == '1') {
				$query10 = "update loads set FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}
			//end updating facility for lot
			
			//Image Saving code
			if(count($data['images']) > 0) {
				for($i=0;$i<count($data['images']);$i++) {
					if($data['images'][$i]['imageID'] == '') {
						$query1 = "insert into load_images(filename,LoadId) values ('".mysqli_real_escape_string($this->connectionlink,$data['images'][$i]['filename'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."')";
						$q1 = mysqli_query($this->connectionlink,$query1);
					}
				}
			}
			//End Image Saving code			

			$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row4=mysqli_fetch_assoc($q4)) {
					$images[$i] = $row4;
					$i++;
				}
			}
			if(count($images) > 0)
				$json['images'] = $images;
			else
				$json['images'] = array();
			
			
			//Start updating pallet received date If load update
			if($data['LoadId'] != '') { // If updating load
				$query5 =  "update pallet_location_tracking set DateReceived = '".mysqli_real_escape_string($this->connectionlink,$data['DateReceived'])."' where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."' and ReceiveType = 'Direct'";
				$q5 = mysqli_query($this->connectionlink,$query5);
			}
			//End updating pallet received date If load update
			
			
			//Insert into Load Tracking
			if($data['LoadId'] == '') {
				$action = 'Load Created, Security Guard ID : '.$data['LoadCreationSecurityGuardID'].', Receiving Operator ID : '.$data['LoadCreationReceivingOperatorID'].', Auditor ID : '.$data['LoadCreationAuditorID'];
			} else {
				$action = 'Load Updated, Security Guard ID : '.$data['LoadCreationSecurityGuardID'].', Receiving Operator ID : '.$data['LoadCreationReceivingOperatorID'].', Auditor ID : '.$data['LoadCreationAuditorID'];
			}
			$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."')";			
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End Inserting into Load Tracking


			//Start insert record in TPVR History
			if($data['LoadId'] == '') {
				$query20 = "insert into tpvr_history (InventoryType,InventoryID,OperatorID,AuditorID,CreatedDate,CreatedBy,Action) values ('Load','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','New Load Created')";							
			} else {
				$query20 = "insert into tpvr_history (InventoryType,InventoryID,OperatorID,AuditorID,CreatedDate,CreatedBy,Action) values ('Load','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','Load Updated')";
			}
			$q20 = mysqli_query($this->connectionlink,$query20);
			//End insert record in TPVR History
			
			
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Data";
		}
		return json_encode($json);
	}
	
	public function CreateLoadFromLot ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}		
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);
		//return json_encode($json);
		$data['idCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idCustomer']) : 0 ;
		$data['idTruckingCompany'] = (mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) : 0 ;
		$data['idRefCustomer'] = (mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer']) : 0 ;
		$data['idServiceType'] = (mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) : 0 ;
		$data['LotStatus'] = (mysqli_real_escape_string($this->connectionlink,$data['LotStatus']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['LotStatus']) : 0 ;
		$data['ProcessID'] = (mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) : 0 ;
		
		
		$LoadId = $data['LoadId'];
		//Start check If Load Received or not
		$query1 = "select * from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$json['Success'] = false;
			$json['Result'] = "Load already received";
			return json_encode($json);
		} else {
			
		}
		//End check If Load Received or not
		$query = "insert into loads (idCustomer,LoadId,idTruckingCompany,idRefCustomer,idServiceType,LoadDescription,SpecialInstructions,DateReceived,DatePickup,EstimatedCompletionDate,DateCreated,DateUpdated,CustomerTag,idUser,FacilityID,Damaged,DamagedReason,ProcessID,LotStatus,pickup,TransportationCost,JabilInternalOrder,LoadValueEstimate,TransportationCommissionPercent,TotalTransportationCost,LogisticCharges,AdvancedPayment,SealNo,LoadCreationSecurityGuardID,LoadCreationReceivingOperatorID,LoadCreationAuditorID,SealNo2,TrailerID,SealNo3) values ('".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany'])."','".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['idServiceType'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."','".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."','".mysqli_real_escape_string($this->connectionlink,$data['DateReceived'])."','".mysqli_real_escape_string($this->connectionlink,$data['DatePickup'])."','".mysqli_real_escape_string($this->connectionlink,$data['EstimatedCompletionDate'])."',NOW(),NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['CustomerTag'])."','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."','".mysqli_real_escape_string($this->connectionlink,$data['DamagedReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."','0','".mysqli_real_escape_string($this->connectionlink,$data['pickup'])."','".mysqli_real_escape_string($this->connectionlink,$data['TransportationCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['JabilInternalOrder'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadValueEstimate'])."','".mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent'])."','".mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges'])."','".mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationSecurityGuardID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."')"; 			
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {			
			$json['Success'] = true;
			if($data['LoadId'] == '') {
				$json['Result'] = 'Load Created with LoadID '.$LoadId;
			} else {
				$json['Result'] = 'Load Updated';
			}
			if($data['LoadId'] == '') {
				$json['LoadId'] = $LoadId;
			}
			
			
			
			//Image Saving code
			if(count($data['images']) > 0) {
				for($i=0;$i<count($data['images']);$i++) {
					if($data['images'][$i]['imageID'] == '') {
						$query1 = "insert into load_images(filename,LoadId) values ('".mysqli_real_escape_string($this->connectionlink,$data['images'][$i]['filename'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."')";
						$q1 = mysqli_query($this->connectionlink,$query1);
					}
				}
			}
			//End Image Saving code			

			$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row4=mysqli_fetch_assoc($q4)) {
					$images[$i] = $row4;
					$i++;
				}
			}
			
			//Insert into Load Tracking			
			$action = 'Load Received From LOT, Security Guard ID : '.$data['LoadCreationSecurityGuardID'].', Receiving Operator ID : '.$data['LoadCreationReceivingOperatorID'].', Auditor ID : '.$data['LoadCreationAuditorID'];
			$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."')";			
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End Inserting into Load Tracking
			
			$query20 = "insert into tpvr_history (InventoryType,InventoryID,OperatorID,AuditorID,CreatedDate,CreatedBy,Action) values ('Load','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadCreationAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','Load received from LOT')";			
			$q20 = mysqli_query($this->connectionlink,$query20);
			
			//Start Update lot_details with LotStatus 0
			$query11 = "update lot_details set LotStatus = 0 where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q11 = mysqli_query($this->connectionlink,$query11);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End Update lot_details with LotStatus 0
			
			//Start Update pickup with Received 
			if($data['pickup'] != '') {
				$query11 = "update pickups set LoadReceived = 1 where pickup = '".mysqli_real_escape_string($this->connectionlink,$data['pickup'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
			}
			//End Update pickup with Received 
			
			//Start Creating Pallets
			$query = "select * from lot_pallets where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row=mysqli_fetch_assoc($q)) {
					$row['statusChangedBy'] = 0 ;
					$row['updatedWarehouseLocationId'] = 0;
					$row['receivedWarehouseLocationId'] = 0;
					$row['closestatus'] = 0;
					$row['closedBy'] = 0;//closedDate
					$row['oldpalletid'] = 0;
					$row['SortPalletCombination'] = 0;
					$row['SortPallet'] = 0;
					$row['dismantledStationID'] = 0;
					//$query1 = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,PalletFacilityID,idPackage,PackageWeight,totalWeight,pallet_netweight,AcquisitionCost,status,statusChangedDate,statusChangedBy,updatedWarehouseLocationId,receivedWarehouseLocationId,transferStatus,closestatus,closedBy,closedDate,oldpalletid,SortPalletCombination,SortPallet,AuditBased,MigratedDate,LotPalletId,SAPStandardCost,Charges,RackType) values ('".$row['LoadId']."','".$row['PalletDescription']."','".$row['WarehouseLocationId']."','".$row['PalletFacilityID']."','".$row['idPackage']."','".$row['PackageWeight']."','".$row['totalWeight']."','".$row['pallet_netweight']."','".$row['AcquisitionCost']."','".$row['status']."','".$row['statusChangedDate']."','".$row['statusChangedBy']."','".$row['updatedWarehouseLocationId']."','".$row['receivedWarehouseLocationId']."','".$row['transferStatus']."','".$row['closestatus']."','".$row['closedBy']."',NOW(),'".$row['oldpalletid']."','".$row['SortPalletCombination']."','".$row['SortPallet']."','".$row['AuditBased']."','".$row['MigratedDate']."','".$row['idPallet']."','".$row['SAPStandardCost']."','".$row['Charges']."','".$row['RackType']."')";
					//$query1 = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,PalletFacilityID,idPackage,PackageWeight,totalWeight,pallet_netweight,AcquisitionCost,status,statusChangedDate,statusChangedBy,updatedWarehouseLocationId,receivedWarehouseLocationId,transferStatus,closestatus,closedBy,closedDate,oldpalletid,SortPalletCombination,SortPallet,AuditBased,MigratedDate,LotPalletId,SAPStandardCost,Charges,RackType,SealNo1,SealNo2,CreatedDate,CreatedBy) values ('".$row['LoadId']."','".$row['PalletDescription']."','".$row['WarehouseLocationId']."','".$row['PalletFacilityID']."','".$row['idPackage']."','".$row['PackageWeight']."','".$row['totalWeight']."','".$row['pallet_netweight']."','".$row['AcquisitionCost']."','".$row['status']."','".$row['statusChangedDate']."','".$row['statusChangedBy']."','".$row['updatedWarehouseLocationId']."','".$row['receivedWarehouseLocationId']."','".$row['transferStatus']."','".$row['closestatus']."','".$row['closedBy']."',NOW(),'".$row['oldpalletid']."','".$row['SortPalletCombination']."','".$row['SortPallet']."','".$row['AuditBased']."','".$row['MigratedDate']."','".$row['idPallet']."','".$row['SAPStandardCost']."','".$row['Charges']."','".$row['RackType']."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo2'])."',NOW(),'".$_SESSION['user']['UserId']."')";					
					//$PalletID = $this->GetRandomPallet();
					$query1 = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,PalletFacilityID,idPackage,PackageWeight,totalWeight,pallet_netweight,AcquisitionCost,status,updatedWarehouseLocationId,receivedWarehouseLocationId,transferStatus,closestatus,oldpalletid,SortPalletCombination,SortPallet,AuditBased,MigratedDate,LotPalletId,SAPStandardCost,Charges,RackType,SealNo1,SealNo2,CreatedDate,CreatedBy,PickupDC,SealNo3,SealNo4,Comments,awsipn) values ('".$row['LoadId']."','".trim($row['PalletDescription'])."','".$row['WarehouseLocationId']."','".$row['PalletFacilityID']."','".$row['idPackage']."','".$row['PackageWeight']."','".$row['totalWeight']."','".$row['pallet_netweight']."','".$row['AcquisitionCost']."','4','".$row['updatedWarehouseLocationId']."','".$row['receivedWarehouseLocationId']."','".$row['transferStatus']."','".$row['closestatus']."','".$row['oldpalletid']."','".$row['SortPalletCombination']."','".$row['SortPallet']."','".$row['AuditBased']."','".$row['MigratedDate']."','".$row['idPallet']."','".$row['SAPStandardCost']."','".$row['Charges']."','".$row['RackType']."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo2'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['PickupDC'])."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$row['SealNo4'])."','".mysqli_real_escape_string($this->connectionlink,$row['Comments'])."','".mysqli_real_escape_string($this->connectionlink,$row['awsipn'])."')";					
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					$new_pallet_id = mysqli_insert_id($this->connectionlink);
					//$new_pallet_id = $PalletID;
					//Start creating pallet_items
					$query2 = "select * from lot_pallet_items where palletId = '".$row['idPallet']."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row2=mysqli_fetch_assoc($q2)) {							
							$query3 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,PalletItemsStatus,palletType,parentPallet,idWarehouseLocation,idPackage,totalWeight,customdismantle,PriceUNIT,PricingLevel) values ('".$new_pallet_id."','".$row2['weight']."','".$row2['quantity']."','".$row2['idProductClass']."','".$row2['idProductCategory']."','".$row2['weightPercent']."','0','".$row2['palletType']."','".$row2['parentPallet']."','".$row2['idWarehouseLocation']."','".$row2['idPackage']."','".$row2['totalWeight']."','".$row2['customdismantle']."','".$row2['PriceUNIT']."','".$row2['PricingLevel']."')";
							$q3 = mysqli_query($this->connectionlink,$query3);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);	
							}
						}
					}
					
					//End creating pallet_items
					$i = $i + 1;
				}
				$query3 = "update loads set TotalPallets = '".$i."' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				
				$query3 = "update lot_details set LotStatus = '0' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				
				$json['Success'] = true;
				$json['Result'] = 'Load Received';
				return json_encode($json);
			} else {
				$query3 = "update loads set TotalPallets = '0' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);

				$json['Success'] = true;
				$json['Result'] = 'Load Created';
				return json_encode($json);
			}
			//End Creating Pallets
			
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Data";
		}
		return json_encode($json);
	}

	function GenerateLoadID ($customerID) {
		$query = "select CustomerShotCode,CustomerID from customer where CustomerID='".mysqli_real_escape_string($this->connectionlink,$customerID)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);			
		}
		//$loadID = $row['CustomerShotCode'].$this->RamdomGenerator();
		$loadID = $this->RamdomGenerator1($row['CustomerID']);
		return $loadID;
	}
	function RamdomGenerator($minLength = 6, $maxLength = 6, $maxSymbols = 0) {
		$symbolCount = 0;
		srand((double)microtime() * 1000003);
		for ($i = 0; $i < rand($minLength, $maxLength); $i++)
		{
			do
			{
				$char = rand(33, 126);
				$symbolCount += $isSymbol = (!in_array($char, range(48, 57)));
				if ($symbolCount <= $maxSymbols || !$isSymbol)
				{
					break;
				}
			}
			while (true);
			$passwd = sprintf('%s%c', isset($passwd) ? $passwd : NULL, $char);
		}
		return $passwd;
	}
	
	function RamdomGenerator1($CustomerID) {
		while(1) {
			// generate unique random number
			$randomNumber = rand(1000000000, 9999999999);
			$randomNumber = PRE.$randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `loads` WHERE LoadId = '".$randomNumber."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}

	public function GetCategories ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from product_category where ProductClassID='".mysqli_real_escape_string($this->connectionlink,$data['classID'])."' and CategoryStatus='1' and AccountID='".$_SESSION['user']['AccountID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Categories Available";
		}
		return json_encode($json);
	}

	public function GetLocations($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select l.*,lab.lable_name from location l,lables lab where l.FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND l.Locked != 1 AND l.LocationID != 0 and l.lable_name = lab.id and isnull(l.GroupID) order by l.LocationName asc";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Locations Available";
			return json_encode($json);
		}
		if($data['LocationID']) {			
			//$query1 = "select l.*,lab.lable_name from location l,labels lab where l.LocationID = '".$data['LocationID']."' and l.lable_name = lab.id";
			$query1 = "select l.*,lab.lable_name from location l,lables lab where l.LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."' and l.lable_name = lab.id";
			$q1 = mysqli_query($this->connectionlink,$query1);
			$row1 = mysqli_fetch_assoc($q1);
			//$json['Success'] = true;
			$json['CurrentLocation'] = $row1;
		}
		return json_encode($json);
	}

	public function CreatePallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		$data['totalWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) : 0 ;
		$Pallet_netweight = floatval($data['totalWeight']) - floatval($data['PackageWeight']);
		$Pallet_TotWeight = $data['totalWeight'];
		if($Pallet_netweight < 0) {
			$Pallet_netweight = 0;
		}

		if($data['SecurityConcern'] == 0) {
			$data['SecurityConcernReason'] = '';
		}

		if($data['SafetyConcern'] == 0) {
			$data['SafetyConcernReason'] = '';
		}


		$airportcode = substr($data['PickupDC'], 0, 3);
		$numbercode = substr($data['PickupDC'], 3);

		if($data['PickupDC'] == '' || $data['PickupDC'] == '0') {
			$airportcode = 'CVG';
			$numbercode = '110';
		}
		if(substr($data['PickupDC'], -5) == 'Xdock' ) {
			$airportcode = substr($data['PickupDC'], 0, 3);
			$numbercode = '000';
		}
		if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
			$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."'";							
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row20 = mysqli_fetch_assoc($q20);
				// if($row20['Locked'] == '1') {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Location is Locked';
				// 	return json_encode($json);
				// }
				$data['location'] = $row20['LocationID'];
				$LocationType = $row20['LocationType'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Location';
				return json_encode($json);
			}
		} else if($data['Group'] != '') {
			//Start get free location from group selected
			//$query = "select LocationID,LocationType from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['Group'])."'";
			$query = "select l.LocationID,l.LocationType from location l,location_group lg where l.Locked = '2' and l.LocationStatus = '1' and l.GroupID = lg.GroupID and lg.GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['Group'])."' limit 1";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$data['location'] = $row['LocationID'];
				$LocationType = $row['LocationType'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No locations available, in selected group';
				return json_encode($json);
			}
			//End get free location from group selected			
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Location and Location Group';
			return json_encode($json);
		}

		// $json['Success'] = false;
		// $json['Result'] = $LocationType;
		// return json_encode($json);

		if($data['idPallet'] == '') { //If New Pallet

			// $json['Success'] = false;
			// $json['Result'] = $LocationType;
			// return json_encode($json);

			//Start check for Quarantine user or not
			if($LocationType == 'Quarantine') {
				$QuarantineUser = $this->isQuarantineUser();
				if($QuarantineUser != '1') {
					$json['Success'] = false;
					$json['Result'] = 'User does not have permission to move into Quarantine Location';
					return json_encode($json);
				}
			}
			//End check for Quarantine user or not

			if($row20['Locked'] == '1') {
				$json['Success'] = false;
				$json['Result'] = 'Location is Locked';
				return json_encode($json);
			}

			// $duplicate = $this->CheckDuplicate('New','pallets','PalletDescription',$data['description'],false,'','');

			// if($duplicate) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'AWS ID already exists';
			// 	return json_encode($json);
			// }

			//Start Validate for unique bag serial
			// $query = "select * from bag_details where BagSerial = '".mysqli_real_escape_string($this->connectionlink,$data['BagSerial'])."'";
			// $q = mysqli_query($this->connectionlink,$query);
			// if(mysqli_error($this->connectionlink)) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = mysqli_error($this->connectionlink);
			// 	return json_encode($json);	
			// }
			// if(mysqli_affected_rows($this->connectionlink) > 0) {
			// 	$row = mysqli_fetch_assoc($q);
			// 	if($row['BagStatusID'] == 1 || $row['BagStatusID'] == 2 || $row['BagStatusID'] == 3) {
			// 		$json['Success'] = false;
			// 		$json['Result'] = 'Bag Serial already exists';
			// 		return json_encode($json);	
			// 	}
			// }
			//End Validate for unique bag serial		
			
			//Start validate process location of customer
			// $isLocationPossible = $this->isPalletLocationMatches($data['loadid'],$data['location']);
			// if($isLocationPossible['Success'] == false) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = $isLocationPossible['Error'];
			// 	return json_encode($json);	
			// }

			// $json['Success'] = false;
			// $json['Result'] = $isLocationPossible;
			// return json_encode($json);
			//End validate process location of customer
			
			//$PalletID = $this->GetRandomPallet();
			$query = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,PalletFacilityID,closestatus,RackType,PalletUpdateSecurityGuardID,PalletUpdateReceivingOperatorID,PalletUpdateAuditorID,SealNo1,SealNo2,CreatedDate,CreatedBy,PickupDC,SecurityConcern,SecurityConcernReason,SafetyConcern,SafetyConcernReason,awsipn,SealNo3,SealNo4,Comments,BagSerial,BagCondition,Model,GroupName,UniversalModelNumber,PickupDCAirportCode,PickupDCNumberCode,ActualReceivedDate,ForFALab) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,trim($data['description']))."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."','".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['RackType'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateSecurityGuardID'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateAuditorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['PickupDC'])."','".mysqli_real_escape_string($this->connectionlink,$data['SecurityConcern'])."','".mysqli_real_escape_string($this->connectionlink,$data['SecurityConcernReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['SafetyConcern'])."','".mysqli_real_escape_string($this->connectionlink,$data['SafetyConcernReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['awsipn'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."','".mysqli_real_escape_string($this->connectionlink,$data['Comments'])."','".mysqli_real_escape_string($this->connectionlink,$data['BagSerial'])."','".mysqli_real_escape_string($this->connectionlink,$data['BagCondition'])."','".mysqli_real_escape_string($this->connectionlink,$data['Model'])."','".mysqli_real_escape_string($this->connectionlink,$data['GroupName'])."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$airportcode)."','".mysqli_real_escape_string($this->connectionlink,$numbercode)."',NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['ForFALab'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				//$palletID = $PalletID;
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$palletID)."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);

				$record_location = $this->RecordLocationHistory('Pallet',$palletID,'0',$data['location'],'New Pallet Created');
				if($record_location != '1') {
					$json['Success'] = false;
					$json['Result'] = $record_location."123123";
					return json_encode($json);	
				}
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}						

				$query1 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				// for($i=0;$i<count($data['Splits']);$i++) {
				// 	$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
				// 	$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
				// 	if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
				// 		$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
				// 		//$quantity = 0;
				// 	} else {
				// 		$quantity = $data['Splits'][$i]['quantity'];
				// 	}
				// 	$query2 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);	
				// 	}
				// }

				//Update Pallet Count in loads 
				$query3 = "update loads set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count
				//Insert into Pallet Tracking
				//$action = 'Pallet Created, Security Guard ID : '.$data['PalletUpdateSecurityGuardID'].', Receiving Operator ID : '.$data['PalletUpdateReceivingOperatorID'].', Auditor ID : '.$data['PalletUpdateAuditorID'];
				$action = 'Pallet Created,  Receiving Operator ID : '.$data['PalletUpdateReceivingOperatorID'].', Auditor ID : '.$data['PalletUpdateAuditorID'];
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				
				
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','Pallet received at location ','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}			
				//End Inserting into Pallet Tracking

				
				//Start insert record in TPVR History	
				$query = "insert into tpvr_history (InventoryType,InventoryID,OperatorID,AuditorID,CreatedDate,CreatedBy,Action,SealNo1,SealNo2,SealNo3,SealNo4) values ('Pallet','".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','New Pallet Created','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."')";
				$q = mysqli_query($this->connectionlink,$query);	
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}			
				//End insert record in TPVR History
				
				//Start getting load received date
				$query6 = "select DateReceived from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					$DateReceived = $row6['DateReceived'];
				}
				//End getting load received date
				$data['FromFacility'] = (mysqli_real_escape_string($this->connectionlink,$data['FromFacility']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['FromFacility']) : 0 ;
				//Start inserting into pallet location tracking
				$query4 = "insert into pallet_location_tracking (LoadId,PalletId,ReceiveType,DateReceived,FromFacility,ToFacility,UserId) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$palletID)."','Direct','".mysqli_real_escape_string($this->connectionlink,$DateReceived)."','".mysqli_real_escape_string($this->connectionlink,$data['FromFacility'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."','".$_SESSION['user']['UserId']."')";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				//End inserting into pallet location tracking

				//Start Check If AWSID Exists
				$data['idPallet'] = $palletID;
				$ifExists = $this->ValidateRackID($data);
				//End Check If AWSID Exists


				$json['Success'] = true;
				$json['Result'] = 'Container Created';
				$json['palletId'] = $palletID;
				$json['desc'] = $data['description'];
			}

			if($data['BagSerial'] != '') {
				$BagID = $this->CreateBag($palletID,$data['BagSerial'],$data['PickupDC'],$data['BagCondition']);
				if($BagID > 0) {
					$query5 = "update pallets set BagID = '".$BagID."' where idPallet = '".$palletID."'";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
				}
			}

			//Sart insert safety and security concern reasons
			if($data['SecurityConcern'] == '1') {
				for($i=0;$i<count($data['SecurityList']);$i++) {
					if($data['SecurityList'][$i]['Selected'] == '1') {
						$selected = true;
					} else {
						$selected = false;
					}
					if($selected) {
						$query = "insert into pallet_security_concern_reasons (idPallet,ReasonID,CreatedDate,CreatedBy) values ('".$palletID."','".mysqli_real_escape_string($this->connectionlink,$data['SecurityList'][$i]['ReasonID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink).$query;
							return json_encode($json);	
						}		
					}		
				}
			}

			if($data['SafetyConcern'] == '1') {
				for($i=0;$i<count($data['SafetyList']);$i++) {				
					if($data['SafetyList'][$i]['Selected'] == '1') {
						$selected = true;
					} else {
						$selected = false;
					}

					if($selected) {
						$query = "insert into pallet_safety_concern_reasons (idPallet,ReasonID,CreatedDate,CreatedBy) values ('".$palletID."','".mysqli_real_escape_string($this->connectionlink,$data['SafetyList'][$i]['ReasonID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink).$query;
							return json_encode($json);	
						}
					}	
				}
			}					
			//End insert safety and security conern reasons

		} else { //If Update Pallet

			$duplicate = $this->CheckDuplicate('Edit','pallets','PalletDescription',mysqli_real_escape_string($this->connectionlink,$data['description']),false,'idPallet',$data['idPallet']);	

			if($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'AWS ID already exists';
				return json_encode($json);
			}
			
			//Start validate process location of customer
			$isLocationPossible = $this->isPalletLocationMatches($data['loadid'],$data['location']);
			if($isLocationPossible['Success'] == false) {
				$json['Success'] = false;
				$json['Result'] = $isLocationPossible['Error'];
				return json_encode($json);	
			}			
			//End validate process location of customer
			
			if($data['status'] == '4' || $data['BagID'] == '' || ! $data['BagID']) { //If updating pallet for the first time
				//Start Validate for unique bag serial
				$query = "select * from bag_details where BagSerial = '".mysqli_real_escape_string($this->connectionlink,$data['BagSerial'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['BagStatusID'] == 1 || $row['BagStatusID'] == 2 || $row['BagStatusID'] == 3) {
						$json['Success'] = false;
						$json['Result'] = 'Bag Serial already exists';
						return json_encode($json);
					}
				}
				//End Validate for unique bag serial
			}

			$sqlselloc = "Select WarehouseLocationId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);

			if($rowselloc['WarehouseLocationId'] != $data['location']) { // Location Changed
				if($row20['Locked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Location is Locked';
					return json_encode($json);
				}

				//Start check for Quarantine user or not
				if($LocationType == 'Quarantine') {
					$QuarantineUser = $this->isQuarantineUser();
					if($QuarantineUser != '1') {
						$json['Success'] = false;
						$json['Result'] = 'User does not have permission to move into Quarantine Location';
						return json_encode($json);
					}
				}
				//End check for Quarantine user or not
			}

			$query = "update pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,trim($data['description']))."',WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',idPackage='".mysqli_real_escape_string($this->connectionlink,$data['package'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."',RackType = '".mysqli_real_escape_string($this->connectionlink,$data['RackType'])."',PalletUpdateSecurityGuardID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateSecurityGuardID'])."',PalletUpdateReceivingOperatorID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateReceivingOperatorID'])."',PalletUpdateAuditorID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateAuditorID'])."',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',status = '1',PickupDC = '".mysqli_real_escape_string($this->connectionlink,$data['PickupDC'])."',SecurityConcern = '".mysqli_real_escape_string($this->connectionlink,$data['SecurityConcern'])."',SecurityConcernReason = '".mysqli_real_escape_string($this->connectionlink,$data['SecurityConcernReason'])."',SafetyConcern = '".mysqli_real_escape_string($this->connectionlink,$data['SafetyConcern'])."',SafetyConcernReason = '".mysqli_real_escape_string($this->connectionlink,$data['SafetyConcernReason'])."',awsipn = '".mysqli_real_escape_string($this->connectionlink,$data['awsipn'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',Comments = '".mysqli_real_escape_string($this->connectionlink,$data['Comments'])."',BagSerial = '".mysqli_real_escape_string($this->connectionlink,$data['BagSerial'])."',Model = '".mysqli_real_escape_string($this->connectionlink,$data['Model'])."',GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['GroupName'])."',UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',PickupDCAirportCode = '".mysqli_real_escape_string($this->connectionlink,$airportcode)."',PickupDCNumberCode = '".mysqli_real_escape_string($this->connectionlink,$numbercode)."',ForFALab = '".mysqli_real_escape_string($this->connectionlink,$data['ForFALab'])."' ";
			if($data['status'] == '4') { //If updating pallet for the first time
				$query = $query . ",ActualReceivedDate = NOW() ";
			}
			$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			else {
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$rowselloc['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
				}
			}

			if($rowselloc['WarehouseLocationId'] != $data['location']) {
				$this->RecordLocationHistory('Pallet',$data['idPallet'],$rowselloc['WarehouseLocationId'],$data['location'],'Location Changed for pallet in Receive Screen');

				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Pallet received at location ','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
			}
			

			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";				
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
			}


			if(count($data['Splits']) > 0) {				
				// for($i=0;$i<count($data['Splits']);$i++) {
				// 	$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
				// 	$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
				// 	if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
				// 		$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
				// 		//$quantity = 0;
				// 	} else {
				// 		$quantity = $data['Splits'][$i]['quantity'];
				// 	}

				// 	if($data['Splits'][$i]['id'] != '') {																								
				// 		$query2 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
				// 	} else {						
				// 		$query2 = "insert into pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
				// 	}					
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		//return json_encode($json);	
				// 	}
				// }
			}			
			
			//Insert into Pallet Tracking
			//$action = 'Pallet Updated, Security Guard ID : '.$data['PalletUpdateSecurityGuardID'].', Receiving Operator ID : '.$data['PalletUpdateReceivingOperatorID'].', Auditor ID : '.$data['PalletUpdateAuditorID'];
			$action = 'Pallet Updated, Receiving Operator ID : '.$data['PalletUpdateReceivingOperatorID'].', Auditor ID : '.$data['PalletUpdateAuditorID'];
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End Inserting into Pallet Tracking


			//Start insert record in TPVR History	
			$query = "insert into tpvr_history (InventoryType,InventoryID,OperatorID,AuditorID,CreatedDate,CreatedBy,Action,SealNo1,SealNo2,SealNo3,SealNo4) values ('Pallet','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateReceivingOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletUpdateAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','Pallet Updated','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."')";
			$q = mysqli_query($this->connectionlink,$query);				
			//End insert record in TPVR History

			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $data['idPallet'];
			$json['desc'] = $data['description'];



			if(($data['BagSerial'] != '' && $data['status'] == '4') || ($data['BagSerial'] != '' && !$data['BagID'])) {
				$BagID = $this->CreateBag($data['idPallet'],$data['BagSerial'],$data['PickupDC'],$data['BagCondition']);
				if($BagID > 0) {
					$query5 = "update pallets set BagID = '".$BagID."',BagSerial = '".mysqli_real_escape_string($this->connectionlink,$data['BagSerial'])."',BagCondition = '".mysqli_real_escape_string($this->connectionlink,$data['BagCondition'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
				}
			}

			//Stat deleting Security and Safety concerns
			$query10 = "delete from pallet_security_concern_reasons where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}

			$query10 = "delete from pallet_safety_concern_reasons where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End deleting Security and Safety concerns

			//Sart insert safety and security concern reasons
			if($data['SecurityConcern'] == '1') {
				for($i=0;$i<count($data['SecurityList']);$i++) {
					if($data['SecurityList'][$i]['Selected'] == '1') {
						$selected = true;
					} else {
						$selected = false;
					}
					if($selected) {
						$query = "insert into pallet_security_concern_reasons (idPallet,ReasonID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['SecurityList'][$i]['ReasonID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink).$query;
							return json_encode($json);	
						}		
					}		
				}
			}

			if($data['SafetyConcern'] == '1') {
				for($i=0;$i<count($data['SafetyList']);$i++) {				
					if($data['SafetyList'][$i]['Selected'] == '1') {
						$selected = true;
					} else {
						$selected = false;
					}

					if($selected) {
						$query = "insert into pallet_safety_concern_reasons (idPallet,ReasonID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['SafetyList'][$i]['ReasonID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink).$query;
							return json_encode($json);	
						}
					}	
				}
			}					
			//End insert safety and security conern reasons


		}
		return json_encode($json);
	}
	public function CreatePallet1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}		
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		$Pallet_netweight = floatval($data['totalWeight']) - floatval($data['PackageWeight']);		
		if($Pallet_netweight < 0) {
			$Pallet_netweight = 0;
		}
		if($data['idPallet'] == '') { //If New Pallet
			$query = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,closestatus,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$data['description'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."',0,NOW(),'".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$palletID)."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
				} else {
					$quantity = $data['quantity'];
				}				
				$query1 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				// for($i=0;$i<count($data['Splits']);$i++) {
				// 	$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
				// 	if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
				// 		$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
				// 	} else {
				// 		$quantity = $data['Splits'][$i]['quantity'];
				// 	}
				// 	$query2 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."')";
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 	}
				// }
				//Update Pallet Count in loads 
				$query3 = "update loads set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count

				//Insert into Pallet Tracking
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','Pallet Created','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				//End Inserting into Pallet Tracking

				$json['Success'] = true;
				$json['Result'] = 'Pallet Created';
				$json['palletId'] = $palletID;
			}
		} else { //If Update Pallet
			$sqlselloc = "Select WarehouseLocationId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);
			$query = "update pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,$data['description'])."',WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',idPackage='".mysqli_real_escape_string($this->connectionlink,$data['package'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."' where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			else {
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$rowselloc['WarehouseLocationId'])."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".$data['location']."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
			}
			if(count($data['Splits']) > 0) {
				// for($i=0;$i<count($data['Splits']);$i++) {
				// 	$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
				// 	if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
				// 		$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
				// 	} else {
				// 		$quantity = $data['Splits'][$i]['quantity'];
				// 	}

				// 	if($data['Splits'][$i]['id'] != '') {																								
				// 		$query2 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
				// 	} else {						
				// 		$query2 = "insert into pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."')";
				// 	}
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 	}
				// }
			}

			//Insert into Pallet Tracking
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Pallet Updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = $query2;
				return json_encode($json);
			}
			//End Inserting into Pallet Tracking
			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $palletID;
		}
		return json_encode($json);
	}
	public function GetLoadDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from loads where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$sqlpalcou = "Select SUM(LPI.totalWeight) as pallettotweight from pallets LP,pallet_items LPI
				Where LPI.palletId = LP.idPallet
				AND LP.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
			$qpalcou = mysqli_query($this->connectionlink,$sqlpalcou);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			$rowpalcou = mysqli_fetch_assoc($qpalcou);
			$row['PalletTotWeight'] = $rowpalcou['pallettotweight'];
			$row['TransportationCost'] = floatval($row['TransportationCost']);
			$row['TotalTransportationCost'] = floatval($row['TotalTransportationCost']);
			$row['TransportationCommissionPercent'] = floatval($row['TransportationCommissionPercent']);
			$row['LogisticCharges'] = floatval($row['LogisticCharges']);
			$row['ReceivedHours'] = floatval($row['ReceivedHours']);
			$row['ReceivePricePerHour'] = floatval($row['ReceivePricePerHour']);
			$row['AdvancedPayment'] = floatval($row['AdvancedPayment']);
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i=0;
					while($row4=mysqli_fetch_assoc($q4)) {
						$images[$i] = $row4;
						$i++;
					}
				}
				if(count($images) > 0)
					$row['images'] = $images;
				else
					$row['images'] = array();
			}
			$json['Success'] = true;
			$json['Result'] = $row;
			//$query1 = "select p.*,l.LocationName,pkg.packageName from pallets p,location l,package pkg where p.WarehouseLocationId = l.LocationID and p.idPackage=pkg.idPackage and LoadId = '".$data['LoadID']."' ";
			
			$query1 = "select p.*,l.LocationName,pkg.packageName from pallets p 
			left join location l on p.WarehouseLocationId = l.LocationID 
			left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' ";
			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row1['PackageWeight'] = floatval($row1['PackageWeight']);
					$row1['totalWeight'] = floatval($row1['totalWeight']);
					$row1['AcquisitionCost'] = floatval($row1['AcquisitionCost']);					
					//$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from pallet_items pi,product_category cat,product_class cla where pi.idProductClass = cla.ProductClassID and pi.idProductCategory = cat.ProductCatID and pi.palletId = '".$row1['idPallet']."' order by pi.id";
					
					$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from pallet_items pi 
					left join product_category cat on pi.idProductCategory = cat.ProductCatID 
					left join product_class cla on pi.idProductClass = cla.ProductClassID where pi.palletId = '".$row1['idPallet']."' order by pi.id";
					
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$j=0;
						$splits = array();
						while($row2 = mysqli_fetch_assoc($q2)) {
							$row2['weight'] = floatval($row2['weight']);
							$row2['quantity'] = floatval($row2['quantity']);
							$row2['weightPercent'] = floatval($row2['weightPercent']);
							$splits[$j] = $row2;
							$j++;
						}
						$row1['Splits'] = $splits;
					}

					//Start get Pallet Safety Reasons
					$query3 = "select * from safety_concern_reasons where Status = 'Active'";
					$q3 = mysqli_query($this->connectionlink,$query3);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					$SafetyList = array();
					$a = 0;
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row3 = mysqli_fetch_assoc($q3)) {
							$query4 = "select * from pallet_safety_concern_reasons where idPallet = '".$row1['idPallet']."' and ReasonID = '".$row3['ReasonID']."'";
							$q4 = mysqli_query($this->connectionlink,$query4);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row4 = mysqli_fetch_assoc($q4);
								$row3['Selected'] = '1';
								$row3['PalletReasonID'] = $row4['PalletReasonID'];
							} else {
								$row3['Selected'] = '0';
							}
							$SafetyList[$a] = $row3;
							$a++;
						}
					}
					$row1['SafetyList'] = $SafetyList;
					//End get Pallet Safety Reasons
					
					

					//Start get Pallet Security Reasons
					$query3 = "select * from security_concern_reasons where Status = 'Active'";
					$q3 = mysqli_query($this->connectionlink,$query3);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					$SecurityList = array();
					$a = 0;
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row3 = mysqli_fetch_assoc($q3)) {
							$query4 = "select * from pallet_security_concern_reasons where idPallet = '".$row1['idPallet']."' and ReasonID = '".$row3['ReasonID']."'";
							$q4 = mysqli_query($this->connectionlink,$query4);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row4 = mysqli_fetch_assoc($q4);
								$row3['Selected'] = '1';
								$row3['PalletReasonID'] = $row4['PalletReasonID'];
							} else {
								$row3['Selected'] = '0';
							}
							$SecurityList[$a] = $row3;
							$a++;
						}
					}
					$row1['SecurityList'] = $SecurityList;
					//End get Pallet Security Reasons


					$pallets[$i] = $row1;
					$i++;
				}
				$json['pallets'] = $pallets;
			} else {
				$json['pallets'] = 'NO Pallets Available';
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Load';
		}
		return json_encode($json);
	}

	public function GetLoads($keyword) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//$query = "select LoadId from loads where LoadId like '%".$keyword."%'";
		if($_SESSION['user']['CustomerBased'] == 0) {
			if($_SESSION['user']['ReferenceCustomerBased'] == 1) {
				$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' and l.idRefCustomer in (select idRefCustomer from user_assigned_reference_customers where UserId='".$_SESSION['user']['UserId']."')";
			} else {
				$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."'";
			}
		} else {
			$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' and c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."')";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}

	public function RemoveSplit($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);		
		//Start Getting Palletid from splitid
		$query1 = "select palletId from pallet_items where id = '".mysqli_real_escape_string($this->connectionlink,$data['id'])."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row1 = mysqli_fetch_assoc($q1);
			$palletID = $row1['palletId'];
		}
		//End Getting Palletid from splitid
		//Insert into Pallet Tracking		
		$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','Pallet Split Deleted','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
		$q2 = mysqli_query($this->connectionlink,$query2);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = $query2;
			return json_encode($json);
		}		
		//End Inserting into Pallet Tracking
		$query = "delete from pallet_items where id='".mysqli_real_escape_string($this->connectionlink,$data['id'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = 'Deleted';
		}
		return json_encode($json);
	}
	
	public function GetReferenceCustomers($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from referencecustomer where idCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Reference Customers';
		}
		return json_encode($json);
	}

	public function GetInstructions($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select Instructions,SLADays,ServiceLevelAuditAssetReport,ServiceLevelRecyclingReport,ServiceLevelDataDestructionReport,SapId,PaymentCode,ServiceLoadAcknowledgementReport,ServiceInvoiceRequisition,ServicePurchaseOrderRequistion,ServiceTeardownReport,ServiceHarvestReport,ServiceAuditReport,ServiceRecycleReport,ServiceDataDestructionReport,ServiceSettlementNonRevReport,ServiceSettlementRevReport,ServiceAssayReport from customer where CustomerID='".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['ServiceLoadAcknowledgementReport'] == NULL)
			{
				$row['ServiceLoadAcknowledgementReport'] = 0;
			}
			if($row['ServiceInvoiceRequisition'] == NULL)
			{
				$row['ServiceInvoiceRequisition'] = 0;
			}
			if($row['ServicePurchaseOrderRequistion'] == NULL)
			{
				$row['ServicePurchaseOrderRequistion'] = 0;
			}
			if($row['ServiceTeardownReport'] == NULL)
			{
				$row['ServiceTeardownReport'] = 0;
			}
			if($row['ServiceHarvestReport'] == NULL)
			{
				$row['ServiceHarvestReport'] = 0;
			}
			if($row['ServiceAuditReport'] == NULL)
			{
				$row['ServiceAuditReport'] = 0;
			}
			if($row['ServiceRecycleReport'] == NULL)
			{
				$row['ServiceRecycleReport'] = 0;
			}
			if($row['ServiceDataDestructionReport'] == NULL)
			{
				$row['ServiceDataDestructionReport'] = 0;
			}
			if($row['ServiceSettlementNonRevReport'] == NULL)
			{
				$row['ServiceSettlementNonRevReport'] = 0;
			}
			if($row['ServiceSettlementRevReport'] == NULL)
			{
				$row['ServiceSettlementRevReport'] = 0;
			}
			if($row['ServiceAssayReport'] == NULL)
			{
				$row['ServiceAssayReport'] = 0;
			}
			$json['Success'] = true;
			$json['Result'] = $row['Instructions'];
			$json['SLADays'] = $row['SLADays'];
			$json['ServiceLevelAuditAssetReport'] = $row['ServiceLevelAuditAssetReport'];
			$json['ServiceLevelRecyclingReport'] = $row['ServiceLevelRecyclingReport'];
			$json['ServiceLevelDataDestructionReport'] = $row['ServiceLevelDataDestructionReport'];
			$json['ServiceLoadAcknowledgementReport'] = $row['ServiceLoadAcknowledgementReport'];
			$json['ServiceInvoiceRequisition'] = $row['ServiceInvoiceRequisition'];
			$json['ServicePurchaseOrderRequistion'] = $row['ServicePurchaseOrderRequistion'];
			$json['ServiceTeardownReport'] = $row['ServiceTeardownReport'];
			$json['ServiceHarvestReport'] = $row['ServiceHarvestReport'];
			$json['ServiceAuditReport'] = $row['ServiceAuditReport'];
			$json['ServiceRecycleReport'] = $row['ServiceRecycleReport'];
			$json['ServiceDataDestructionReport'] = $row['ServiceDataDestructionReport'];
			$json['ServiceSettlementNonRevReport'] = $row['ServiceSettlementNonRevReport'];
			$json['ServiceSettlementRevReport'] = $row['ServiceSettlementRevReport'];
			$json['ServiceAssayReport'] = $row['ServiceAssayReport'];
			//$json['SapId'] = $row['SapId'];
			$json['SapId'] = $row['PaymentCode'];
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No data Available';
		}
		return json_encode($json);
	}

	public function UploadImage($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "insert into load_images (filetype,filesize,image,LoadId) values ('".mysqli_real_escape_string($this->connectionlink,$data['filetype'])."','".mysqli_real_escape_string($this->connectionlink,$data['filesize'])."','".mysqli_real_escape_string($this->connectionlink,$data['base64'])."','".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$json['Success'] = true;
			$json['Result'] = "Image Saved";
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No data Available';
		}
		return json_encode($json);
	}

	public function UploadLoadImage($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		/*if($data['file']['type'] != 'application/vnd.ms-excel') {
			$json['Success'] = false;
			$json['Result'] = 'Invalid File type';	
			return json_encode($json);		
		}*/
		$filename = time().$data['file']['name'];
		$target_path = '../../load_images/'.$filename;
		if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			//$validFormat = $this->isValidFormat($target_path);			
			$json['Success'] = true;
			$json['Result'] = 'File Uploaded';
			$json['FileName'] = $filename;								
		} else{
			$json['Success'] = false;
			$json['Result'] = 'Problem with File uploading';
		}
		return json_encode($json);
	}
	public function DeleteLoadImage($data){
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "delete from load_images where imageID='".mysqli_real_escape_string($this->connectionlink,$data['imageID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = 'Image Deleted';
		}
		return json_encode($json);
	}

	public function GetPendingLoadsList() {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		$sql = "Select L.LoadId as LoadID, C.CustomerName as Customer, L.TotalPallets as Pallets, L.DateReceived as Date from loads L, customer C
				Where 
				C.CustomerID = L.idCustomer
				AND L.LotStatus = 1";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_affected_rows($this->connectionlink) > 0)
		{
			$i=0;
			while($row = mysqli_fetch_assoc($query))
			{
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		}
		else
		{
			$json['Success'] = false;
			$json['Result'] = "No Data Available";
		}
		return json_encode($json);
	}

	public function GeneratePendingLoadsxls($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$_SESSION['PendingLoadsxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function GetLotDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		$json['Success'] = true;
		$json['Result'] = $data['loadID'];
		return json_encode($json);
	}

	public function GetInterFacilityShipments() {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "select s.*,ss.Status from shipping s,shipping_status ss where s.AccountID = '".$_SESSION['user']['AccountID']."' and s.ShipmentStatusID = ss.ShipmentStatusID and s.DestinationFacilityID='".$_SESSION['user']['FacilityID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Shipments Available';
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function GetCurrentLocations() {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "select LocationID,LocationName from location where FacilityID='".$_SESSION['user']['FacilityID']."' and LocationStatus='1' and Locked='2'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = $query ;
			return json_encode($json);
		}
		return json_encode($json);
	}
	public function ChangeLocation($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);

		if($data['AssetScanID'] != '') {
			$sqlloc = "Select LocationID from asset where AssetScanID='".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$queryloc = mysqli_query($this->connectionlink,$sqlloc);
			$rowloc = mysqli_fetch_assoc($queryloc);
			$query = "update asset set FacilityID='".$_SESSION['user']['FacilityID']."',LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',DateUpdated=NOW(),UpdatedBy='".$_SESSION['user']['UserId']."',StatusID='2',StatusUpdatedDate=NOW(),StatusChangeddBy='".$_SESSION['user']['UserId']."' where AssetScanID='".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$rowloc['itemtype'] = 'Asset';
			$rowloc['itemid'] = $data['AssetScanID'];
		}

		if($data['GaylordUniqueID'] != '') {
			$query = "update gaylord set FacilityID='".$_SESSION['user']['FacilityID']."',LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',DateUpdated=NOW(),UpdatedBy='".$_SESSION['user']['UserId']."',Status='1',AssayReceivedDate = NOW() where GaylordUniqueID='".mysqli_real_escape_string($this->connectionlink,$data['GaylordUniqueID'])."'";
		}
		
		if($data['CustomPalletID'] != '') {
			$sqlloc = "Select LocationID from custompallet where CustomPalletID='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$queryloc = mysqli_query($this->connectionlink,$sqlloc);
			$rowloc = mysqli_fetch_assoc($queryloc);
			$query = "update custompallet set FacilityID='".$_SESSION['user']['FacilityID']."',LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',LastModifiedDate=NOW(),LastModifiedBy='".$_SESSION['user']['UserId']."',StatusID='1',StatusModifiedDate=NOW(),StatusModifiedBy='".$_SESSION['user']['UserId']."' where CustomPalletID='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$rowloc['itemtype'] = 'CustomPallet';
			$rowloc['itemid'] = $data['CustomPalletID'];
			
			//Start Updating facility of assets in custompallet
			$query1 = "update asset set FacilityID = '".$_SESSION['user']['FacilityID']."' where CustomPalletID='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Updating facility of assets in custompallet
		}
		if($data['idPallet'] != '') {
			$sqlloc = "Select WarehouseLocationId as LocationID,PalletFacilityID,LoadId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryloc = mysqli_query($this->connectionlink,$sqlloc);
			$rowloc = mysqli_fetch_assoc($queryloc);
			$query = "update pallets set WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',status='1',statusChangedDate=NOW(),statusChangedBy='".$_SESSION['user']['UserId']."',PalletFacilityID = '".$_SESSION['user']['FacilityID']."'  where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$rowloc['itemtype'] = 'Pallet';
			$rowloc['itemid'] = $data['idPallet'];		
			
			
			//Start insert into Pallet location tracking
			$query3 = "insert into pallet_location_tracking (LoadId,PalletId,ReceiveType,DateReceived,FromFacility,ToFacility,UserId) values ('".$rowloc['LoadId']."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Inter-Facility',NOW(),'".$rowloc['PalletFacilityID']."','".$_SESSION['user']['FacilityID']."','".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			//End insert into pallet location tracking
				
		}				
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$sqlupdateloc = "update location SET currentItemType = '',currentItemID = '' where LocationID='".$rowloc['LocationID']."'";
		$queryupdateloc = mysqli_query($this->connectionlink,$sqlupdateloc);
		
		if($data['GaylordUniqueID'] != '') {
			$sqlupdateloc1 = "update location SET currentItemType = 'Gaylord',currentItemID = '".mysqli_real_escape_string($this->connectionlink,$data['GaylordID'])."',Locked = '1' where LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
			$queryupdateloc1 = mysqli_query($this->connectionlink,$sqlupdateloc1);
			$this->RecordLocationHistory('Gaylord',$data['GaylordUniqueID'],0,$data['NewLocationID'],'Received Inter facility transfer');

		} else if($data['AssetScanID'] != '') {
			$sqlupdateloc1 = "update location SET currentItemType = 'Asset',currentItemID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."',Locked = '1' where LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
			$queryupdateloc1 = mysqli_query($this->connectionlink,$sqlupdateloc1);
		} else if($data['CustomPalletID'] != '') {
			$sqlupdateloc1 = "update location SET currentItemType = 'CustomPallet',currentItemID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',Locked = '1' where LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
			$queryupdateloc1 = mysqli_query($this->connectionlink,$sqlupdateloc1);
		} else if($data['idPallet'] != '') {
			$sqlupdateloc1 = "update location SET currentItemType = 'Pallet',currentItemID = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."',Locked = '1' where LocationID='".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
			$queryupdateloc1 = mysqli_query($this->connectionlink,$sqlupdateloc1);
		}
		
		
		if($data['AssetScanID'] != '') {
			//Insert into Asset Tracking
			$query2 = "insert into asset_tracking (AssetScanID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','Asset Received at New Location','','".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = $query2;
				return json_encode($json);
			}
			//End Inserting into Asset Tracking
		}
		if($data['idPallet'] != '') {
			//Insert into Pallet Tracking			
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Pallet Location Changed','','".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = $query2;
				return json_encode($json);
			}			
			//End Inserting into Pallet Tracking			
		}
		$json['Success'] = true;
		$json['Result'] = 'Locatin Updated';
		return json_encode($json);

	}

	public function ReceiveShipment($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "update shipping set ShipmentStatusID='4',StatusChangedDate=NOW(),StatusChangedBy='".$_SESSION['user']['UserId']."' where ShippingID='".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'Shipment Received';
		return json_encode($json);
	}
	public function GetReceiveContainerList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['LoadId']
			);
			/*$query = "select p.* from pallets p where status=1";*/

			$query = "select DISTINCT p.*,l.LocationName,l.LocationName as location,g.GroupName,g.GroupName as `group`,f.FacilityName,CU.CustomerName,U.FirstName,U.LastName,PS.StatusValue,DATEDIFF(NOW(),p.ReceivedDate) as DaysBeforeReceived,lf.FacilityName as SourceFacilityName,lo.SourceFacilityID from pallets p 
			LEFT JOIN location l ON l.LocationID = p.WarehouseLocationId 
			LEFT JOIN location_group g on l.GroupID = g.GroupID 
			LEFT JOIN facility f ON p.PalletFacilityID = f.FacilityID  			
			LEFT JOIN customer CU on p.idCustomer = CU.CustomerID  
			LEFT JOIN users U on U.UserId = p.CreatedBy 
			LEFT JOIN pallet_status PS on p.status = PS.status 
			LEFT JOIN loads lo on p.LoadId = lo.LoadId 
			LEFT JOIN facility lf on lo.SourceFacilityID = lf.FacilityID 
			where (p.status=1 or p.status = 7) ";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'LoadId') {
							$query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'idPallet') {
							$query = $query . " AND p.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ReceivedDate') {
							$query = $query . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo1') {
							$query = $query . " AND p.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo2') {
							$query = $query . " AND p.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo3') {
							$query = $query . " AND p.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo4') {
							$query = $query . " AND p.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'LocationName') {
							$query = $query . " AND l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CustomerName') {
							$query = $query . " AND CU.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}	
						if($key == 'UserName') {
							$query = $query . " AND (U.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR U.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
						}
						if($key == 'MaterialType') {
							$query = $query . " AND p.MaterialType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'StatusValue') {
							$query = $query . " AND PS.StatusValue like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}	
						if($key == 'OnDemandMedia') {
							$query = $query . " AND p.OnDemandMedia like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}	
						if($key == 'GroupName') {
							$query = $query . " AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}	

						if($key == 'BatchRecovery') {
							$query = $query . " AND p.BatchRecovery like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'SourceFacilityName') {
							$query = $query . " AND lf.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						
					}
				}
			}

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				  if($data['OrderBy'] == 'LoadId') {
					$query = $query . " order by p.LoadId ".$order_by_type." ";
				} else if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by p.idPallet ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ReceivedDate') {
					$query = $query . " order by p.ReceivedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo1') {
					$query = $query . " order by p.SealNo1 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo2') {
					$query = $query . " order by p.SealNo2 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo3') {
					$query = $query . " order by p.SealNo3 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo4') {
					$query = $query . " order by p.SealNo4 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'LocationName') {
					$query = $query . " order by l.LocationName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CustomerName') {
					$query = $query . " order by CU.CustomerName ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'UserName') {
					$query = $query . " order by U.FirstName ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'MaterialType') {
					$query = $query . " order by p.MaterialType ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'StatusValue') {
					$query = $query . " order by PS.StatusValue ".$order_by_type." ";
				} else if($data['OrderBy'] == 'OnDemandMedia') {
					$query = $query . " order by p.OnDemandMedia ".$order_by_type." ";
				} else if($data['OrderBy'] == 'GroupName') {
					$query = $query . " order by g.GroupName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'BatchRecovery') {
					$query = $query . " order by p.BatchRecovery ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SourceFacilityName') {
					$query = $query . " order by lf.FacilityName ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by ReceivedDate asc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));
			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {

					if($row['BatchRecovery'] == '1') {
						$row['BatchRecovery'] = 'Yes';
					} else {
						$row['BatchRecovery'] = 'No';
					}

					$parttype = '';				
					// $sqlpalitems = "Select count(*) as palquantity, CC.part_type from asn_assets PI, catlog_creation CC
					// WHERE 
					// CC.mpn_id = PI.UniversalModelNumber
					// AND PI.idPallet = '".$row['idPallet']."' and isnull(PI.AssetScanID) 
					// GROUP BY CC.part_type";

					$sqlpalitems = "Select count(*) as palquantity, PI.part_type from asn_assets PI 
					WHERE PI.idPallet = '".$row['idPallet']."' and isnull(PI.AssetScanID) 
					GROUP BY PI.part_type";

					$querypalitems = mysqli_query($this->connectionlink,$sqlpalitems);
					while($rowpalitems = mysqli_fetch_assoc($querypalitems)) {
						$parttype = $parttype." ".$rowpalitems['palquantity']."-".$rowpalitems['part_type']."\n";
					}
					$row['parttype'] = $parttype;

					//Start getting days since received
					//End getting days since received

					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Receive Containers Available";
			}
		
			if($data['skip'] == 0) {				
				$query1 = "select COUNT(DISTINCT p.idPallet) as `count(*)` from pallets p 
					LEFT JOIN location l ON l.LocationID = p.WarehouseLocationId 
					LEFT JOIN location_group g on l.GroupID = g.GroupID 
					LEFT JOIN facility f ON p.PalletFacilityID = f.FacilityID  					
					LEFT JOIN customer CU on p.idCustomer = CU.CustomerID  
					LEFT JOIN users U on U.UserId = p.CreatedBy 
					LEFT JOIN pallet_status PS on p.status = PS.status 
					LEFT JOIN loads lo on p.LoadId = lo.LoadId 
					LEFT JOIN facility lf on lo.SourceFacilityID = lf.FacilityID 
					where (p.status=1 or p.status = 7) ";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'LoadId') {
								$query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'idPallet') {
								$query1 = $query1 . " AND p.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ReceivedDate') {
								$query1 = $query1 . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo1') {
								$query1 = $query1 . " AND p.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo2') {
								$query1 = $query1 . " AND p.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo3') {
								$query1 = $query1 . " AND p.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo4') {
								$query1 = $query1 . " AND p.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'LocationName') {
								$query1 = $query1 . " AND l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'FacilityName') {
								$query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'CustomerName') {
								$query1 = $query1 . " AND CU.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}	
							if($key == 'UserName') {
								$query1 = $query1 . " AND (U.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR U.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
							}
							if($key == 'MaterialType') {
								$query1 = $query1 . " AND p.MaterialType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'StatusValue') {
								$query1 = $query1 . " AND PS.StatusValue like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'OnDemandMedia') {
								$query1 = $query1 . " AND p.OnDemandMedia like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'GroupName') {
								$query1 = $query1 . " AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'BatchRecovery') {
								$query1 = $query1 . " AND p.BatchRecovery like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'SourceFacilityName') {
								$query1 = $query1 . " AND lf.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetPendingPalletsList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['LoadId']
			);
			$query = "select * from pallets where status = 6 order by CreatedDate desc";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {

						if($key == 'LoadId') {
							$query = $query . " AND LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'idPallet') {
							$query = $query . " AND idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CreatedDate') {
							$query = $query . " AND CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo1') {
							$query = $query . " AND SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo2') {
							$query = $query . " AND SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'SealNo3') {
							$query = $query . " AND SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo4') {
							$query = $query . " AND SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
									
					}
				}
			}

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				  if($data['OrderBy'] == 'LoadId') {
					$query = $query . " order by LoadId ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by idPallet ".$order_by_type." ";
				}  
				else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by CreatedDate ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'SealNo1') {
					$query = $query . " order by SealNo1 ".$order_by_type." ";
				}
				elseif($data['OrderBy'] == 'SealNo2') {
					$query = $query . " order by SealNo2 ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'SealNo3') {
					$query = $query . " order by SealNo3 ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'SealNo4') {
					$query = $query . " order by SealNo4 ".$order_by_type." ";
				}
				
			} else {
				$query = $query . " order by LoadId desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));
			
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0)
		{
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Pending Containers Available";
		}
		
	if($data['skip'] == 0) {

				$query1 = "select count(*) from pallets where status=6 order by CreatedDate desc";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'LoadId') {
								$query1 = $query1 . " AND LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'idPallet') {
								$query1 = $query1 . " AND idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'CreatedDate') {
								$query1 = $query1 . " AND CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo1') {
								$query1 = $query1 . " AND SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo2') {
								$query1 = $query1 . " AND SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo3') {
								$query1 = $query1 . " AND SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo4') {
								$query1 = $query1 . " AND SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		}
		 catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}



	
	public function GetPendingLots() {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);	
		// if($_SESSION['user']['CustomerBased'] == 0) {
		// 	if($_SESSION['user']['ReferenceCustomerBased'] == 1) {
		// 		$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and l.LotStatus=1 and l.LoadDescription!='' and l.idRefCustomer in (select idRefCustomer from user_assigned_reference_customers where UserId='".$_SESSION['user']['UserId']."') and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
		// 	} else {
		// 		$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and l.LotStatus=1 and l.LoadDescription!='' and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
		// 	}
		// } else {
		// 	$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."') and l.LotStatus=1 and l.LoadDescription!='' and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
		// }

		if($_SESSION['user']['CustomerBased'] == 0) {
			if($_SESSION['user']['ReferenceCustomerBased'] == 1) {
				$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and l.LotStatus=1 and l.idRefCustomer in (select idRefCustomer from user_assigned_reference_customers where UserId='".$_SESSION['user']['UserId']."') and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			} else {
				$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and l.LotStatus=1 and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			}
		} else {
			$query = "select l.*,c.CustomerName,rc.CustomerName as RefCustomerName from lot_details l,customer c,referencecustomer rc where l.idCustomer = c.CustomerID and l.idRefCustomer = rc.idRefCustomer and c.AccountID='".$_SESSION['user']['AccountID']."' and c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."') and l.LotStatus=1 and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				/*if($row['idRefCustomer'] == '0') {
					$row['ReferenceCustomerName'] = 'N / A';
				} else {
					$query1 = "select CustomerName as ReferenceCustomerName from referencecustomer where idRefCustomer = '".$row['idRefCustomer']."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['ReferenceCustomerName'] = $row1['ReferenceCustomerName'];
					}
				}*/
				if($row['FacilityID'] == '0') {
					$row['FacilityName'] = 'N / A';
				} else {
					$query1 = "select FacilityName from facility where FacilityID = '".$row['FacilityID']."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					/*if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}*/
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['FacilityName'] = $row1['FacilityName'];
					}
				}
				//Start getting pallets from Load
				$query2 = "select * from lot_pallets where LoadId = '".$row['LoadId']."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$row['ReferenceCustomerName'] = $row1['ReferenceCustomerName'];
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$j =0;
					$pallets = array();
					while($row2 = mysqli_fetch_assoc($q2)){
						//Start getting pallet details
						$query3 = "select pi.*,pcl.ProductClassName,pcat.CategoryName from lot_pallet_items pi,product_class pcl,product_category pcat where pi.palletId = '".$row2['idPallet']."' and pi.idProductClass = pcl.ProductClassID and pi.idProductCategory = pcat.ProductCatID";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$splitdetails = array();
							$k = 0;
							while($row3 = mysqli_fetch_assoc($q3)){
								$splitdetails[$k] = $row3;
								$k++;
							}
							$row2['SplitDetails'] = $splitdetails;
						} else {
							$row2['SplitDetails'] = array();
						}
						//End getting pallet details
						$pallets[$j] = $row2;
						$j++;
					}
					$row['Pallets'] = $pallets;
				} else {
					$row['Pallets'] = array();
				}
				//End getting pallets from Load
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Pending Loads Available';
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function GetLoatPallets($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query1 = "select p.* from pallets p where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' ";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i=0;
			while($row1 = mysqli_fetch_assoc($q1)) {
				$row1['PackageWeight'] = floatval($row1['PackageWeight']);
				$row1['totalWeight'] = floatval($row1['totalWeight']);
				$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from pallet_items pi,product_category cat,product_class cla where pi.idProductClass = cla.ProductClassID and pi.idProductCategory = cat.ProductCatID and pi.palletId = '".$row1['idPallet']."' order by pi.id";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$j=0;
					$splits = array();
					while($row2 = mysqli_fetch_assoc($q2)) {												
						$row2['weight'] = floatval($row2['weight']);
						$row2['quantity'] = floatval($row2['quantity']);
						$row2['weightPercent'] = floatval($row2['weightPercent']);
						$splits[$j] = $row2;
						$j++;
					}
					$row1['Splits'] = $splits;
				}
				$pallets[$i] = $row1;
				$i++;
			}
			$json['pallets'] = $pallets;
		} else {
			$json['Success'] = false;
			$json['pallets'] = 'NO Pallets Available';
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'Pallets Available';
		return json_encode($json);
	}
	public function AddLocations($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from location where LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."' OR (FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND Locked != '1')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				if($row['GroupID'] == NULL || ($row['LocationID'] == $data['LocationID'])) {
					$result[$i] = $row;
					$i++;
				}				
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Locations Available";
		}
		return json_encode($json);
	}
	//$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
	//$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
	public function CalculateWeight($percent,$netweight) {
		$weight = 0;
		$weight = ($netweight * $percent) / 100;
		return $weight;
	}
	public function CalculateQuantity($weight,$ProductCatID) {
		$query = "select sum(Weight) as weight from categoryparts where ProductCatID='".mysqli_real_escape_string($this->connectionlink,$ProductCatID)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			return 0;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$total = $row['weight'];
			if($total != 0) {
				$quantity = intval($weight / $total) ;
				return $quantity;
			} else {
				return 0;
			}
		} else {
			return 0;
		}
	}
	public function GetPalletDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);						
		if($_SESSION['user']['CustomerBased'] == 0) {
			$query = "select p.*,c.CustomerName from pallets p,loads l,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."' and p.SortPallet = 0 and p.LoadId = l.LoadId and l.idCustomer = c.CustomerID and c.AccountID = '".$_SESSION['user']['AccountID']."' and l.FacilityID='".$_SESSION['user']['FacilityID']."'";
		} else {
			$query = "select p.*,c.CustomerName from pallets p,loads l,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."' and p.SortPallet = 0 and p.LoadId = l.LoadId and l.idCustomer = c.CustomerID and c.AccountID = '".$_SESSION['user']['AccountID']."' and l.FacilityID='".$_SESSION['user']['FacilityID']."' and  c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."')";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			//Start Getting Pallet Split Details
			$query2 = "select pi.*,pcla.ProductClassName,pcat.CategoryName from pallet_items pi,product_class pcla,product_category pcat where pi.palletId='".$row['idPallet']."' and pi.idProductClass = pcla.ProductClassID and pi.idProductCategory = pcat.ProductCatID";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$j = 0;
				$splits = array();
				while($row2 = mysqli_fetch_assoc($q2)){
					$splits[$j] = $row2;
					$j++;
				}
				$row['Splits'] = $splits;				
			}
			//End Getting Pallet Split Details			
			//Start Getting Sortpallet Details
			$sortpallets = array();
			$query3 = "select p.*,l.LocationName,pkg.packageName from pallets p,location l,package pkg where p.SortPallet = '".$row['idPallet']."' and p.WarehouseLocationId = l.LocationID and p.idPackage = pkg.idPackage";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$k = 0;
				while($row3 = mysqli_fetch_assoc($q3)) {
					//Start Getting Pallet Split Details
					$query4 = "select pi.*,pcla.ProductClassName,pcat.CategoryName from pallet_items pi,product_class pcla,product_category pcat where pi.palletId='".$row3['idPallet']."' and pi.idProductClass = pcla.ProductClassID and pi.idProductCategory = pcat.ProductCatID";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$l = 0;
						$Sortsplits = array();
						while($row4 = mysqli_fetch_assoc($q4)){
							$Sortsplits[$l] = $row4;
							$l++;
						}
						$row3['Splits'] = $Sortsplits;				
					}
					//End Getting Pallet Split Details	
					$sortpallets[$k] = $row3;
					$k++;
				}
				$row['SortPallets'] = $sortpallets;
			} else {
				$row['SortPallets'] = $sortpallets;
			}
			//End Getting Sortpallet Details
			$json['Success'] = true;
			$json['Result'] = $row;
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Pallet ID';
			return json_encode($json);
		}		
	}
	public function CreateSortPallet($data) {	
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		$Pallet_netweight = floatval($data['totalWeight']) - floatval($data['PackageWeight']);
		$Pallet_TotWeight = floatval($data['totalWeight']);
		if($Pallet_netweight < 0) {
			$Pallet_netweight = 0;
		}
		if($data['idPallet'] == '') { //If New Pallet
			$query = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,SortPallet,AuditBased,closestatus,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$data['description'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".$Pallet_netweight."','".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditBased'])."',0,NOW(),'".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$palletID)."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
				} else {
					$quantity = $data['quantity'];
				}				
				$query1 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					$query2 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
				//Update Pallet Count in loads 
				$query3 = "update loads set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count
				//Insert into Pallet Tracking
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."','Sort Pallet Created (SortPalletID : ".mysqli_real_escape_string($this->connectionlink,$palletID).")','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				//End Inserting into Pallet Tracking
				//Insert into Pallet Tracking
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','New Sort Pallet Created','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				//End Inserting into Pallet Tracking
				$json['Success'] = true;
				$json['Result'] = 'Pallet Created';
				$json['palletId'] = $palletID;
			}
		} else { //If Update Pallet
			$sqlselloc = "Select WarehouseLocationId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);
			$query = "update pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,$data['description'])."',WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',idPackage='".mysqli_real_escape_string($this->connectionlink,$data['package'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."' where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			else {
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$rowselloc['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}

				if($rowselloc['WarehouseLocationId'] != $data['location']) {
					
				}
			}
			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
			}
			if(count($data['Splits']) > 0) {
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					if($data['Splits'][$i]['id'] != '') {																								
						$query2 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
					} else {						
						$query2 = "insert into pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					}
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			//Insert into Pallet Tracking
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Sort Pallet Updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = $query2;
				return json_encode($json);
			}
			//End Inserting into Pallet Tracking
			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $palletID;
		}
		return json_encode($json);
	}
	public function GetProcesses ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if($data['type'] == 'Load') {
			$query = "select * from processes where AccountID = '".$_SESSION['user']['AccountID']."' and Status = '1' and `Load` = '1' order by ProcessName";
		} else if($data['type'] == 'Gaylord') {
			$query = "select * from processes where AccountID = '".$_SESSION['user']['AccountID']."' and Status = '1' and `Gaylord` = '1' order by ProcessName";
		} else if($data['type'] == 'CustomPallet') {
			$query = "select * from processes where AccountID = '".$_SESSION['user']['AccountID']."' and Status = '1' and `CustomPallet` = '1' order by ProcessName";
		}
		$q = mysqli_query($this->connectionlink,$query);			
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;			
			$json['Result'] = mysqli_error($this->connectionlink);			
			return json_encode($json);			
		}
		$processes = array();
		$i = 0;
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			while($row = mysqli_fetch_assoc($q)){
				$processes[$i] = $row;
				$i = $i + 1;
			}				
			$json['Success'] = true;			
			$json['Result'] = $processes;
		} else {
			$json['Success'] = false;			
			$json['Result'] = "No Process Available";
		}
		return json_encode($json);
	}
	
	
	public function GetLoadDetails1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//$query = "select l.*,c.CustomerName,c.CustomerID,f.FacilityName from loads l,customer c, facility f where l.LoadId='".$data['LoadID']."' and l.idCustomer = c.CustomerID and l.FacilityID = f.FacilityID";
		$query = "select l.*,c.CustomerName,c.CustomerID,f.FacilityName from loads l 
		left join customer c on l.idCustomer = c.CustomerID 
		left join facility f on l.FacilityID = f.FacilityID 
		where l.LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$row['LogisticsCost'] = floatval($row['LogisticsCost']);
			$row['PartsHarvestHrs'] = floatval($row['PartsHarvestHrs']);
			$row['OnsiteDestructionHr'] = floatval($row['OnsiteDestructionHr']);
			$row['FacilityDestructionHr'] = floatval($row['FacilityDestructionHr']);
			$row['DataCaptureHr'] = floatval($row['DataCaptureHr']);
			$json['Success'] = true;
			$json['Result'] = $row;			
			
			$query1 = "select p.*,l.LocationName,pkg.packageName,pro.ProcessName from pallets p 
			left join location l on p.WarehouseLocationId = l.LocationID 
			left join processes pro on p.ProcessID = pro.ProcessID 
			left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' ";
			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row1['PackageWeight'] = floatval($row1['PackageWeight']);
					$row1['totalWeight'] = floatval($row1['totalWeight']);			
					$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from pallet_items pi,product_category cat,product_class cla where pi.idProductClass = cla.ProductClassID and pi.idProductCategory = cat.ProductCatID and pi.palletId = '".$row1['idPallet']."' order by pi.id";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$j=0;
						$splits = array();
						while($row2 = mysqli_fetch_assoc($q2)) {
							$row2['weight'] = floatval($row2['weight']);
							$row2['quantity'] = floatval($row2['quantity']);
							$row2['weightPercent'] = floatval($row2['weightPercent']);
							$splits[$j] = $row2;
							$j++;
						}
						$row1['Splits'] = $splits;
					}
					$pallets[$i] = $row1;
					$i++;
				}
				$json['pallets'] = $pallets;
			} else {
				$json['pallets'] = 'NO Pallets Available';
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Load ';
		}
		return json_encode($json);
	}
	
	public function CreateSortPallet1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);	 	
		if(! $data['TPVREnabled']) {
			$data['TPVREnabled'] = 0;
		}
		//return json_encode($json);
		$data['PackageWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) : 0 ;
		
		if($data['idPallet'] == '') { //If New Pallet

			//$PalletID = $this->GetRandomPallet();
			$query = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,SortPallet,AuditBased,closestatus,ProcessID,PalletFacilityID,CreatedDate,CreatedBy,TPVREnabled,CurrentSealNumber,CurrentSealNumberOperatorID,CurrentSealNumberAuditorID,SealNumberModifiedTime,SealNumberModifiedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$data['description'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','0','1','".mysqli_real_escape_string($this->connectionlink,$data['AuditBased'])."',0,'".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['TPVREnabled'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				//$palletID = $PalletID;
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$palletID)."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				
				//Update Pallet Count in loads 
				$query3 = "update loads set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count
						
				//Insert into Pallet Tracking
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','New Sort Pallet Created','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				//End Inserting into Pallet Tracking
				$json['Success'] = true;
				$json['Result'] = 'Pallet Created';
				$json['palletId'] = $palletID;


				//Start insert record in TPVR History
				if($data['TPVREnabled'] == '1') {
					$query = "insert into tpvr_history (InventoryType,InventoryID,NewSealNumber,OperatorID,AuditorID,CreatedDate,CreatedBy,Action) values ('SortPallet','".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','New Sort Pallet Created')";
					$q = mysqli_query($this->connectionlink,$query);			
				}
				//End insert record in TPVR History
			}


		} else { //If Update Pallet
			$sqlselloc = "Select WarehouseLocationId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);
			$query = "update pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,$data['description'])."',WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',idPackage='".mysqli_real_escape_string($this->connectionlink,$data['package'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."',ProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."' where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			else {
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$rowselloc['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
			}
			if(count($data['Splits']) > 0) {
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
						//$quantity = 0;
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					if($data['Splits'][$i]['id'] != '') {																								
						$query2 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
					} else {						
						$query2 = "insert into pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					}
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			//Insert into Pallet Tracking
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Sort Pallet Updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Pallet Tracking
			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $palletID;
		}
		return json_encode($json);
	}
	
	public function CreateSortPallet1_bkp($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);	 	
		//return json_encode($json);
		$data['PackageWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) : 0 ;
		$data['totalWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) : 0 ;
		$Pallet_netweight = floatval($data['totalWeight']) - floatval($data['PackageWeight']);  
		$Pallet_TotWeight = floatval($data['totalWeight']);
		if($Pallet_netweight < 0) {
			$Pallet_netweight = 0;
		}
		if($data['idPallet'] == '') { //If New Pallet
			$query = "insert into pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,SortPallet,AuditBased,closestatus,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$data['description'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."','1','".mysqli_real_escape_string($this->connectionlink,$data['AuditBased'])."',0,NOW(),'".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$palletID)."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}				
				$query1 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
						//$quantity = 0;
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					$query2 = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
				//Update Pallet Count in loads 
				$query3 = "update loads set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count
				
				//Insert into Pallet Tracking
				/*$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".$data['PalletID']."','Sort Pallet Created (SortPalletID : ".$palletID.")','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}*/
				//End Inserting into Pallet Tracking
				
				
				//Insert into Pallet Tracking
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','New Sort Pallet Created','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				//End Inserting into Pallet Tracking
				$json['Success'] = true;
				$json['Result'] = 'Pallet Created';
				$json['palletId'] = $palletID;
			}
		} else { //If Update Pallet
			$sqlselloc = "Select WarehouseLocationId from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);
			$query = "update pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,$data['description'])."',WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',idPackage='".mysqli_real_escape_string($this->connectionlink,$data['package'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."' where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			else {
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$rowselloc['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
			}
			if(count($data['Splits']) > 0) {
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
						//$quantity = 0;
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					if($data['Splits'][$i]['id'] != '') {																								
						$query2 = "update pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
					} else {						
						$query2 = "insert into pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					}
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
					}
				}
			}
			//Insert into Pallet Tracking
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Sort Pallet Updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Pallet Tracking
			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $palletID;
		}
		return json_encode($json);
	}
	
	public function AddtoSortPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		$data['quantity'] = (mysqli_real_escape_string($this->connectionlink,$data['quantity']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['quantity']) : 0 ;
		if($data['quantity'] == '' || $data['quantity'] == '0') {
			$data['quantity'] = $this->CalculateQuantity($data['totalWeight'],$data['category']);
		}
		//Start entering into sort pallet items
		$query = "insert into pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,palletType,parentPallet,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['SortPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['quantity'])."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End entering into sort pallet items
		
		//Start updating Sort Pallet Weight
		$query = "update pallets set totalWeight = totalWeight + '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',pallet_netweight = pallet_netweight + '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SortPalletID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End updating Sort Pallet Weight
		
		//Start updating main pallet as used for sort pallet		
		$query1 = "update pallets set SortPalletCombination = '1' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End updating main pallet as used for sort pallet

		if($data['CurrentSealNumber'] != '') {
			//Start update Custom Pallet with New Seal Number
			$query = "update pallets set CurrentSealNumber = '".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumber'])."',SealNumberModifiedTime = NOW(),SealNumberModifiedBy = '".$_SESSION['user']['UserId']."',CurrentSealNumberOperatorID = '".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberOperatorID'])."',CurrentSealNumberAuditorID = '".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberAuditorID'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SortPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet with New Seal Number

			//Start insert record in TPVR History
			$query = "insert into tpvr_history (InventoryType,InventoryID,OldSealNumber,NewSealNumber,OperatorID,AuditorID,CreatedDate,CreatedBy,Action) values ('SortPallet','".mysqli_real_escape_string($this->connectionlink,$data['SortPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RemovedSealNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberOperatorID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CurrentSealNumberAuditorID'])."',NOW(),'".$_SESSION['user']['UserId']."','Adding into Sort Pallet from Pallet ".$data['idPallet']."')";
			$q = mysqli_query($this->connectionlink,$query);			
			//End insert record in TPVR History
		}		
		
		$json['Success'] = true;
		$json['Result'] = 'Success';
		return json_encode($json);
	}
	
	public function ClosePallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "update pallets set `status` = '3' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		
		//Start getting pallet location
		$query1 = "select WarehouseLocationId from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row1 = mysqli_fetch_assoc($q1);
			//Start Unlock Pallet Location			
			$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row1['WarehouseLocationId']."'";
			$querylocold = mysqli_query($this->connectionlink,$sqllocold);			
			//End Unlock Pallet Location
		}		
		
		
		//Insert into Pallet Tracking
		$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Pallet Closed in sort pallet screen','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
		$q2 = mysqli_query($this->connectionlink,$query2);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End Inserting into Pallet Tracking
		
		
		$json['Success'] = true;
		$json['Result'] = 'Success';
		return json_encode($json);
		
	}
	public function CreateLot ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);
		//return json_encode($json);
		$data['idServiceType'] = (mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idServiceType']) : 0 ;
		$data['FacilityID'] = (mysqli_real_escape_string($this->connectionlink,$data['FacilityID']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['FacilityID']) : 0 ;
		$data['ProcessID'] = (mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['ProcessID']) : 0 ;
		$data['TransportationCost'] = (mysqli_real_escape_string($this->connectionlink,$data['TransportationCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['TransportationCost']) : 0 ;
		$data['idTruckingCompany'] = (mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany']) : 0 ;
		
		$data['TransportationCommissionPercent'] = (mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent']) : 0 ;
		
		$data['TotalTransportationCost'] = (mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost']) : 0 ;
		$data['AdvancedPayment'] = (mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment']) : 0 ;
		
		$data['LogisticCharges'] = (mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges']) : 0 ;
		
		$data['EstimatedDeliveryDate'] = (mysqli_real_escape_string($this->connectionlink,$data['EstimatedDeliveryDate']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['EstimatedDeliveryDate']) : '1900-01-01' ;

		$data['date'] = (mysqli_real_escape_string($this->connectionlink,$data['date']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['date']) : '1900-01-01' ;
		
		if($data['LoadId'] == '') { // If New Load
			$LoadId = $this->GenerateLoadID($data['idCustomer']);
			$query = "insert into lot_details  (idCustomer,LoadId,idTruckingCompany,idRefCustomer,idServiceType,LoadDescription,SpecialInstructions,DateCreated,DateUpdated,CustomerTag,idUser,FacilityID,Damaged,DamagedReason,ProcessID,LotStatus,TransportationCost,EstimatedDeliveryDate,JabilInternalOrder,address,city,state,zipcode,`date`,`time`,contactName,contactPhone,TransportationCommissionPercent,TotalTransportationCost,LogisticCharges,AdvancedPayment,SealNo,SealNo2,TrailerID,SealNo3) values ('".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany'])."','".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['idServiceType'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."','".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."',NOW(),NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['CustomerTag'])."','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."','".mysqli_real_escape_string($this->connectionlink,$data['DamagedReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."',1,'".mysqli_real_escape_string($this->connectionlink,$data['TransportationCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['EstimatedDeliveryDate'])."','".mysqli_real_escape_string($this->connectionlink,$data['JabilInternalOrder'])."','".mysqli_real_escape_string($this->connectionlink,$data['address'])."','".mysqli_real_escape_string($this->connectionlink,$data['city'])."','".mysqli_real_escape_string($this->connectionlink,$data['state'])."','".mysqli_real_escape_string($this->connectionlink,$data['zipcode'])."','".mysqli_real_escape_string($this->connectionlink,$data['date'])."','".mysqli_real_escape_string($this->connectionlink,$data['time'])."','".mysqli_real_escape_string($this->connectionlink,$data['contactName'])."','".mysqli_real_escape_string($this->connectionlink,$data['contactPhone'])."','".mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent'])."','".mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges'])."','".mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."')";
		} else {
			$LoadId = $data['LoadId'];
			$query = "update lot_details set idRefCustomer='".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."',idServiceType='".mysqli_real_escape_string($this->connectionlink,$data['idServiceType'])."',idTruckingCompany='".mysqli_real_escape_string($this->connectionlink,$data['idTruckingCompany'])."',LoadDescription='".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."',SpecialInstructions='".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."',DateUpdated=NOW(),CustomerTag='".mysqli_real_escape_string($this->connectionlink,$data['CustomerTag'])."',idUser='".$_SESSION['user']['UserId']."',LotStatus='0',Damaged = '".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."',DamagedReason = '".mysqli_real_escape_string($this->connectionlink,$data['DamagedReason'])."',idCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."',ProcessID = '".mysqli_real_escape_string($this->connectionlink,$data['ProcessID'])."',LotStatus = '1',TransportationCost = '".mysqli_real_escape_string($this->connectionlink,$data['TransportationCost'])."',EstimatedDeliveryDate = '".mysqli_real_escape_string($this->connectionlink,$data['EstimatedDeliveryDate'])."',JabilInternalOrder = '".mysqli_real_escape_string($this->connectionlink,$data['JabilInternalOrder'])."',address = '".mysqli_real_escape_string($this->connectionlink,$data['address'])."',city = '".mysqli_real_escape_string($this->connectionlink,$data['city'])."',state = '".mysqli_real_escape_string($this->connectionlink,$data['state'])."',zipcode = '".mysqli_real_escape_string($this->connectionlink,$data['zipcode'])."',`date` = '".mysqli_real_escape_string($this->connectionlink,$data['date'])."',`time` = '".mysqli_real_escape_string($this->connectionlink,$data['time'])."',contactName = '".mysqli_real_escape_string($this->connectionlink,$data['contactName'])."',contactPhone = '".mysqli_real_escape_string($this->connectionlink,$data['contactPhone'])."',TransportationCommissionPercent = '".mysqli_real_escape_string($this->connectionlink,$data['TransportationCommissionPercent'])."',TotalTransportationCost = '".mysqli_real_escape_string($this->connectionlink,$data['TotalTransportationCost'])."',LogisticCharges = '".mysqli_real_escape_string($this->connectionlink,$data['LogisticCharges'])."',AdvancedPayment='".mysqli_real_escape_string($this->connectionlink,$data['AdvancedPayment'])."',SealNo='".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',TrailerID = '".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."' where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
		}
		//$json['Success'] = false;
//		$json['Result'] = $query;
//		return json_encode($json);
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {			
			$json['Success'] = true;
			if($data['LoadId'] == '') {
				$json['Result'] = 'Lot Created with LotID '.$LoadId;
			} else {
				$json['Result'] = 'Lot Updated';
			}
			if($data['LoadId'] == '') {
				$json['LoadId'] = $LoadId;
			}

			//Image Saving code
			if(count($data['images']) > 0) {
				for($i=0;$i<count($data['images']);$i++) {
					if($data['images'][$i]['imageID'] == '') {
						$query1 = "insert into load_images(filename,LoadId) values ('".mysqli_real_escape_string($this->connectionlink,$data['images'][$i]['filename'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."')";
						$q1 = mysqli_query($this->connectionlink,$query1);
					}
				}
			}
			//End Image Saving code			

			$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row4=mysqli_fetch_assoc($q4)) {
					$images[$i] = $row4;
					$i++;
				}
			}
			if(count($images) > 0)
				$json['images'] = $images;
			else
				$json['images'] = array();
			
			
			//Start updating pallet received date If load update
			/*if($data['LoadId'] != '') { // If updating load
				$query5 =  "update pallet_location_tracking set DateReceived = '".mysqli_real_escape_string($this->connectionlink,$data['DateReceived'])."' where LoadId = '".$data['LoadId']."' and ReceiveType = 'Lot'";
				$q5 = mysqli_query($this->connectionlink,$query5);
			}*/
			//End updating pallet received date If load update
			
			
			//Insert into Load Tracking
			if($data['LoadId'] == '') {
				$action = 'Lot Created';
			} else {
				$action = 'Lot Updated';
			}
			$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."')";			
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink)."3";
				return json_encode($json);	
			}
			//End Inserting into Load Tracking
			
			
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Data";
		}
		
		//Start managing Lot questions from pickup
		if($data['questions']) {
			$query = "delete from pickup_answers where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink)."3";
				return json_encode($json);	
			}
			
			for($i=0;$i<count($data['questions']);$i++) {
				
				$query3 = "insert into pickup_answers (LoadId,qestionID,answer,status) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['questions'][$i]['qestionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['questions'][$i]['answer'])."','1')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				
			}
		}
		//End managing Lot questions from pickup
		
		return json_encode($json);
	}
	public function GetLots($keyword) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//$query = "select LoadId from loads where LoadId like '%".$keyword."%'";
		if($_SESSION['user']['CustomerBased'] == 0) {
			if($_SESSION['user']['ReferenceCustomerBased'] == 1) {
				$query = "select l.LoadId from lot_details l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID AND l.LotStatus=1 and c.AccountID='".$_SESSION['user']['AccountID']."' and l.idRefCustomer in (select idRefCustomer from user_assigned_reference_customers where UserId='".$_SESSION['user']['UserId']."')";
			} else {
				$query = "select l.LoadId from lot_details l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' AND l.LotStatus=1";
			}
		} else {
			$query = "select l.LoadId from lot_details l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' AND l.LotStatus=1 and c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."')";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}
	
	public function CreatelotPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		$data['totalWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['totalWeight']) : 0 ;		
		$Pallet_netweight = floatval($data['totalWeight']) - floatval($data['PackageWeight']);
		$Pallet_TotWeight = $data['totalWeight'];
		if($Pallet_netweight < 0) {
			$Pallet_netweight = 0;
		}
		$data['location'] = (mysqli_real_escape_string($this->connectionlink,$data['location']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['location']) : 0 ;
		$data['package'] = (mysqli_real_escape_string($this->connectionlink,$data['package']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['package']) : 0 ;
		$data['PackageWeight'] = (mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['PackageWeight']) : 0 ;
		$data['SAPStandardCost'] = (mysqli_real_escape_string($this->connectionlink,$data['SAPStandardCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['SAPStandardCost']) : 0 ;
		$data['Charges'] = (mysqli_real_escape_string($this->connectionlink,$data['Charges']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['Charges']) : 0 ;
		
		/*$data['AcquisitionCost'] = (mysqli_real_escape_string($this->connectionlink,$data['AcquisitionCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['AcquisitionCost']) : 0 ;
		
		$data['AcquisitionCost'] = (mysqli_real_escape_string($this->connectionlink,$data['AcquisitionCost']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['AcquisitionCost']) : 0 ;*/
		
		$data['PriceLB'] = (mysqli_real_escape_string($this->connectionlink,$data['PriceLB']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['PriceLB']) : 0 ;
		if($data['PalletFacilityID'] > 0) {
			
		} else {
			$data['PalletFacilityID'] = 0 ;
		}
		if($data['idPallet'] == '') { //If New Pallet PackageWeight
			//$PalletID = $this->GetRandomLotPallet();

			$query = "insert into lot_pallets (LoadId,PalletDescription,WarehouseLocationId,idPackage,totalWeight,PackageWeight,pallet_netweight,PalletFacilityID,AcquisitionCost,SAPStandardCost,Charges,RackType,SealNo1,SealNo2,PickupDC,SealNo3,SealNo4,Comments) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."','".mysqli_real_escape_string($this->connectionlink,$data['description'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['package'])."','".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."','".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."','".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['TotalPrice'])."','".mysqli_real_escape_string($this->connectionlink,$data['SAPStandardCost'])."','".mysqli_real_escape_string($this->connectionlink,$data['Charges'])."','".mysqli_real_escape_string($this->connectionlink,$data['RackType'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['PickupDC'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."','".mysqli_real_escape_string($this->connectionlink,$data['Comments'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink).$query;
				return json_encode($json);	
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$palletID = mysqli_insert_id($this->connectionlink);
				//$palletID = $PalletID;
				/*$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".$palletID."' WHERE `LocationID` = '".$data['location']."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);*/
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}				
				$query1 = "insert into lot_pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight,PaymentType,PriceLB,TotalPrice,PriceUNIT,PricingLevel) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."','".mysqli_real_escape_string($this->connectionlink,$data['PaymentType'])."','".mysqli_real_escape_string($this->connectionlink,$data['PriceLB'])."','".mysqli_real_escape_string($this->connectionlink,$data['TotalPrice'])."','".mysqli_real_escape_string($this->connectionlink,$data['PriceUNIT'])."','".mysqli_real_escape_string($this->connectionlink,$data['PricingLevel'])."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
						//$quantity = 0;
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}
					$query2 = "insert into lot_pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$palletID)."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
				}

				//Update Pallet Count in loads 
				$query3 = "update lot_details set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End updating pallet count
				//Insert into Pallet Tracking
				/*$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".$palletID."','Pallet Created','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}*/
				//End Inserting into Pallet Tracking
				
				//Start getting load received date
				$query6 = "select DateReceived from lot_details where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					$DateReceived = $row6['DateReceived'];
				}
				//End getting load received date
				$data['FromFacility'] = (mysqli_real_escape_string($this->connectionlink,$data['FromFacility']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['FromFacility']) : 0 ;
				//Start inserting into pallet location tracking
				/*$query4 = "insert into pallet_location_tracking (LoadId,PalletId,ReceiveType,DateReceived,FromFacility,ToFacility,UserId) values ('".$data['loadid']."','".$palletID."','Direct','".$DateReceived."','".$data['FromFacility']."','".$data['PalletFacilityID']."','".$_SESSION['user']['UserId']."')";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}*/
				//End inserting into pallet location tracking
				
				$json['Success'] = true;
				$json['Result'] = 'Pallet Created';
				$json['palletId'] = $palletID;
				$json['desc'] = $data['description'];
			}
		} else { //If Update Pallet
			/*$sqlselloc = "Select WarehouseLocationId from lot_pallets where idPallet='".$data['idPallet']."'";
			$queryselloc = mysqli_query($this->connectionlink,$sqlselloc);
			$rowselloc = mysqli_fetch_assoc($queryselloc);*/
			$query = "update lot_pallets set PalletDescription='".mysqli_real_escape_string($this->connectionlink,$data['description'])."',totalWeight = '".mysqli_real_escape_string($this->connectionlink,$data['totalWeight'])."',PackageWeight = '".mysqli_real_escape_string($this->connectionlink,$data['PackageWeight'])."',AcquisitionCost = '".mysqli_real_escape_string($this->connectionlink,$data['TotalPrice'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$Pallet_netweight)."',SAPStandardCost = '".mysqli_real_escape_string($this->connectionlink,$data['SAPStandardCost'])."',Charges = '".mysqli_real_escape_string($this->connectionlink,$data['Charges'])."',RackType = '".mysqli_real_escape_string($this->connectionlink,$data['RackType'])."',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',PickupDC = '".mysqli_real_escape_string($this->connectionlink,$data['PickupDC'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',Comments = '".mysqli_real_escape_string($this->connectionlink,$data['Comments'])."' where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			} else {
				/*$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$rowselloc['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
				else {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".$data['idPallet']."' WHERE `LocationID` = '".$data['location']."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
				}*/
			}

			if($data['splitID'] != '') {
				$weight = $this->CalculateWeight($data['weightPercent'],$Pallet_netweight);
				$Totweight = $this->CalculateWeight($data['weightPercent'],$Pallet_TotWeight);
				if($data['quantity'] == '' || $data['quantity'] == '0') {
					$quantity = $this->CalculateQuantity($weight,$data['category']);
					//$quantity = 0;
				} else {
					$quantity = $data['quantity'];
				}
				$query1 = "update lot_pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."',PaymentType = '".mysqli_real_escape_string($this->connectionlink,$data['PaymentType'])."',PriceLB = '".mysqli_real_escape_string($this->connectionlink,$data['PriceLB'])."',TotalPrice = '".mysqli_real_escape_string($this->connectionlink,$data['TotalPrice'])."',PriceUNIT = '".mysqli_real_escape_string($this->connectionlink,$data['PriceUNIT'])."',PricingLevel = '".mysqli_real_escape_string($this->connectionlink,$data['PricingLevel'])."' where id='".mysqli_real_escape_string($this->connectionlink,$data['splitID'])."'";				
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}
			}

			if(count($data['Splits']) > 0) {				
				for($i=0;$i<count($data['Splits']);$i++) {
					$weight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_netweight);
					$Totweight = $this->CalculateWeight($data['Splits'][$i]['weightPercent'],$Pallet_TotWeight);
					if($data['Splits'][$i]['quantity'] == '' || $data['Splits'][$i]['quantity'] == '0') {
						$quantity = $this->CalculateQuantity($weight,$data['Splits'][$i]['category']);
						//$quantity = 0;
					} else {
						$quantity = $data['Splits'][$i]['quantity'];
					}

					if($data['Splits'][$i]['id'] != '') {																								
						$query2 = "update lot_pallet_items set weight='".mysqli_real_escape_string($this->connectionlink,$weight)."',weightPercent = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."',quantity='".mysqli_real_escape_string($this->connectionlink,$quantity)."',idProductClass='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."',idProductCategory='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."',totalWeight='".mysqli_real_escape_string($this->connectionlink,$Totweight)."',PriceUNIT = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['PriceUNIT'])."',PricingLevel = '".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['PricingLevel'])."' where id='".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['id'])."'";						
					} else {						
						$query2 = "insert into lot_pallet_items (palletId,weight,weightPercent,quantity,idProductClass,idProductCategory,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$weight)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['weightPercent'])."','".mysqli_real_escape_string($this->connectionlink,$quantity)."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['class'])."','".mysqli_real_escape_string($this->connectionlink,$data['Splits'][$i]['category'])."','".mysqli_real_escape_string($this->connectionlink,$Totweight)."')";
					}					
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
				}
			}			
			
			//Insert into Pallet Tracking
			/*$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".$data['idPallet']."','Pallet Updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}*/
			//End Inserting into Pallet Tracking
			$json['Success'] = true;
			$json['Result'] = 'Pallet Updated';
			$json['palletId'] = $data['idPallet'];
			$json['desc'] = $data['description'];
		}
		//$json['LoadValueEstimate'] = $this->AdjustLotValueEstimate($data['loadid']);
		$json['LoadValueEstimate'] = $this->AdjustLotValueEstimate($data['loadid']);
		return json_encode($json);
	}
	
	public function SearchLot($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from lot_details where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$sqlpalcou = "Select SUM(LPI.totalWeight) as pallettotweight from lot_pallets LP,lot_pallet_items LPI
				Where LPI.palletId = LP.idPallet
				AND LP.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
			$qpalcou = mysqli_query($this->connectionlink,$sqlpalcou);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			$rowpalcou = mysqli_fetch_assoc($qpalcou);
			$row['PalletTotWeight'] = $rowpalcou['pallettotweight'];
			$row['TransportationCost'] = floatval($row['TransportationCost']);
			$row['TransportationCommissionPercent'] = floatval($row['TransportationCommissionPercent']);
			$row['TotalTransportationCost'] = floatval($row['TotalTransportationCost']);
			$row['LogisticCharges'] = floatval($row['LogisticCharges']);
			$row['AdvancedPayment'] = floatval($row['AdvancedPayment']);
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i=0;
					while($row4=mysqli_fetch_assoc($q4)) {
						$images[$i] = $row4;
						$i++;
					}
				}
				if(count($images) > 0)
					$row['images'] = $images;
				else
					$row['images'] = array();
			}
			$json['Success'] = true;
			$json['Result'] = $row;
			//$query1 = "select p.*,l.LocationName,pkg.packageName from pallets p,location l,package pkg where p.WarehouseLocationId = l.LocationID and p.idPackage=pkg.idPackage and LoadId = '".$data['LoadID']."' ";
			
			$query1 = "select p.*,l.LocationName,pkg.packageName from lot_pallets p 
			left join location l on p.WarehouseLocationId = l.LocationID 
			left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' ";
			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row1['PackageWeight'] = floatval($row1['PackageWeight']);
					$row1['totalWeight'] = floatval($row1['totalWeight']);
					$row1['AcquisitionCost'] = floatval($row1['AcquisitionCost']);
					$row1['SAPStandardCost'] = floatval($row1['SAPStandardCost']);
					$row1['Charges'] = floatval($row1['Charges']);
					$row1['LoadValueEstimate'] = floatval($row1['LoadValueEstimate']);
					//$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from lot_pallet_items pi,product_category cat,product_class cla where pi.idProductClass = cla.ProductClassID and pi.idProductCategory = cat.ProductCatID and pi.palletId = '".$row1['idPallet']."' order by pi.id";
					$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from lot_pallet_items pi 
						left join product_category cat on pi.idProductCategory = cat.ProductCatID 
						left join product_class cla on pi.idProductClass = cla.ProductClassID 
						where pi.palletId = '".$row1['idPallet']."' order by pi.id";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$j=0;
						$splits = array();
						while($row2 = mysqli_fetch_assoc($q2)) {
							$row2['weight'] = floatval($row2['weight']);
							$row2['quantity'] = floatval($row2['quantity']);
							$row2['weightPercent'] = floatval($row2['weightPercent']);
							$row2['PriceLB'] = floatval($row2['PriceLB']);
							$row2['PriceUNIT'] = floatval($row2['PriceUNIT']);
							$row2['TotalPrice'] = floatval($row2['TotalPrice']);
							$splits[$j] = $row2;
							$j++;
						}
						$row1['Splits'] = $splits;
					} else {
						$row1['Splits'] = array();						
					}
					$pallets[$i] = $row1;
					$i++;
				}
				$json['pallets'] = $pallets;
			} else {
				$json['pallets'] = 'NO Pallets Available';
			}
			
			//Start getting Pickup Questions
			$query = "select qestionID,question,type from pickup_questions where accountID = '".$_SESSION['user']['AccountID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				$questions = array();
				while($row = mysqli_fetch_assoc($q)) {
					//Start getting Answer
					$query1 = "select answer from pickup_answers where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' and qestionID = '".$row['qestionID']."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['answer'] = $row1['answer'];
					} else {
						if($row['type'] == 'radio')
							$row['answer'] = 'No';
						else 
							$row['answer'] = '';
					}	
					
					$questions[$i] = $row;
					$i++;
				}
				$json['questions'] = $questions;
			} else {
				$json['questions'] = array();
			}
			//End getting Pickup Questions
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Load';
		}
		return json_encode($json);
	}
	
	public function SearchLotDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		
		$query = "select * from lot_details where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$row['TransportationCost'] = floatval($row['TransportationCost']);
			$row['TransportationCommissionPercent'] = floatval($row['TransportationCommissionPercent']);
			$row['TotalTransportationCost'] = floatval($row['TotalTransportationCost']);
			$row['LogisticCharges'] = floatval($row['LogisticCharges']);
			$row['AdvancedPayment'] = floatval($row['AdvancedPayment']);
			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$query4 = "select imageID,filename from load_images where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i=0;
					while($row4=mysqli_fetch_assoc($q4)) {
						$images[$i] = $row4;
						$i++;
					}
				}
				if(count($images) > 0)
					$row['images'] = $images;
				else
					$row['images'] = array();
			}
			$json['Success'] = true;
			$json['Result'] = $row;			
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Load';
		}
		return json_encode($json);
	}
	
	public function getChargeDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//Check Pricing in customer lever
		$query = "select cp.ProductCatID,cp.PriceLB,cp.PriceUNIT,pc.PaymentType,cp.UsePrice from category_price cp, product_category pc where cp.CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."' and cp.ProductCatID = '".mysqli_real_escape_string($this->connectionlink,$data['idProductCategory'])."' and cp.ProductCatID = pc.ProductCatID";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$customer_pricing = false;
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['UsePrice'] == '1') {
				$customer_pricing = true;
			} else {
				$customer_pricing = false;
			}
		} else {
			$customer_pricing = false;
		}
		if($customer_pricing) {			
			$row['PriceLB'] = floatval($row['PriceLB']);
			$row['PriceUNIT'] = floatval($row['PriceUNIT']);
			$row['PricingLevel'] = 'Customer';
			$json['Success'] = true;
			$json['Result'] = $row;	
			return json_encode($json);
		} else { // If customer level pricing is not available, check for category level pricing
			$query = "select ProductCatID,PaymentType,UnitPrice,Commodity_Price from product_category where ProductCatID = '".mysqli_real_escape_string($this->connectionlink,$data['idProductCategory'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$row['PriceLB'] = floatval($row['Commodity_Price']);
				$row['PriceUNIT'] = floatval($row['UnitPrice']);
				$row['PaymentType'] = $row['PaymentType'];
				$row['PricingLevel'] = 'Category';
				$json['Success'] = true;
				$json['Result'] = $row;	
				return json_encode($json);
			}else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid input details';
				return json_encode($json);
			}
		}		
	}
	
	public function getChargeDetails1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select cp.ProductCatID,cp.PriceLB,cp.PriceUNIT,pc.PaymentType from category_price cp, product_category pc where cp.CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."' and cp.ProductCatID = '".mysqli_real_escape_string($this->connectionlink,$data['idProductCategory'])."' and cp.ProductCatID = pc.ProductCatID";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$row['PriceLB'] = floatval($row['PriceLB']);
			$row['PriceUNIT'] = floatval($row['PriceUNIT']);
			$json['Success'] = true;
			$json['Result'] = $row;	
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Price is not set for Category and Customer';
			return json_encode($json);
		}
	}
	
	public function AddReceivedHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '1'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['ReceivedHours'] = ($data['ReceivedHours'] !== '') ? $data['ReceivedHours'] : 0 ;
		$totprice = $data['ReceivedHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '1', '".mysqli_real_escape_string($this->connectionlink,$data['ReceivedHours'])."', '".$row1['PriceTypePrice']."', '".$totprice."', '".mysqli_real_escape_string($this->connectionlink,$data['ReceivedDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$Received_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'Received Hours Added';
			$json['ReceivedId'] = $Received_id;
			return json_encode($json);
		}
	}
	public function AddInspectedHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '2'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['InspectedHoursHours'] = ($data['InspectedHoursHours'] !== '') ? $data['InspectedHoursHours'] : 0 ;
		$totprice = $data['InspectedHoursHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '2', '".mysqli_real_escape_string($this->connectionlink,$data['InspectedHoursHours'])."', '".$row1['PriceTypePrice']."', '".mysqli_real_escape_string($this->connectionlink,$totprice)."', '".mysqli_real_escape_string($this->connectionlink,$data['InspectedDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$Received_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'Inspected Hours Added';
			$json['InspectedId'] = $Received_id;
			return json_encode($json);
		}
	}
	public function AddDemandHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '3'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['DemanHoursHours'] = ($data['DemanHoursHours'] !== '') ? $data['DemanHoursHours'] : 0 ;
		$totprice = $data['DemanHoursHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '3', '".mysqli_real_escape_string($this->connectionlink,$data['DemanHoursHours'])."', '".$row1['PriceTypePrice']."', '".mysqli_real_escape_string($this->connectionlink,$totprice)."', '".mysqli_real_escape_string($this->connectionlink,$data['DemanDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$Received_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'Deman Hours Added';
			$json['DemanId'] = $Received_id;
			return json_encode($json);
		}
	}
	public function AddITADHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '4'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['ITADHoursHours'] = ($data['ITADHoursHours'] !== '') ? $data['ITADHoursHours'] : 0 ;
		$totprice = $data['ITADHoursHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '4', '".mysqli_real_escape_string($this->connectionlink,$data['ITADHoursHours'])."', '".mysqli_real_escape_string($this->connectionlink,$row1['PriceTypePrice'])."', '".mysqli_real_escape_string($this->connectionlink,$totprice)."', '".mysqli_real_escape_string($this->connectionlink,$data['ITADDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$Received_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'ITAD Hours Added';
			$json['ITADId'] = $Received_id;
			return json_encode($json);
		}
	}
	public function AddInventoryHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '5'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['InventoryHoursHours'] = ($data['InventoryHoursHours'] !== '') ? $data['InventoryHoursHours'] : 0 ;
		$totprice = $data['InventoryHoursHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '5', '".mysqli_real_escape_string($this->connectionlink,$data['InventoryHoursHours'])."', '".$row1['PriceTypePrice']."', '".mysqli_real_escape_string($this->connectionlink,$totprice)."', '".mysqli_real_escape_string($this->connectionlink,$data['InventoryDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$Received_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'Inventory Hours Added';
			$json['DemanId'] = $Received_id;
			return json_encode($json);
		}
	}
	public function AddCMETHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$sql1 = "Select PriceTypePrice from load_customer_price where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."' AND PriceTypeID = '6'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PriceTypePrice'] == '')
			{
				$row1['PriceTypePrice'] = 0;
			}
		}
		$data['CMETHoursHours'] = ($data['CMETHoursHours'] !== '') ? $data['CMETHoursHours'] : 0 ;
		$totprice = $data['CMETHoursHours']*$row1['PriceTypePrice'];
		$query = "INSERT INTO `load_price` (`LoadId`, `PriceTypeID`, `Hours`, `PricePerHour`, `TotalPrice`, `Date`, `CreatedDate`, `CreatedBy`, `UpdatedDate`, `UpdatedBy`) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."', '6', '".mysqli_real_escape_string($this->connectionlink,$data['CMETHoursHours'])."', '".$row1['PriceTypePrice']."', '".mysqli_real_escape_string($this->connectionlink,$totprice)."', '".mysqli_real_escape_string($this->connectionlink,$data['CMETDate'])."', NOW(), '".$_SESSION['user']['UserId']."', NOW(), '".$_SESSION['user']['UserId']."');";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$CMET_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = 'CMET Hours Added';
			$json['CMETId'] = $CMET_id;
			return json_encode($json);
		}
	}
	public function GetReceivedHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as ReceivedDate,Hours as Hours,PriceID as ReceivedId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =1";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[Hours] = floatval($row[Hours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function GetInspectingHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as InspectedDate,Hours as InspectedHoursHours,PriceID as InspectedId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =2";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[InspectedHoursHours] = floatval($row[InspectedHoursHours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function GetDemanHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as DemanDate,Hours as DemanHoursHours,PriceID as DemanId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =3";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[DemanHoursHours] = floatval($row[DemanHoursHours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function GetITADHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as ITADDate,Hours as ITADHoursHours,PriceID as ITADId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =4";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[ITADHoursHours] = floatval($row[ITADHoursHours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function GetInventoryHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as InventoryDate,Hours as InventoryHoursHours,PriceID as InventoryId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =5";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[InventoryHoursHours] = floatval($row[InventoryHoursHours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function GetCMETHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select Date as CMETDate,Hours as CMETHoursHours,PriceID as CMETId from load_price where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' AND PriceTypeID =6";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row = mysqli_fetch_assoc($query))
				{
					$row[CMETHoursHours] = floatval($row[CMETHoursHours]);
					$row1[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $row1;
			} /*else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}*/
		}
		return json_encode($json);
	}
	public function DeletePriceHours($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "delete from load_price where PriceID='".mysqli_real_escape_string($this->connectionlink,$data['ReceivedId'])."'";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = "Record Deleted Successfully";
		}
		return json_encode($json);
	}
	public function AddLogisticsCost($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$data['LogisticsCost'] = ($data['LogisticsCost'] !== '') ? $data['LogisticsCost'] : 0 ;		
		$sql = "UPDATE `loads` SET `LogisticsCost`='".mysqli_real_escape_string($this->connectionlink,$data['LogisticsCost'])."' WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = "Logistics Cost Updated Successfully";
		}
		return json_encode($json);
	}
	public function AddPartsHarvestHrs($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql1 = "Select PartsHarvest from customer where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['PartsHarvest'] == '')
			{
				$row1['PartsHarvest'] = 0;
			}
		}
		$data['PartsHarvestHrs'] = ($data['PartsHarvestHrs'] !== '') ? $data['PartsHarvestHrs'] : 0 ;
		$sql2 = "Select PartsHarvestHrsDate,CustomerPartsHarvestFee from loads where `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
		$query2 = mysqli_query($this->connectionlink,$sql2);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row2 = mysqli_fetch_assoc($query2);
			if($row2['PartsHarvestHrsDate'] != '')
			{
				$totPartsHarvestfee = $row2['CustomerPartsHarvestFee']*$data['PartsHarvestHrs'];
				$sql = "UPDATE `loads` SET `PartsHarvestHrs`='".mysqli_real_escape_string($this->connectionlink,$data['PartsHarvestHrs'])."',`TotPartsHarvestFee`='".mysqli_real_escape_string($this->connectionlink,$totPartsHarvestfee)."',`PartsHarvestHrsDate`=NOW() WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
			}
			else
			{
				$totPartsHarvestfee = $row1['PartsHarvest']*$data['PartsHarvestHrs'];
				$sql = "UPDATE `loads` SET `PartsHarvestHrs`='".mysqli_real_escape_string($this->connectionlink,$data['PartsHarvestHrs'])."',`CustomerPartsHarvestFee`='".mysqli_real_escape_string($this->connectionlink,$row1['PartsHarvest'])."',`TotPartsHarvestFee`='".mysqli_real_escape_string($this->connectionlink,$totPartsHarvestfee)."',`PartsHarvestHrsDate`=NOW() WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
			}
		}
		
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = "Parts Harvest Hours Updated Successfully";
		}
		return json_encode($json);
	}
	public function AddDataCaptureHrs($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql1 = "Select DataCapture from customer where CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomerID'])."'";
		$query1 = mysqli_query($this->connectionlink,$sql1);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row1 = mysqli_fetch_assoc($query1);
			if($row1['DataCapture'] == '')
			{
				$row1['DataCapture'] = 0;
			}
		}
		$data['DataCaptureHr'] = ($data['DataCaptureHr'] !== '') ? $data['DataCaptureHr'] : 0 ;
		
		$sql2 = "Select DataCaptureHrdate,CustomerDataCaptureFee from loads where `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
		$query2 = mysqli_query($this->connectionlink,$sql2);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		else {
			$row2 = mysqli_fetch_assoc($query2);
			if($row2['DataCaptureHrdate'] != '')
			{
				$totdatacapfee = $row2['CustomerDataCaptureFee']*$data['DataCaptureHr'];
				$sql = "UPDATE `loads` SET `DataCaptureHr`='".mysqli_real_escape_string($this->connectionlink,$data['DataCaptureHr'])."',`TotDataCaptureFee`='".mysqli_real_escape_string($this->connectionlink,$totdatacapfee)."',`DataCaptureHrdate`=NOW() WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
			}
			else
			{
				$totdatacapfee = $row1['DataCapture']*$data['DataCaptureHr'];
				$sql = "UPDATE `loads` SET `DataCaptureHr`='".mysqli_real_escape_string($this->connectionlink,$data['DataCaptureHr'])."',`CustomerDataCaptureFee`='".mysqli_real_escape_string($this->connectionlink,$row1['DataCapture'])."',`TotDataCaptureFee`='".mysqli_real_escape_string($this->connectionlink,$totdatacapfee)."',`DataCaptureHrdate`=NOW() WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
			}
		}
		
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = "Data Capture Hours Updated Successfully";
		}
		return json_encode($json);
	}
	public function AddDataDestructionHrs($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$data['OnsiteDestructionHr'] = ($data['OnsiteDestructionHr'] !== '') ? $data['OnsiteDestructionHr'] : 0 ;
		$data['FacilityDestructionHr'] = ($data['FacilityDestructionHr'] !== '') ? $data['FacilityDestructionHr'] : 0 ;
		$sql = "UPDATE `loads` SET `FacilityDestructionHr`='".mysqli_real_escape_string($this->connectionlink,$data['FacilityDestructionHr'])."',`OnsiteDestructionHr`='".mysqli_real_escape_string($this->connectionlink,$data['OnsiteDestructionHr'])."' WHERE `LoadId`='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."';";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		} else {
			$json['Success'] = true;
			$json['Result'] = "Data Destruction Hours Updated Successfully";
		}
		return json_encode($json);
	}
	public function SaveUnitPrice($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		//$query = "update product_category set UnitPrice = '".$data['UnitPrice']."' where ProductCatID = '".$data['ProductCatID']."'";
		//$query = "update product_category set ".$data['field']." = '".$data['fieldValue']."' where ProductCatID = '".$data['ProductCatID']."'";
		$query = "update product_category set `".mysqli_real_escape_string($this->connectionlink, $data['field'])."` = '".mysqli_real_escape_string($this->connectionlink,$data['fieldValue'])."' where JabilPN = '".mysqli_real_escape_string($this->connectionlink,$data['JabilPN'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'Success';
		return json_encode($json);
	}
	
	public function AdjustLotValueEstimate($lotID) {
		$query = "select sum(AcquisitionCost) as totalEstimate from lot_pallets where LoadId = '".mysqli_real_escape_string($this->connectionlink,$lotID)."'";		
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			return 0;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$query1 = "update lot_details set LoadValueEstimate = '".$row['totalEstimate']."' where LoadId = '".mysqli_real_escape_string($this->connectionlink,$lotID)."'";
			$q1 = mysqli_query($this->connectionlink,$query1);			
			return $row['totalEstimate'];
		} else {
			return 0;			
		}
	}
	
	public function GetReferenceCustomerFullDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//Start getting Reference Customer Address
		$query = "select rc.street_address,rc.city,rc.StateName,rc.zip_code,rc.contact_name,rc.contact_phone,s.State from referencecustomer rc 
					left join states s on rc.StateName = s.StateID where rc.idRefCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Address']  = $row;
		}
		
		//Start getting Pickup Questions
		$query = "select qestionID,question,type from pickup_questions where accountID = '".$_SESSION['user']['AccountID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i=0;
			$questions = array();
			while($row = mysqli_fetch_assoc($q)) {
				//Start getting Answer
				$query1 = "select answer from pickup_answers where idRefCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."' and qestionID = '".mysqli_real_escape_string($this->connectionlink,$row['qestionID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$row['answer'] = $row1['answer'];
				} else {
					if($row['type'] == 'radio')
						$row['answer'] = 'No';
					else 
						$row['answer'] = '';
				}	
				
				$questions[$i] = $row;
				$i++;
			}
			$json['questions'] = $questions;
		}
		//End getting Pickup Questions
		
		$json['Success'] = true;			
		return json_encode($json);
			
	}
	public function UploadCSV($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		if(($data['file']['type'] != 'application/vnd.ms-excel') && ($data['file']['type'] != 'application/msexcel') && ($data['file']['type'] != 'text/csv')) {
			$json['Success'] = false;
			$json['Result'] = 'Only CSV Files are Supported';	
			return json_encode($json);		
		}
		$filename = time().$data['file']['name'];
		$target_path = '../../lot_documents/'.$filename;
		if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			$json['Success'] = true;
			$json['Result'] = 'File Uploaded';
			$json['FileName'] = $filename;
			return json_encode($json);
				
		} else{
			$json['Success'] = false;
			$json['Result'] = 'Problem with File uploading';
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'File uploaded Successfully';
		return json_encode($json);
	}
	public function CSVSaveDatabase($data) {
		/*$data = new Spreadsheet_Excel_Reader();
		$data->setOutputEncoding('CP1251');
		$id = $pickupID;
		$file = '../../customer_upload/'.$filename;
		$data->read($file);
		error_reporting(E_ALL ^ E_NOTICE);
		$res = $data->sheets[0]['cells'];
		for ($i = 1,$count = count($res); $i <= $count ; $i++){
			if($i != 1){
			$str = substr(number_format(time() * rand(),0,'',''),0,9);
				//$str = "INSERT INTO `call` (`SubscriberHICN#`) VALUES ('".$res[$i][1]."');";
				$sql = "INSERT INTO `customerupload` (`customeruploadID`, `pickup`, `CustomerName`, `CustomerNumber`, `PickupDate`, `PickupAddress1`, `PickupAddress2`, `City`, `State`, `Zip`, `RecoveryNumber`, `DateReceived`, `ProcessingCenter`, `AssetNumber`, `AssetType`, `Manufacturer`, `ModelNumber`, `ModelDescription`, `ManufacturerSerialNumber`, `AssetTagNumber`, `DateDispositioned`, `DispositionedOrderType`, `DisposalComments`, `ResalePrice`, `WrittenOffValue`, `DataSecurityLevel`) VALUES (NULL, '".$id."', '".$res[$i][1]."', '".$res[$i][2]."', '".$res[$i][3]."', '".$res[$i][4]."', '".$res[$i][5]."', '".$res[$i][6]."', '".$res[$i][7]."', '".$res[$i][8]."', '".$res[$i][9]."', '".$res[$i][10]."', '".$res[$i][11]."', '".$res[$i][12]."', '".$res[$i][13]."', '".$res[$i][14]."', '".$res[$i][15]."', '".$res[$i][16]."', '".$res[$i][17]."', '".$res[$i][18]."', '".$res[$i][19]."', '".$res[$i][20]."', '".$res[$i][21]."', '".$res[$i][22]."', '".$res[$i][23]."', '".$res[$i][24]."');";
				$result = mysqli_query($this->connectionlink,$sql);
				if(mysqli_error($this->connectionlink))
					echo mysqli_error($this->connectionlink);
			}
		}*/
		//if (($handle = fopen('../../lot_documents/'.$data['Filename'], "r")) !== false) {
		if (($handle = fopen(HOST.'lot_documents/'.$data['Filename'], "r")) !== false) {			
			$column = array();
			$result = array();
			$line = 1;
			while (($row = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
				if($line == 1) {
					for($i=0;$i<count($row);$i++) {
						$column[$i] = $row[$i];
					}
					//return $column;
				}
				if($line > 1) {
					$query = $query."insert into lot_csv_data(LoadID,SerialNumber,AssetTag,Model,Type,CreatedBy,UpdatedBy,CreatedDate,UpdatedDate,AccountID) values ('".mysqli_real_escape_string($this->connectionlink,$data['loadid'])."',";
					for($i=0;$i<count($column);$i++) {
						//$result[$line-2][$column[$i]] = $row[$i];
						//$Row = $i + 1;
						$query = $query."'".mysqli_real_escape_string($this->connectionlink,$row[$i])."',";
						//$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink))
						echo mysqli_error($this->connectionlink);
					}
					$query = $query."'".$_SESSION['user']['UserId']."','".$_SESSION['user']['UserId']."',NOW(),NOW(),'".$_SESSION['user']['AccountID']."')";
					$q1 = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					else
					{
						$query = '';
					}
				}
				$line++;				
			}
			//return $result;
			//return 1;
		} 	
		$json['Success'] = true;
		$json['Result'] = "Data Uploaded Successfully";
		return json_encode($json);									
	}
	
	public function AddtoPilotPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		$data['quantity'] = (mysqli_real_escape_string($this->connectionlink,$data['quantity']) !== '') ? mysqli_real_escape_string($this->connectionlink,$data['quantity']) : 0 ;
		if($data['quantity'] == '' || $data['quantity'] == '0') {
			$data['quantity'] = $this->CalculateQuantity($data['totalWeight'],$data['category']);
		}
		//Start entering into sort pallet items
		$query = "insert into PilotPallet (PilotPalletID,PartNo,Quantity,Description,DateCreated,DateUpdated,CreatedBy,UpdatedBy,PalletID,SerialNumber,AssetTagID) values (NULL,'".mysqli_real_escape_string($this->connectionlink,$data['PartNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['Quantity'])."','".mysqli_real_escape_string($this->connectionlink,$data['Description'])."',NOW(),NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetTagID'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End entering into sort pallet items
		
		$json['Success'] = true;
		$json['Result'] = 'Success';
		return json_encode($json);
	}
	public function GetLoadPilotDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from loads l 
		left join customer c on l.idCustomer = c.CustomerID 
		left join facility f on l.FacilityID = f.FacilityID 
		where l.LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;			
			
			$query1 = "select p.*,l.LocationName,pkg.packageName,pro.ProcessName from pallets p 
			left join location l on p.WarehouseLocationId = l.LocationID 
			left join processes pro on p.ProcessID = pro.ProcessID 
			left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' and p.SortPallet = 0";
			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row1 = mysqli_fetch_assoc($q1)) {
					/*$query2 = "select * from PilotPallet where PalletID = '".$row1['idPallet']."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$j=0;
						$splits = array();
						while($row2 = mysqli_fetch_assoc($q2)) {
							$splits[$j] = $row2;
							$j++;
						}
						$row1['Splits'] = $splits;
					}*/
					$pallets[$i] = $row1;
					$i++;
				}
				$json['pallets'] = $pallets;
			} else {
				$json['pallets'] = 'NO Pilot Pallets Available';
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Load ';
		}
		return json_encode($json);
	}
	
	public function GetPalletScannedAssets($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		$query = "select * from PilotPallet where PalletID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."' ";		
		/*if($data['OrderBy'] != '') {
			$query = $query . " order by `".intval(mysqli_real_escape_string($this->connectionlink,$data['OrderBy']))."` ".intval(mysqli_real_escape_string($this->connectionlink,$data['OrderByType']))." ";
		} else {
			$query = $query . " order by `DateCreated` desc ";
		}*/
		$query = $query . " order by `DateCreated` desc ";
		$query = $query . "limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));		
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = $query;
			return json_encode($json);
		} 
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i=0;
			while($row = mysqli_fetch_assoc($q)) {				
				$result[$i] = $row;
				$i++;
			}			
			$json['Success'] = true;
			$json['Result'] = $result;
		}  else {
			$json['Success'] = false;
			$json['Result'] = 'No Records Available';
			return json_encode($json);
		}
		if($data['skip'] == 0) {			
			$sql11 = "select count(*) from PilotPallet where PalletID = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."' ";			
			$query11 = mysqli_query($this->connectionlink,$sql11);
			//$row11 = mysqli_fetch_assoc($query11);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			} 
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row11 = mysqli_fetch_assoc($query11);
				$count = $row11['count(*)'];
			}
			$json['total'] = $count;
		}
		$json['Success'] = true;
		$json['Result'] = $result;
		return json_encode($json);
	}
	public function DeleteScannedAsset($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "delete from PilotPallet where PilotPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['PilotPalletID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'Record Deleted';
		return json_encode($json);
	}
	
	public function UploadASN1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);		
		if(($data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') && ($data['file']['type'] != 'application/vnd.ms-excel')) {
			$json['Success'] = false;
			$json['Result'] = 'Invalid file format uploaded';	
			return json_encode($json);
		}
		/*if(($data['file']['type'] != 'application/vnd.ms-excel') && ($data['file']['type'] != 'application/msexcel') && ($data['file']['type'] != 'text/csv')) {
			$json['Success'] = false;
			$json['Result'] = 'Only CSV Files are Supported';	
			return json_encode($json);		
		}*/
		$filename = time().$data['file']['name'];
		$target_path = 'customer_upload/asn_upload/'.$filename;
		$upload = $this->UploadFile($target_path,$data['file']['tmp_name']);
		if($upload) {
			$json['Success'] = true;
			$json['Result'] = 'File Uploaded';
			$json['FileName'] = $filename;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Problem with File uploading';
		}		
		return json_encode($json);
	}

	public function UploadASN($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);		
		if(($data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') && ($data['file']['type'] != 'application/vnd.ms-excel')) {
			$json['Success'] = false;
			$json['Result'] = 'Invalid file format uploaded';	
			return json_encode($json);
		}		
		$filename = 'asn/'.time().$data['file']['name'];			
		$upload = $this->UploadToS3($filename,$data['file']['tmp_name']);
		if($upload) {
			$json['Success'] = true;
			$json['Result'] = 'File Uploaded';
			$json['FileName'] = $filename;				
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Unable to upload file';				
		}	
		return json_encode($json);
	}

	public function CreateASNLOT1($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);			
		if($xlsx = SimpleXLSX::parse('../../customer_upload/asn_upload/'.$data['ASNFile'])) {
		} else {
			$json['Success'] = false;
			$json['Result'] = SimpleXLSX::parseError();
			return json_encode($json);			
		}

		//Start creating LOT
		$LoadId = $this->GenerateLoadID($data['idCustomer']);
		$query = "insert into lot_details  (idCustomer,LoadId,idRefCustomer,LoadDescription,DateCreated,DateUpdated,idUser,FacilityID,LotStatus,ASNFile) values ('".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['Description'])."',NOW(),NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',1,'".mysqli_real_escape_string($this->connectionlink,$data['ASNFile'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End creating LOT

		$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','LOT Created through ASN Upload',NOW(),'".$_SESSION['user']['UserId']."')";			
		$q10 = mysqli_query($this->connectionlink,$query10);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink)."3";
			return json_encode($json);	
		}

		if($LoadId) { // Start creating Pallets
			$can_insert = false;
			$i = 0;						
			foreach ($xlsx->rows() as $elt) {
				if($can_insert) {

					//Start getting Rack Details from BOM
					$query10 = "select * from harvest_upload_data where AssetID = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$AssetID_exists = true;
						$row10 = mysqli_fetch_assoc($q10);
					} else {
						$AssetID_exists = false;
					}
					//End gettign Rack Details from BOM
					if($AssetID_exists) {						
						$query = "insert into lot_pallets (LoadId,PalletFacilityID,totalWeight,pallet_netweight,RackType,PalletDescription) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','1','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,$row10['RackType'])."','".mysqli_real_escape_string($this->connectionlink,$row10['AssetID'])."')";
					} else {
						$query = "insert into lot_pallets (LoadId,PalletFacilityID,totalWeight,pallet_netweight,PalletDescription) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','1','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."')";
					}
					//$query = "insert into lot_pallets (LoadId,PalletDescription,PalletFacilityID,totalWeight,pallet_netweight) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','1','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."')";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					
					$idPallet = mysqli_insert_id($this->connectionlink);
					if($AssetID_exists) {
						//Select class and category from AWS IPN of BOM
						$query11 = "select ProductCatID,ProductClassID from product_category where CustomerPN = '".mysqli_real_escape_string($this->connectionlink,$row10['RackIPN'])."'";
						$q11 = mysqli_query($this->connectionlink,$query11);
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row11 = mysqli_fetch_assoc($q11);
							$classID = $row11['ProductClassID'];
							$categoryID = $row11['ProductCatID'];
						} else {
							//$classID = 48;
							//$categoryID = 603;
							$classID = 0;
							$categoryID = 0;
						}
						$query1 = "insert into lot_pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$idPallet)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,$classID)."','".mysqli_real_escape_string($this->connectionlink,$categoryID)."','100','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."')";
					} else {
						$query1 = "insert into lot_pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$idPallet)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','0','0','100','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."')";
					}					
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}

					$query3 = "update lot_details set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
					$q3 = mysqli_query($this->connectionlink,$query3);
				}
				if(trim($elt[0]) == 'AWS Asset ID#') {
					$can_insert = true;
				}							
			}			

			$json['Success'] = true;
			$json['Result'] = 'LOT Created';
			$json['LOTID'] = $LoadId;
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Unable to create LOT';
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function CreateASNLOT($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);	
		

		$s3 = S3Client::factory(
			array(
				'credentials' => array(
					'key' => S3_key,
					'secret' => S3_secret
				),
				'version' => 'latest',
				'region'  => S3_region
			)
		);
		$s3->registerStreamWrapper();
		if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket.'/'.$data['ASNFile'])) {
		} else {
			$json['Success'] = false;
			$json['Result'] = SimpleXLSX::parseError();
			return json_encode($json);			
		}
				
		//Start creating LOT
		$LoadId = $this->GenerateLoadID($data['idCustomer']);
		$query = "insert into lot_details  (idCustomer,LoadId,idRefCustomer,LoadDescription,DateCreated,DateUpdated,idUser,FacilityID,LotStatus,ASNFile,TrailerID) values ('".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['idRefCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['Description'])."',NOW(),NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',1,'".mysqli_real_escape_string($this->connectionlink,$data['ASNFile'])."','".mysqli_real_escape_string($this->connectionlink,$data['TrailerID'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		//End creating LOT

		$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','LOT Created through ASN Upload',NOW(),'".$_SESSION['user']['UserId']."')";			
		$q10 = mysqli_query($this->connectionlink,$query10);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink)."3";
			return json_encode($json);	
		}

		if($LoadId) { // Start creating Pallets		
			$can_insert = false;
			$i = 0;						
			foreach ($xlsx->rows() as $elt) {				
				if($i == 1) {
					$query20 = "update lot_details set CustomerTag = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."',SealNo = '".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."' where LoadId = '".$LoadId."'";
					$q20 = mysqli_query($this->connectionlink,$query20);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
				}


				if($can_insert) {
					if($elt[5] != '') {
						//Start getting Rack Details from BOM
						$query10 = "select * from harvest_upload_data where AssetID = '".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."'";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);	
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$AssetID_exists = true;
							$row10 = mysqli_fetch_assoc($q10);
						} else {
							$AssetID_exists = false;
						}
						//End gettign Rack Details from BOM
						//$PalletID = $this->GetRandomLotPallet();
						if($AssetID_exists) {				
							$query = "insert into lot_pallets (LoadId,PalletFacilityID,totalWeight,pallet_netweight,RackType,PalletDescription,SealNo1,SealNo2,PickupDC,SealNo3,SealNo4,Comments,awsipn) values (".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."','".mysqli_real_escape_string($this->connectionlink,$row10['RackType'])."','".mysqli_real_escape_string($this->connectionlink,$row10['AssetID'])."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[6])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[7])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[4])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[8])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[9])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[12])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[13])))."')";
						} else {
							$query = "insert into lot_pallets (LoadId,PalletFacilityID,totalWeight,pallet_netweight,PalletDescription,SealNo1,SealNo2,PickupDC,SealNo3,SealNo4,Comments,awsipn) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[5])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[6])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[7])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[4])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[8])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[9])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[12])))."','".mysqli_real_escape_string($this->connectionlink,trim(str_replace(' ', '',$elt[13])))."')";
						}
						//$query = "insert into lot_pallets (LoadId,PalletDescription,PalletFacilityID,totalWeight,pallet_netweight) values ('".mysqli_real_escape_string($this->connectionlink,$LoadId)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','1','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."')";
						$q = mysqli_query($this->connectionlink,$query);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);	
						}
						
						$idPallet = mysqli_insert_id($this->connectionlink);
						//$idPallet = $PalletID;
													
						$query11 = "select ProductCatID,ProductClassID from product_category where ProductClassID != '20' and CategoryStatus = '1' and CategoryName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[11]))."'";
						$q11 = mysqli_query($this->connectionlink,$query11);
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row11 = mysqli_fetch_assoc($q11);
							$classID = $row11['ProductClassID'];
							$categoryID = $row11['ProductCatID'];
						} else {
							//$classID = 48;
							//$categoryID = 603;
							$classID = 0;
							$categoryID = 0;
						}
						$query1 = "insert into lot_pallet_items (palletId,weight,quantity,idProductClass,idProductCategory,weightPercent,totalWeight) values ('".mysqli_real_escape_string($this->connectionlink,$idPallet)."','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."','1','".mysqli_real_escape_string($this->connectionlink,$classID)."','".mysqli_real_escape_string($this->connectionlink,$categoryID)."','100','".floatval(mysqli_real_escape_string($this->connectionlink,trim($elt[10])))."')";											
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);	
						}

						$query3 = "update lot_details set TotalPallets = TotalPallets+1 where LoadId='".mysqli_real_escape_string($this->connectionlink,$LoadId)."'";
						$q3 = mysqli_query($this->connectionlink,$query3);
					}
				}
				if(trim($elt[0]) == '#') {
					$can_insert = true;
				}

				$i++;						
			}			

			$json['Success'] = true;
			$json['Result'] = 'LOT Created';
			$json['LOTID'] = $LoadId;
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Unable to create LOT';
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function ValidateRackID($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);

		$query = "select count(*) from harvest_upload_data where AssetID = '".mysqli_real_escape_string($this->connectionlink,$data['description'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		$row = mysqli_fetch_assoc($q);
		if($row['count(*)'] > 0) {
			$query1 = "update pallets set validated = '1',validateResult = '1' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['validated'] = '1';
			$json['validateResult'] = '1';
		} else {
			$query1 = "update pallets set validated = '1',validateResult = '0' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['validated'] = '1';
			$json['validateResult'] = '0';
		}

		$json['Success'] = true;
		$json['Result'] = 'Validation completed';
		return json_encode($json);
	}

	public function ValidateLoadUser($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "select count(*) from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and Status = '1'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['count(*)'] > 0) {
				$json['Success'] = true;
				$json['Result'] = 'Valid User';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invlaid Input Details';
				return json_encode($json);
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invlaid Input Details';
			return json_encode($json);
		}
	}

	public function ValidateSortPalletSealNumber($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array (
			'Success' => false,
			'Result' => $data
		);		
		$query = "select CurrentSealNumber from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0){
			$row = mysqli_fetch_assoc($q);
			if($row['CurrentSealNumber'] == $data['CurrentSealNumber']) {
				$json['Success'] = true;
				$json['Result'] = 'Seal # matched';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Seal # not matched';
				return json_encode($json);
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Custom Pallet';
			return json_encode($json);
		}
	}

	public function DeleteLot($data){
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "delete from lot_details where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		} else {
			//Start deleting lot pallets
			$query1 = "delete from lot_pallets where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			//End deleting lot pallets

			$json['Success'] = true;
			$json['Result'] = 'Lot and pallets deleted';
		}
		return json_encode($json);
	}

	public function PalletNotReceivedUpdated($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		$query = "update pallets set NotReceived = '".mysqli_real_escape_string($this->connectionlink,$data['NotReceived'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		} else {
			//Insert into Pallet Tracking
			if($data['NotReceived'] == '1') {
				$action = 'Pallet updated as Not Received';
			} else {
				$action = 'Pallet Not Received cancelled';
			}			
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			//End Inserting into Pallet Tracking			
		}
		$json['Success'] = true;
		$json['Result'] = 'Not Received Updated';
		return json_encode($json);
	}

	public function RecordLocationHistory($itemtype,$itemid,$FromLocationID,$ToLocationID,$description) {
		$query = "insert into location_history (ItemType,ItemID,FromLocationID,ToLocationID,CreatedDate,CreatedBy,Description) values ('".mysqli_real_escape_string($this->connectionlink,$itemtype)."','".mysqli_real_escape_string($this->connectionlink,$itemid)."','".mysqli_real_escape_string($this->connectionlink,$FromLocationID)."','".mysqli_real_escape_string($this->connectionlink,$ToLocationID)."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$description)."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			return mysqli_error($this->connectionlink);	
		} else {
			return 1;
		}
	}

	public function CreateBag($palletID,$BagSerial,$PickupDC,$BagCondition) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$query1 = "select * from bag_details where BagSerial = '".mysqli_real_escape_string($this->connectionlink,$BagSerial)."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {			
			return 0;	
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {//Bag returned
			$row1 = mysqli_fetch_assoc($q1);
			$query = "update bag_details set PickupDC = '".mysqli_real_escape_string($this->connectionlink,$PickupDC)."',ReceivedPalletID = '".$palletID."',BagStatusID = '1',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',CurrentLocationID = NULL,BagCondition = '".mysqli_real_escape_string($this->connectionlink,$BagCondition)."' where BagID = '".$row1['BagID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				return 0;	
			} else {
				$BagID = $row1['BagID'];
				//Start Insert in tracking
				
				$action = 'Bag Returned,  Pickup DC : '.$PickupDC.', Pallet ID : '.$palletID;
				$query2 = "insert into bag_tracking (BagID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName,BagSerial,BagCondition) values ('".$BagID."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".$palletID."',NOW(),'".$_SESSION['user']['UserId']."','','','','".mysqli_real_escape_string($this->connectionlink,$BagSerial)."','".mysqli_real_escape_string($this->connectionlink,$BagCondition)."')";			
				$q2 = mysqli_query($this->connectionlink,$query2);			
				return $BagID;
				//End Insert in tracking		
			}
		} else {

			//$BagID = $this->GetRandomBag();
			$query = "insert into bag_details (BagSerial,PickupDC,ReceivedPalletID,BagStatusID,CreatedDate,CreatedBy,BagCondition) values ('".mysqli_real_escape_string($this->connectionlink,$BagSerial)."','".mysqli_real_escape_string($this->connectionlink,$PickupDC)."','".$palletID."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$BagCondition)."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				return 0;	
			} else {
				$BagID = mysqli_insert_id($this->connectionlink);
				//$BagID = $BagID;
				//Start Insert in tracking
				
				$action = 'Bag Created,  Pickup DC : '.$PickupDC.', Pallet ID : '.$palletID;
				$query2 = "insert into bag_tracking (BagID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName,BagSerial,BagCondition) values ('".$BagID."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".$palletID."',NOW(),'".$_SESSION['user']['UserId']."','','','','".mysqli_real_escape_string($this->connectionlink,$BagSerial)."','".mysqli_real_escape_string($this->connectionlink,$BagCondition)."')";			
				$q2 = mysqli_query($this->connectionlink,$query2);			
				return $BagID;
				//End Insert in tracking		
			}	
		}		
	}

	public function GetBagConditions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from bag_conditions where ConditionStatus='Active' and AccountID='".$_SESSION['user']['AccountID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Bag Conditions Available";
		}
		return json_encode($json);
	}	

	public function GetSecurityAndSafetyConcernList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$SecurityList = array();
		$SafetyList = array();

		$query = "select * from security_concern_reasons where Status='Active' and AccountID='".$_SESSION['user']['AccountID']."' order by SecurityReason";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;			
			while ($row = mysqli_fetch_assoc($q)) {
				$SecurityList[$i] = $row;
				$i++;
			}			
			$json['SecurityList'] = $SecurityList;
		}

		$query = "select * from safety_concern_reasons where Status='Active' and AccountID='".$_SESSION['user']['AccountID']."' order by SafetyReason";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;			
			while ($row = mysqli_fetch_assoc($q)) {
				$SafetyList[$i] = $row;
				$i++;
			}			
			$json['SafetyList'] = $SafetyList;
		}
		$json['Success'] = true;
		return json_encode($json);
	}

	public function GetRandomLotPallet() {
		while(1) {
			// generate unique random number
			$randomNumber = rand(100000, 999999);
			$randomNumber = PRE.$randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `lot_pallets` WHERE idPallet = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}

	public function GetRandomPallet() {
		while(1) {
			// generate unique random number
			$randomNumber = rand(100000, 999999);
			$randomNumber = PRE.$randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `pallets` WHERE idPallet = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}

	public function GetRandomBag() {
		while(1) {
			// generate unique random number
			$randomNumber = rand(100000, 999999);
			$randomNumber = PRE.$randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `bag_details` WHERE BagID = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}


	// public function CheckDuplicate($type,$table,$column,$value,$AccountID,$ID,$IDValue) {
	// 	$duplicate = false;
	// 	if($type == 'New') { //If New Record
	// 		$query = "select count(*) from ".$table." where ".$column." = '".mysqli_real_escape_string($this->connectionlink,$value)."' and NotReceived = 0 ";
	// 		if($AccountID == true) {
	// 			$query = $query . " and AccountID = '".$_SESSION['user']['AccountID']."'";
	// 		}
	// 	} else if($type == 'Edit') {
	// 		$query = "select count(*) from ".$table." where ".$column." = '".mysqli_real_escape_string($this->connectionlink,$value)."' and ".$ID." != '".mysqli_real_escape_string($this->connectionlink,$IDValue)."' and NotReceived = 0 ";
	// 		if($AccountID == true) {
	// 			$query = $query . " and AccountID = '".$_SESSION['user']['AccountID']."'";
	// 		}
	// 	}
	// 	$q = mysqli_query($this->connectionlink,$query);		
	// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
	// 		$row = mysqli_fetch_assoc($q);
	// 		if($row['count(*)'] > 0) {
	// 			$duplicate = true;
	// 		}
	// 	} 
	// 	return $duplicate;		
	// }


	public function GetModelAndIPNDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array (
			'Success' => false,
			'Result' => 'No Data Available'
		);

		$query = "select RackIPN,externalitemname from harvest_upload_data where AssetID = '".mysqli_real_escape_string($this->connectionlink,$data['description'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['awsipn'] = $row['RackIPN'];
			$json['Model'] = $row['externalitemname'];
		} else {
			$json['Success'] = false;
			$json['Result'] = 'AWS ID not found';
			return json_encode($json);
		}
		$json['Success'] = true;
		$json['Result'] = 'Validation completed';
		return json_encode($json);
	}



	public function ValidateTPVRAuditor($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "select count(*) from users u,profile_type p where u.UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and u.Status = '1' and u.ProfileID = p.ProfileID and (p.tpvr_auditor = '1' or p.ProfileID = '1')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['count(*)'] > 0) {
				$json['Success'] = true;
				$json['Result'] = 'Valid User';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invlaid Input Details';
				return json_encode($json);
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invlaid Input Details';
			return json_encode($json);
		}
	}


	public function UploadPO($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);		
		/*if(($data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') && ($data['file']['type'] != 'application/vnd.ms-excel')) {
			$json['Success'] = false;
			$json['Result'] = 'Invalid file format uploaded';	
			return json_encode($json);
		}*/	
		//preg_replace('/[^A-Za-z0-9\-]/', '', $data['file']['name']);
		//$filename = 'po/'.time().$data['file']['name'];		
		$filename = 'po/'.time().str_replace('#', '-', $data['file']['name']);
		$upload = $this->UploadToS3AWS($filename,$data['file']['tmp_name']);
		if($upload) {
			$json['Success'] = true;
			$json['Result'] = 'File Uploaded';
			$json['FileName'] = $filename;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Unable to upload file';
		}
		return json_encode($json);
	}

	public function isQuarantineUser() {

		$query = "select count(*) from users u,profile_type p where u.UserId = '".$_SESSION['user']['UserId']."' and u.Status = '1' and u.ProfileID = p.ProfileID and p.quarantine_user = '1' ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['count(*)'] > 0) {
				return 1;
			} else {
				return 0;
			}
		} else {
			return 0;
		}

	}


	public function GetMatchingLocations($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//$query = "select LoadId from loads where LoadId like '%".$keyword."%'";		
		//$query = "select l.*,lab.lable_name from location l,lables lab where l.FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND l.Locked != 1 AND l.LocationID != 0 and l.lable_name = lab.id and isnull(l.GroupID) and l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%' limit 10 ";
		$query = "select l.* from location l where l.FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND l.Locked != 1 AND l.LocationID != 0 and isnull(l.GroupID) and l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%' limit 10 ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}

	// AWS Changes Start
	public function GetLoadDetailsNew($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select * from loads where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);			
			$json['Success'] = true;
			$json['Result'] = $row;			
			
			$query1 = "select p.*,l.LocationName,l.LocationName as location,pkg.packageName from pallets p 
			left join location l on p.WarehouseLocationId = l.LocationID 
			left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."' and p.Received = '1'";
			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i=0;
				while($row1 = mysqli_fetch_assoc($q1)) {
					//$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from pallet_items pi,product_category cat,product_class cla where pi.idProductClass = cla.ProductClassID and pi.idProductCategory = cat.ProductCatID and pi.palletId = '".$row1['idPallet']."' order by pi.id";
					
					$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from lot_pallet_items pi 
					left join product_category cat on pi.idProductCategory = cat.ProductCatID 
					left join product_class cla on pi.idProductClass = cla.ProductClassID where pi.palletId = '".$row1['idPallet']."' order by pi.id";
					
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$j=0;
						$splits = array();
						while($row2 = mysqli_fetch_assoc($q2)) {
							$row2['weight'] = floatval($row2['weight']);
							$row2['quantity'] = floatval($row2['quantity']);
							$row2['weightPercent'] = floatval($row2['weightPercent']);
							$splits[$j] = $row2;
							$j++;
						}
						$row1['Splits'] = $splits;
					}
					$pallets[$i] = $row1;
					$i++;
				}
				$json['pallets'] = $pallets;
			} else {
				$json['pallets'] = 'No Containers Available';
			}
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Ticket';
		}
		return json_encode($json);
	}


	public function GetLoadsNew($keyword) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Shipment Page';
				return json_encode($json);
			}			
			if($_SESSION['user']['CustomerBased'] == 0) {
				if($_SESSION['user']['ReferenceCustomerBased'] == 1) {
					$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' and l.idRefCustomer in (select idRefCustomer from user_assigned_reference_customers where UserId='".$_SESSION['user']['UserId']."') and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
				} else {
					$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
				}
			} else {
				$query = "select l.LoadId from loads l,customer c where l.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$keyword)."%' and l.idCustomer = c.CustomerID and c.AccountID='".$_SESSION['user']['AccountID']."' and c.CustomerID in (select CustomerID from user_assigned_customers where UserId='".$_SESSION['user']['UserId']."') and l.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			}
			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Data';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ReceivePallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		
		$query = "select Received from lot_pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['Received'] == '1') {
				$json['Success'] = false;
				$json['Result'] = 'Container already received';
				return json_encode($json);
			}			
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Container';
			return json_encode($json);
		}


		if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
			$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row20 = mysqli_fetch_assoc($q20);
				if($row20['Locked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Location is Locked';
					return json_encode($json);
				}
				$data['location'] = $row20['LocationID'];
				$LocationType = $row20['LocationType'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Location';
				return json_encode($json);
			}
		} else if($data['Group'] != '') {
			//Start get free location from group selected
			//$query = "select LocationID,LocationType from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['Group'])."'";
			$query = "select l.LocationID,l.LocationType from location l,location_group lg where l.Locked = '2' and l.LocationStatus = '1' and l.GroupID = lg.GroupID and lg.GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['Group'])."' limit 1";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$data['location'] = $row['LocationID'];
				$LocationType = $row['LocationType'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No locations available, in selected group';
				return json_encode($json);
			}
			//End get free location from group selected			
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Location and Location Group';
			return json_encode($json);
		}

		// $json['Success'] = false;
		// $json['Result'] = $LocationType;
		// return json_encode($json);

		if(true) { //If Update Pallet

			$query = "update lot_pallets set WarehouseLocationId='".mysqli_real_escape_string($this->connectionlink,$data['location'])."',status = '1',Received = '1',ReceivedDate = NOW(),ReceivedBy = '".$_SESSION['user']['UserId']."' ";

			$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Pallet',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
			$queryloc = mysqli_query($this->connectionlink,$sqlloc);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				//return json_encode($json);	
			}
			$json['Success'] = true;
			$json['Result'] = 'Container Received';
			$json['palletId'] = $data['idPallet'];			
		}
		return json_encode($json);
	}



	public function ReceiveLoad ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}
						
			if(!$data['idLoads']) { //Create New Load
				//Start check IF Ticket ID exists
				$duplicate = $this->CheckDuplicate('New','loads','LoadId',$data['LoadId'],false,'','');	
				if($duplicate) {
					$json['Success'] = false;
					$json['Result'] = 'Ticket  ID already exists';
					return json_encode($json);				
				}
				//End check IF Ticket ID exist				
				//$query = "insert into loads (LoadId,idCustomer,TotalPallets,LoadDescription,DateReceived,DateCreated,CreatedBy,FacilityID,StatusID,SealNo,SealNo2,LoadType) values ('".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','0','".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."',NOW(),NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','Manual')";
				$query = "insert into loads (LoadId,TotalPallets,LoadDescription,DateReceived,DateCreated,CreatedBy,FacilityID,StatusID,SealNo,SealNo2,LoadType) values ('".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','0','".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."',NOW(),NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','Manual')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start insert in load tracking
				$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','New Inbound Shipment Ticket Created',NOW(),'".$_SESSION['user']['UserId']."')";			
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink)."3";
					return json_encode($json);	
				}
				//End insert in load tracking


				$json['Success'] = true;
				$json['Result'] = 'New Inbound Shipment Ticket Created';
				$json['NewLoad'] = '1';
				return json_encode($json);	

			} else { // Update existing Load
				$query = "update loads set LoadDescription = '".mysqli_real_escape_string($this->connectionlink,$data['LoadDescription'])."',SealNo = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where idLoads = '".mysqli_real_escape_string($this->connectionlink,$data['idLoads'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink)."3";
					return json_encode($json);	
				}

				//Start insert in load tracking
				$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','Shipment Ticket Updated',NOW(),'".$_SESSION['user']['UserId']."')";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink)."3";
					return json_encode($json);	
				}
				//End insert in load tracking

				$json['Success'] = true;
				$json['Result'] = 'Shipment Ticket Updated';				
				return json_encode($json);	
			}

		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}		
	}

	public function GetReceiveLoadDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			$query = "select * from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$pallets = array();
				//Start get Pallet Details

				$query1 = "select p.*,l.LocationName,l.LocationName as location,pkg.packageName from pallets p 
				left join location l on p.WarehouseLocationId = l.LocationID 
				left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."' ";
				
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i=0;
					while($row1 = mysqli_fetch_assoc($q1)) {			
						//$row1['idPallet'] = intval($row1['idPallet']);
						$row1['pallet_netweight'] = floatval($row1['pallet_netweight']);
						$query2 = "select pi.*,cla.ProductClassName,cat.CategoryName from lot_pallet_items pi 
						left join product_category cat on pi.idProductCategory = cat.ProductCatID 
						left join product_class cla on pi.idProductClass = cla.ProductClassID where pi.palletId = '".$row1['idPallet']."' order by pi.id";
						
						$q2 = mysqli_query($this->connectionlink,$query2);
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$j=0;
							$splits = array();
							while($row2 = mysqli_fetch_assoc($q2)) {
								$row2['weight'] = floatval($row2['weight']);
								$row2['quantity'] = floatval($row2['quantity']);
								$row2['weightPercent'] = floatval($row2['weightPercent']);
								$splits[$j] = $row2;
								$j++;
							}
							$row1['Splits'] = $splits;
						}
						$pallets[$i] = $row1;
						$i++;
					}					
				}
				$row['Pallets'] = $pallets;;
				//End get Pallet Details


				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Ticket';
				return json_encode($json);
			}
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}		
	}



	public function UploadShipmentFile_bkp($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Shipment Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Receive Shipment Page';
				return json_encode($json);
			}
			if($data['file']['type'] != 'application/vnd.ms-excel' && $data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
				$json['Success'] = false;
				$json['Result'] = 'Invalid File type';	
				return json_encode($json);		
			}
			$filename = time().$data['file']['name'];
			$target_path = '../../uploads/'.$filename;
			if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {

				$query = "insert into uploaded_loads (FilePath,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$filename)."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$UploadID = mysqli_insert_id($this->connectionlink);

				if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
					$can_insert = false;		
					$assets_count = 0;	
					$i = 0;
					$a = 1;
					foreach ($xlsx->rows() as $elt) {// Looking full excel for validation
						if($a == 1) { // Executes only for first row
							if(trim($elt[0]) != 'origin_location_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'origin_location_id'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'origin_ticket_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'origin_ticket_id'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'container_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'container_id'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'seal1') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'seal1'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'seal2') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'seal2'";
								return json_encode($json);
							}

							if(trim($elt[5]) != 'seal3') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell F1 Should be 'seal3'";
								return json_encode($json);
							}

							if(trim($elt[6]) != 'seal4') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell G1 Should be 'seal4'";
								return json_encode($json);
							}

							if(trim($elt[7]) != 'serial_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell H1 Should be 'serial_id'";
								return json_encode($json);
							}

							if(trim($elt[8]) != 'mpn_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell I1 Should be 'mpn_id'";
								return json_encode($json);
							}
						}

						if($a > 1) {//looping through data
							if(trim($elt[0]) != '' || trim($elt[1]) != '' || trim($elt[2]) != '' || trim($elt[3]) != '' || trim($elt[4]) != '' || trim($elt[5]) != '' || trim($elt[6]) != '' || trim($elt[7]) != '' || trim($elt[8]) != '') {

								/*if(trim($elt[0]) != '') { //origin_location_id
									//Start check If Customer exists in our database
									$query = "select count(*) from customer where CustomerShotCode = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row = mysqli_fetch_assoc($q);
										if($row['count(*)'] == 0) {
											$json['Success'] = false;
											$json['Result'] = "origin_location_id from row ".$a." (".trim($elt[0]).") does not exist";
											return json_encode($json);
										}
									}
									//End check If Customer exists in our database
								} else {
									$json['Success'] = false;
									$json['Result'] = "origin_location_id from row ".$a." is missing";
									return json_encode($json);
								}*/
								
								if(trim($elt[1]) != '') { //origin_ticket_id
	
									//Start check If Ticket ID exists in our database
									$query = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."'";
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row = mysqli_fetch_assoc($q);
										if($row['count(*)'] > 0) {
											$json['Success'] = false;
											$json['Result'] = "origin_ticket_id from row ".$a." (".trim($elt[1]).") already exists";
											return json_encode($json);
										}
									}
									//End check If Ticket ID in our database
	
								} else {
									$json['Success'] = false;
									$json['Result'] = "origin_ticket_id from row ".$a." is missing";
									return json_encode($json);
								}
	
								if(trim($elt[2]) != '') {
	
									//Start check If Ticket ID exists in our database
									$query = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."'";
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row = mysqli_fetch_assoc($q);
										if($row['count(*)'] > 0) {
											$json['Success'] = false;
											$json['Result'] = "container_id from row ".$a." (".trim($elt[2]).") already exists";
											return json_encode($json);
										}
									}
									//End check If Ticket ID in our database
	
								} else {
									$json['Success'] = false;
									$json['Result'] = "container_id from row ".$a." is missing";
									return json_encode($json);
								}
	
								if(trim($elt[7]) != '') {
	
								} else {
									$json['Success'] = false;
									$json['Result'] = "serial_id from row ".$a." is missing";
									return json_encode($json);
								}
	
								if(trim($elt[8]) != '') {
	
								} else {
									$json['Success'] = false;
									$json['Result'] = "mpn_id from row ".$a." is missing";
									return json_encode($json);
								}

							}
						}
						$a++;
					}


					foreach ($xlsx->rows() as $elt) {
						//Start validate File Format
						if($i == 0) { // Executes only for first row
							if(trim($elt[0]) != 'origin_location_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'origin_location_id'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'origin_ticket_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'origin_ticket_id'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'container_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'container_id'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'seal1') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'seal1'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'seal2') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'seal2'";
								return json_encode($json);
							}

							if(trim($elt[5]) != 'seal3') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell F1 Should be 'seal3'";
								return json_encode($json);
							}

							if(trim($elt[6]) != 'seal4') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell G1 Should be 'seal4'";
								return json_encode($json);
							}

							if(trim($elt[7]) != 'serial_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell H1 Should be 'serial_id'";
								return json_encode($json);
							}

							if(trim($elt[8]) != 'mpn_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell I1 Should be 'mpn_id'";
								return json_encode($json);
							}
						}
						//End validate File Format
						if($can_insert) {
							if(trim($elt[0]) != '' || trim($elt[1]) != '' || trim($elt[2]) != '' || trim($elt[3]) != '' || trim($elt[4]) != '' || trim($elt[5]) != '' || trim($elt[6]) != '' || trim($elt[7]) != '' || trim($elt[8]) != '') {
								$exception = false;
								$exception_message = '';
								$exception_field = '';
								if(trim($elt[0]) != '') { //origin_location_id
									//Start check If Customer exists in our database
									$query101 = "select count(*) from customer where CustomerShotCode = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
									$query101 = "select * from customer where CustomerShotCode = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$idCustomer = $row101['CustomerID'];
									} else {
										$exception = true;
										$exception_message = 'origin_location_id does not exist';
										$exception_field = 'idCustomer';
										$idCustomer = NULL;
									}
									//End check If Customer exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing origin_location_id';
									$exception_field = 'idCustomer';
									$idCustomer = NULL;
								}

								if($exception) {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,ExceptionReason,ExceptionField,ExceptionType,ActualSerialNumber) values ('".$UploadID."',";
									if($idCustomer > 0) {
										$query1 = $query1 ."'".$idCustomer."'";
									} else {
										$query1 = $query1 ."NULL";
									}								
									$query1 = $query1 .",'".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Exception','".$exception_message."','".$exception_field."','File Upload','".$data['OriSerialNumber']."')";
								} else {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,ActualSerialNumber) values ('".$UploadID."','".$idCustomer."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Active','".$data['OriSerialNumber']."')";
								}							
								$q1 = mysqli_query($this->connectionlink,$query1);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}

								$LoadID = trim($elt[1]);
								$PalletID = trim($elt[2]);
								$SerialNumber = trim($elt[7]);
								$MPN = trim($elt[8]);
								
								if(!$exception) {
									//Start check If Serial Number already exists for the Load
									$query = "select count(*) from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row = mysqli_fetch_assoc($q);
										if($row['count(*)'] == 0) {// New Serial Number
											//start check If Load exists
											$query1 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
											$q1 = mysqli_query($this->connectionlink,$query1);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row1 = mysqli_fetch_assoc($q1);
												if($row1['count(*)'] == 0) {
													//$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','119',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$idCustomer)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$q2 = mysqli_query($this->connectionlink,$query2);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													//Start insert in load tracking
													$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','New Inbound Shipment Ticket Created through File Upload',NOW(),'".$_SESSION['user']['UserId']."')";			
													$q10 = mysqli_query($this->connectionlink,$query10);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink)."3";
														return json_encode($json);	
													}
													//End insert in load tracking

												}
											}
											//End check If Load exists

											//Start check If Pallet exists
											$query3 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												if($row3['count(*)'] == 0) {
													$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$LoadID)."','6',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$q4 = mysqli_query($this->connectionlink,$query4);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','Container Created through File Upload','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
													$q12 = mysqli_query($this->connectionlink,$query12);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);	
													}

												}
											}
											//end check If Pallet exists


											//Start check If Pallet Item exists
											$query3 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' and UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												$pallet_item_id = $row3['id'];

												$query5 = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."'";
												$q5 = mysqli_query($this->connectionlink,$query5);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
											} else {
												$query4 = "insert into pallet_items (palletId,quantity,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','1','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."')";
												$q4 = mysqli_query($this->connectionlink,$query4);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
												$pallet_item_id = mysqli_insert_id($this->connectionlink);
											}
											//end check If Pallet Item exists


											//Start create asset
											$OriSerialNumber = $SerialNumber;
											$SerialNumber = preg_replace('/[^A-Za-z0-9]/', '', $SerialNumber);
											$query6 = "insert into asn_assets (LoadId,idPallet,PalletItemsID,SerialNumber,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."','".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."','".$OriSerialNumber."')";
											$q6 = mysqli_query($this->connectionlink,$query6);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											$assets_count = $assets_count + 1;
											//End create asset								
										} else {

										}
									}
									//End check IF Serial Number alredy exists for the Load
								}
							}														
						}
						if(trim($elt[0]) == 'origin_location_id') {
							$can_insert = true;
						}
						$i++;
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = SimpleXLSX::parseError();
					return json_encode($json);			
				}

				$transaction = 'Receive ---> Receive Shipment';
				$description = 'File Uploaded';				
				$this->RecordUserTransaction($transaction,$description);
				

				$json['Success'] = true;
				$json['Result'] = 'File Uploaded';
				$json['FileName'] = $filename;	
				$json['Count'] = $assets_count;	
				return json_encode($json);




				// $query = "insert into uploaded_loads (FilePath,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$filename)."',NOW(),'".$_SESSION['user']['UserId']."')";
				// $q = mysqli_query($this->connectionlink,$query);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// $UploadID = mysqli_insert_id($this->connectionlink);
				// $json['UploadID'] = $UploadID;
				// if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
				// 	$can_insert = false;					
				// 	foreach ($xlsx->rows() as $elt) {
				// 		if($can_insert) {
				// 			$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status) values ('".$UploadID."','119','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Active')";
				// 			$q1 = mysqli_query($this->connectionlink,$query1);
				// 			if(mysqli_error($this->connectionlink)) {
				// 				$json['Success'] = false;
				// 				$json['Result'] = mysqli_error($this->connectionlink);
				// 				return json_encode($json);
				// 			}
				// 		}
				// 		if(trim($elt[0]) == 'origin_location_id') {
				// 			$can_insert = true;
				// 		}						
				// 	}
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = SimpleXLSX::parseError();
				// 	return json_encode($json);			
				// }

				// $json['Success'] = true;
				// $json['Result'] = 'File Uploaded';
				// $json['FileName'] = $filename;								
			} else{
				$json['Success'] = false;
				$json['Result'] = 'Problem with File uploading';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UploadShipmentFile($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}
			if($data['file']['type'] != 'application/vnd.ms-excel' && $data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
				$json['Success'] = false;
				$json['Result'] = 'Invalid File type';
				return json_encode($json);
			}
			$filename = time().$data['file']['name'];
			$target_path = '../../uploads/'.$filename;

			$upload = $this->UploadToS3($filename,$data['file']['tmp_name']);
			if($upload) {				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Unable to upload file';
				$json['Upload'] = $upload;
				return json_encode($json);
			}

			//if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			if(true) {
				$query = "insert into uploaded_loads (FilePath,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$filename)."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$UploadID = mysqli_insert_id($this->connectionlink);

				$s3 = S3Client::factory(
					array(
						'credentials' => array(
							'key' => S3_key_eviridis,
							'secret' => S3_secret_eviridis
						),
						'version' => 'latest',
						'region'  => S3_region_eviridis
					)
				);
				$s3->registerStreamWrapper();
				// if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = SimpleXLSX::parseError();
				// 	return json_encode($json);			
				// }

				//if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
				if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
				//if(true) {
					$can_insert = false;		
					$assets_count = 0;	
					$i = 0;
					$a = 1;

					foreach ($xlsx->rows() as $elt) {
						//Start validate File Format
						if($i == 0) { // Executes only for first row
							if(trim($elt[0]) != 'origin_location_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'origin_location_id'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'origin_ticket_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'origin_ticket_id'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'container_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'container_id'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'seal1') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'seal1'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'seal2') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'seal2'";
								return json_encode($json);
							}

							if(trim($elt[5]) != 'seal3') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell F1 Should be 'seal3'";
								return json_encode($json);
							}

							if(trim($elt[6]) != 'seal4') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell G1 Should be 'seal4'";
								return json_encode($json);
							}

							if(trim($elt[7]) != 'serial_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell H1 Should be 'serial_id'";
								return json_encode($json);
							}

							if(trim($elt[8]) != 'mpn_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell I1 Should be 'mpn_id'";
								return json_encode($json);
							}

							if(trim($elt[9]) != 'material_type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell J1 Should be 'material_type'";
								return json_encode($json);
							}
						}
						//End validate File Format
						if($can_insert) {
							if(trim($elt[1]) != '' || trim($elt[2]) != '' || trim($elt[3]) != '' || trim($elt[4]) != '' || trim($elt[5]) != '' || trim($elt[6]) != '' || trim($elt[7]) != '' || trim($elt[8]) != '' || trim($elt[9]) != '') {
								$exception = false;
								$exception_message = '';
								$exception_field = '';
								if(trim($elt[0]) != '') { //origin_location_id
									//Start check If Customer exists in our database									
									$query101 = "select * from customer where CustomerShotCode = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$idCustomer = $row101['CustomerID'];
										$AWSCustomerID = $row101['AWSCustomerID'];
									} else {
										// $exception = true;
										// $exception_message = 'origin_location_id does not exist';
										// $exception_field = 'idCustomer';
										$idCustomer = NULL;
										$AWSCustomerID = NULL;
									}
									//End check If Customer exists in our database								
								} else {
									// $exception = true;
									// $exception_message = 'Missing origin_location_id';
									// $exception_field = 'idCustomer';
									$idCustomer = NULL;
								}

								if(trim($elt[9]) != '') { //material_type
									if(trim($elt[9]) == 'Component' || $elt[9] == 'Loose Gear' || $elt[9] == 'Decom Rack' || $elt[9] == 'Media Rack') {

									} else {
										$exception = true;
										$exception_message = $exception_message . ' Invalid material_type';
										$exception_field = $exception_field . ' MaterialType';
									}									
								} else {
									$exception = true;
									$exception_message = $exception_message . ' Invalid material_type';
									$exception_field = $exception_field . ' MaterialType';								
								}

								if(trim($elt[2]) == '') {//Empty Container ID
									$exception = true;
									$exception_message = $exception_message . ' Invalid container_id';
									$exception_field = $exception_field . ' idPallet';
								}

								if(trim($elt[1]) == '') {//Empty Ticket ID
									$exception = true;
									$exception_message = $exception_message . ' Invalid origin_ticket_id';
									$exception_field = $exception_field . ' LoadId';
								}

								if($exception) {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9_-]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,MaterialType,ExceptionReason,ExceptionField,ExceptionType,ActualSerialNumber) values ('".$UploadID."',";
									if($idCustomer > 0) {
										$query1 = $query1 ."'".$idCustomer."'";
									} else {
										$query1 = $query1 ."NULL";
									}								
									$query1 = $query1 .",'".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Exception','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."','".$exception_message."','".$exception_field."','File Upload','".$data['OriSerialNumber']."')";
								} else {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9_-]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,MaterialType,ActualSerialNumber) values ('".$UploadID."','".$idCustomer."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Active','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."','".$data['OriSerialNumber']."')";
								}							
								$q1 = mysqli_query($this->connectionlink,$query1);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}

								$LoadID = trim($elt[1]);
								$PalletID = trim($elt[2]);
								$SerialNumber = trim($elt[7]);
								$MPN = trim($elt[8]);

								//Start get APN from MPN
								//$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
								$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
								$q191 = mysqli_query($this->connectionlink,$query191);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$row191 = mysqli_fetch_assoc($q191);
									$APN = $row191['apn_id'];
									$PART_TYPE = $row191['part_type'];
									$ID_MANU = $row191['idManufacturer'];
								} else {
									$APN = '';
									$PART_TYPE = 'n/a';
									$ID_MANU = '';
								}
								//End get APN from MPN
								
								if(!$exception) {
									//Start check If Serial Number already exists for the Load
									$query = "select count(*) from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row = mysqli_fetch_assoc($q);
										if($row['count(*)'] == 0) {// New Serial Number
											//start check If Load exists
											$query1 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
											$q1 = mysqli_query($this->connectionlink,$query1);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row1 = mysqli_fetch_assoc($q1);
												if($row1['count(*)'] == 0) {
													//$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','119',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													//$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$idCustomer)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$query2 = "insert into loads (LoadId,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$q2 = mysqli_query($this->connectionlink,$query2);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													//Start insert in load tracking
													$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','New Inbound Shipment Ticket Created through File Upload',NOW(),'".$_SESSION['user']['UserId']."')";			
													$q10 = mysqli_query($this->connectionlink,$query10);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink)."3";
														return json_encode($json);	
													}
													//End insert in load tracking

												}
											}
											//End check If Load exists

											//Start check If Pallet exists
											$query3 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												if($row3['count(*)'] == 0) {
													if(trim($elt[4]) == '') {
														$elt[4] = 'n/a';
													}
													if(trim($elt[5]) == '') {
														$elt[5] = 'n/a';
													}
													if(trim($elt[6]) == '') {
														$elt[6] = 'n/a';
													}
													$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,UploadID,MaterialType";

													if($idCustomer > 0) {
														$query4 = $query4 .",idCustomer";
													} 

													if($AWSCustomerID > 0) {
														$query4 = $query4 .",AWSCustomerID";
													} 

													$query4 = $query4 . ") values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$LoadID)."','6',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".$_SESSION['user']['FacilityID']."','".$UploadID."','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."'";
													
													if($idCustomer > 0) {
														$query4 = $query4 .",'".$idCustomer."'";
													}
													if($AWSCustomerID > 0) {
														$query4 = $query4 .",'".$AWSCustomerID."'";
													}

													$query4 = $query4 . ")";
													$q4 = mysqli_query($this->connectionlink,$query4);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','Container Created through File Upload','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
													$q12 = mysqli_query($this->connectionlink,$query12);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);	
													}

												}
											}
											//end check If Pallet exists


											//Start check If Pallet Item exists
											$query3 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' and UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												$pallet_item_id = $row3['id'];

												$query5 = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."'";
												$q5 = mysqli_query($this->connectionlink,$query5);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
											} else {
												$query4 = "insert into pallet_items (palletId,quantity,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','1','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."')";
												$q4 = mysqli_query($this->connectionlink,$query4);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
												$pallet_item_id = mysqli_insert_id($this->connectionlink);
											}
											//end check If Pallet Item exists


											//Start create asset
											$OriSerialNumber = $SerialNumber;
											$SerialNumber = preg_replace('/[^A-Za-z0-9_-]/', '', $SerialNumber);
											$query6 = "insert into asn_assets (LoadId,idPallet,PalletItemsID,SerialNumber,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID,OriginalPalletID,apn_id,part_type,idManufacturer,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."','".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$APN)."','".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."','".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."','".$OriSerialNumber."')";
											$q6 = mysqli_query($this->connectionlink,$query6);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											$assets_count = $assets_count + 1;
											//End create asset								
										} else {

										}
									}
									//End check IF Serial Number alredy exists for the Load
								}
							}														
						}
						if(trim($elt[0]) == 'origin_location_id') {
							$can_insert = true;
						}
						$i++;
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = SimpleXLSX::parseError();
					return json_encode($json);			
				}

				$transaction = 'Receive ---> Receive Shipment';
				$description = 'File Uploaded';				
				$this->RecordUserTransaction($transaction,$description);

				$json['Success'] = true;
				$json['Result'] = 'File Uploaded';
				$json['FileName'] = $filename;	
				$json['Count'] = $assets_count;
				return json_encode($json);										
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Problem with File uploading';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function UploadShipmentFile1($data = false) {		
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		try {
			echo "here";
			$filename = 'WW-RZRR-3805.xlsx';
			//if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			if(true) {
				$query = "insert into uploaded_loads (FilePath,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$filename)."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$UploadID = mysqli_insert_id($this->connectionlink);

				$s3 = S3Client::factory(
					array(
						'credentials' => array(
							'key' => S3_key_eviridis,
							'secret' => S3_secret_eviridis
						),
						'version' => 'latest',
						'region'  => S3_region_eviridis
					)
				);
				$s3->registerStreamWrapper();
				// if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = SimpleXLSX::parseError();
				// 	return json_encode($json);			
				// }

				//if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
				if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
					//if(true) {
					$can_insert = false;		
					$assets_count = 0;	
					$i = 0;
					$a = 1;

					foreach ($xlsx->rows() as $elt) {
						//Start validate File Format
						if($i == 0) { // Executes only for first row
							if(trim($elt[0]) != 'origin_location_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'origin_location_id'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'origin_ticket_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'origin_ticket_id'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'container_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'container_id'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'seal1') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'seal1'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'seal2') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'seal2'";
								return json_encode($json);
							}

							if(trim($elt[5]) != 'seal3') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell F1 Should be 'seal3'";
								return json_encode($json);
							}

							if(trim($elt[6]) != 'seal4') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell G1 Should be 'seal4'";
								return json_encode($json);
							}

							if(trim($elt[7]) != 'serial_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell H1 Should be 'serial_id'";
								return json_encode($json);
							}

							if(trim($elt[8]) != 'mpn_id') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell I1 Should be 'mpn_id'";
								return json_encode($json);
							}

							if(trim($elt[9]) != 'material_type') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell J1 Should be 'material_type'";
								return json_encode($json);
							}
						}
						//End validate File Format
						if($can_insert) {
							if(trim($elt[1]) != '' || trim($elt[2]) != '' || trim($elt[3]) != '' || trim($elt[4]) != '' || trim($elt[5]) != '' || trim($elt[6]) != '' || trim($elt[7]) != '' || trim($elt[8]) != '' || trim($elt[9]) != '') {
								$exception = false;
								$exception_message = '';
								$exception_field = '';
								if(trim($elt[0]) != '') { //origin_location_id
									//Start check If Customer exists in our database									
									$query101 = "select * from customer where CustomerShotCode = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$idCustomer = $row101['CustomerID'];
										$AWSCustomerID = $row101['AWSCustomerID'];
									} else {
										// $exception = true;
										// $exception_message = 'origin_location_id does not exist';
										// $exception_field = 'idCustomer';
										$idCustomer = NULL;
										$AWSCustomerID = NULL;
									}
									//End check If Customer exists in our database								
								} else {
									// $exception = true;
									// $exception_message = 'Missing origin_location_id';
									// $exception_field = 'idCustomer';
									$idCustomer = NULL;
								}

								if(trim($elt[9]) != '') { //material_type
									if(trim($elt[9]) == 'Component' || $elt[9] == 'Loose Gear' || $elt[9] == 'Decom Rack' || $elt[9] == 'Media Rack') {

									} else {
										$exception = true;
										$exception_message = $exception_message . ' Invalid material_type';
										$exception_field = $exception_field . ' MaterialType';
									}									
								} else {
									$exception = true;
									$exception_message = $exception_message . ' Invalid material_type';
									$exception_field = $exception_field . ' MaterialType';								
								}

								if(trim($elt[2]) == '') {//Empty Container ID
									$exception = true;
									$exception_message = $exception_message . ' Invalid container_id';
									$exception_field = $exception_field . ' idPallet';
								}

								if(trim($elt[1]) == '') {//Empty Ticket ID
									$exception = true;
									$exception_message = $exception_message . ' Invalid origin_ticket_id';
									$exception_field = $exception_field . ' LoadId';
								}

								if($exception) {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9_-]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,MaterialType,ExceptionReason,ExceptionField,ExceptionType,ActualSerialNumber) values ('".$UploadID."',";
									if($idCustomer > 0) {
										$query1 = $query1 ."'".$idCustomer."'";
									} else {
										$query1 = $query1 ."NULL";
									}								
									$query1 = $query1 .",'".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Exception','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."','".$exception_message."','".$exception_field."','File Upload','".$data['OriSerialNumber']."')";
								} else {
									$data['OriSerialNumber'] = $elt[7];
									$elt[7] = preg_replace('/[^A-Za-z0-9_-]/', '', $elt[7]);
									$query1 = "insert into uploaded_loads_details (UploadID,idCustomer,LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4,SerialNumber,MPN,CreatedDate,CreatedBy,Status,MaterialType,ActualSerialNumber) values ('".$UploadID."','".$idCustomer."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[7]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[8]))."',NOW(),'".$_SESSION['user']['UserId']."','Active','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."','".$data['OriSerialNumber']."')";
								}							
								$q1 = mysqli_query($this->connectionlink,$query1);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}

								$LoadID = trim($elt[1]);
								$PalletID = trim($elt[2]);
								$SerialNumber = trim($elt[7]);
								$MPN = trim($elt[8]);

								//Start get APN from MPN
								//$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
								$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
								$q191 = mysqli_query($this->connectionlink,$query191);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$row191 = mysqli_fetch_assoc($q191);
									$APN = $row191['apn_id'];
									$PART_TYPE = $row191['part_type'];
									$ID_MANU = $row191['idManufacturer'];
								} else {
									$APN = '';
									$PART_TYPE = 'n/a';
									$ID_MANU = '';
								}
								//End get APN from MPN
								
								if(!$exception) {
									//Start check If Serial Number already exists for the Load
									// $query = "select count(*) from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
									// $q = mysqli_query($this->connectionlink,$query);
									// if(mysqli_error($this->connectionlink)) {
									// 	$json['Success'] = false;
									// 	$json['Result'] = mysqli_error($this->connectionlink);
									// 	return json_encode($json);
									// }
									//if(mysqli_affected_rows($this->connectionlink) > 0) {
										//$row = mysqli_fetch_assoc($q);
										//if($row['count(*)'] == 0) {// New Serial Number
											//start check If Load exists
											$query1 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$LoadID)."'";
											$q1 = mysqli_query($this->connectionlink,$query1);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row1 = mysqli_fetch_assoc($q1);
												if($row1['count(*)'] == 0) {
													//$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','119',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													//$query2 = "insert into loads (LoadId,idCustomer,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$idCustomer)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$query2 = "insert into loads (LoadId,DateCreated,CreatedBy,LoadType,FacilityID,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$_SESSION['user']['FacilityID']."','".$UploadID."')";
													$q2 = mysqli_query($this->connectionlink,$query2);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													//Start insert in load tracking
													$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','New Inbound Shipment Ticket Created through File Upload',NOW(),'".$_SESSION['user']['UserId']."')";			
													$q10 = mysqli_query($this->connectionlink,$query10);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink)."3";
														return json_encode($json);	
													}
													//End insert in load tracking

												}
											}
											//End check If Load exists

											//Start check If Pallet exists
											$query3 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												if($row3['count(*)'] == 0) {
													if(trim($elt[4]) == '') {
														$elt[4] = 'n/a';
													}
													if(trim($elt[5]) == '') {
														$elt[5] = 'n/a';
													}
													if(trim($elt[6]) == '') {
														$elt[6] = 'n/a';
													}
													$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,UploadID,MaterialType";

													if($idCustomer > 0) {
														$query4 = $query4 .",idCustomer";
													} 

													if($AWSCustomerID > 0) {
														$query4 = $query4 .",AWSCustomerID";
													} 

													$query4 = $query4 . ") values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$LoadID)."','6',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[5]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[6]))."','".$_SESSION['user']['FacilityID']."','".$UploadID."','".mysqli_real_escape_string($this->connectionlink,trim($elt[9]))."'";
													
													if($idCustomer > 0) {
														$query4 = $query4 .",'".$idCustomer."'";
													}
													if($AWSCustomerID > 0) {
														$query4 = $query4 .",'".$AWSCustomerID."'";
													}

													$query4 = $query4 . ")";
													$q4 = mysqli_query($this->connectionlink,$query4);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);
													}

													$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','Container Created through File Upload','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
													$q12 = mysqli_query($this->connectionlink,$query12);
													if(mysqli_error($this->connectionlink)) {
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);	
													}

												}
											}
											//end check If Pallet exists


											//Start check If Pallet Item exists
											$query3 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' and UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
											$q3 = mysqli_query($this->connectionlink,$query3);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row3 = mysqli_fetch_assoc($q3);
												$pallet_item_id = $row3['id'];

												$query5 = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."'";
												$q5 = mysqli_query($this->connectionlink,$query5);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
											} else {
												$query4 = "insert into pallet_items (palletId,quantity,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','1','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."')";
												$q4 = mysqli_query($this->connectionlink,$query4);
												if(mysqli_error($this->connectionlink)) {
													$json['Success'] = false;
													$json['Result'] = mysqli_error($this->connectionlink);
													return json_encode($json);
												}
												$pallet_item_id = mysqli_insert_id($this->connectionlink);
											}
											//end check If Pallet Item exists


											//Start create asset
											$OriSerialNumber = $SerialNumber;
											$SerialNumber = preg_replace('/[^A-Za-z0-9_-]/', '', $SerialNumber);
											$query6 = "insert into asn_assets (LoadId,idPallet,PalletItemsID,SerialNumber,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID,OriginalPalletID,apn_id,part_type,idManufacturer,ActualSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$LoadID)."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."','".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','File Upload','".$UploadID."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$APN)."','".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."','".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."','".$OriSerialNumber."')";
											$q6 = mysqli_query($this->connectionlink,$query6);
											if(mysqli_error($this->connectionlink)) {
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											$assets_count = $assets_count + 1;
											//End create asset								
										// } else {

										// }
									//}
									//End check IF Serial Number alredy exists for the Load
								}
							}														
						}
						if(trim($elt[0]) == 'origin_location_id') {
							$can_insert = true;
						}
						$i++;
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = SimpleXLSX::parseError();
					return json_encode($json);			
				}

				$transaction = 'Receive ---> Receive Shipment';
				$description = 'File Uploaded';				
				$this->RecordUserTransaction($transaction,$description);

				$json['Success'] = true;
				$json['Result'] = 'File Uploaded';
				$json['FileName'] = $filename;	
				$json['Count'] = $assets_count;
				return json_encode($json);										
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Problem with File uploading';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetUploadShipmentDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Upload Shipments')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Upload Shipments Page';
			return json_encode($json);
		}

		$query = "SELECT LoadId,idPallet,SealNo1,SealNo2,SealNo3,SealNo4 FROM uploaded_loads_details where UploadID = '".mysqli_real_escape_string($this->connectionlink,$data['UploadID'])."' group by idPallet";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}


	public function GetPendingReceivePallets1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Shipment Page';
				return json_encode($json);
			}
			$query = "select p.* from pallets p where status = 6 order by CreatedDate desc limit 10";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$processes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$processes[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $processes;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Shipments Available";
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetPendingReceivePallets($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['LoadId']
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Shipment Page';
				return json_encode($json);
			}
			/*$query = "select p.* from pallets p where status=1";*/
			$query = "select p.* from pallets p where status = 6 ";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'LoadId') {
							$query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'idPallet') {
							$query = $query . " AND p.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CreatedDate') {
							$query = $query . " AND p.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo1') {
							$query = $query . " AND p.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo2') {
							$query = $query . " AND p.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo3') {
							$query = $query . " AND p.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo4') {
							$query = $query . " AND p.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}											
					}
				}
			}

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				  if($data['OrderBy'] == 'LoadId') {
					$query = $query . " order by p.LoadId ".$order_by_type." ";
				} else if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by p.idPallet ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by p.CreatedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo1') {
					$query = $query . " order by p.SealNo1 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo2') {
					$query = $query . " order by p.SealNo2 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo3') {
					$query = $query . " order by p.SealNo3 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo4') {
					$query = $query . " order by p.SealNo4 ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Pending Containers Available";
			}
			
			if($data['skip'] == 0) {

				$query1 = "select count(*) from pallets p where status = 6 ";	
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'LoadId') {
								$query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'idPallet') {
								$query1 = $query1 . " AND p.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'CreatedDate') {
								$query1 = $query1 . " AND p.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo1') {
								$query1 = $query1 . " AND p.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo2') {
								$query1 = $query1 . " AND p.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo3') {
								$query1 = $query1 . " AND p.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SealNo4') {
								$query1 = $query1 . " AND p.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}													
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}
	
	public function ReceivePendingPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Shipment Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Receive Shipment')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Receive Shipment Page';
				return json_encode($json);
			}

			if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				$q20 = mysqli_query($this->connectionlink,$query20);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row20 = mysqli_fetch_assoc($q20);
					if($row20['Locked'] == '1') {
						$json['Success'] = false;
						$json['Result'] = 'Location is Locked';
						return json_encode($json);
					}
					$data['location'] = $row20['LocationID'];
					$LocationType = $row20['LocationType'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Location';
				return json_encode($json);
			}
			if(true) { //If Update Pallet
				$query = "update pallets set Received = '1',ReceivedDate = NOW(),Verified = '1',WarehouseLocationId = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."',status = '1',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' ";


				$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}

				$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],'0',$data['location'],'New Container Received');

				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container received at location ','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Container Received';
				$json['palletId'] = $data['idPallet'];			
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	

	public function UpdatePallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Shipments Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Shipments Page';
				return json_encode($json);
			}
			//Start get Pallet Data
			$query = "select * from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);	
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$old = mysqli_fetch_assoc($q);				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);
			}
			//End get Pallet Data

			if($old['status'] == '7' && $data['PasswordVerified'] != 'true') {
				$json['TPVRRequired'] = '1';
				$json['Success'] = false;
				$json['Result'] = 'TPVR required for moving Container out of Quarantine';
				return json_encode($json);
			}


			//Start call API for getting Servers and Switches of Pallet
			if($old['MaterialType'] == 'Media Rack' && $old['ServersCreatedBy'] == NULL) {
				$create_items = $this->GetRackItems($data['idPallet']);
				if($create_items['Success'] == true) {
					$query4 = "update pallets set ServersCreatedDate = NOW(),ServersCreatedBy = '".$_SESSION['user']['UserId']."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					//Start get all created items
					$query16 = "select SerialNumber,Type,HDDCount,SSDCount from speed_expected_servers where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
					$q16 = mysqli_query($this->connectionlink,$query16);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = $query2;
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$serials = array();
						$k=0;
						while($row16 = mysqli_fetch_assoc($q16)) {

							//Start get Media of Servers
							//$query17 = "Select ServerSerialNumber,MediaSerialNumber,MediaType,MediaMPN from speed_expected_media where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$row16['SerialNumber'])."' ";
							$query17 = "Select ServerSerialNumber,MediaSerialNumber,MediaMPN from speed_expected_media where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$row16['SerialNumber'])."' ";

							$q17 = mysqli_query($this->connectionlink,$query17);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = $query2;
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$server_serials = array();
								$l=0;
								while($row17 = mysqli_fetch_assoc($q17)) {
									$server_serials[$l] = $row17;
									$l++;
								}
							} else {
								$server_serials = array();
							}
							$row16['ServerMedia'] = $server_serials;
							//End get Media of Servers


							$serials[$k] = $row16;
							$k++;
						}
						$json['Serials'] = $serials;
					}
					//End get all created items

				} else {
					$json['APIFailed'] = '1';
					$json['APIERROR'] = '1';
					$json['Success'] = false;
					$json['Serials'] = 'Failure API Response '.$create_items['Error'];
					return json_encode($json);
				}
			}
			//End call API for getting Servers and Switches of Pallet

			/*if($data['location'] != $data['LocationName'])	{ // Location Changed
				if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
					$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
					$q20 = mysqli_query($this->connectionlink,$query20);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row20 = mysqli_fetch_assoc($q20);
						if($row20['Locked'] == '1') { 
							$json['Success'] = false;
							$json['Result'] = 'Location is Locked';
							return json_encode($json);
						}

						if($row20['LocationType'] != 'Inbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Selected Location is not for Inbound Storage';
							return json_encode($json);
						}
						$data['WarehouseLocationId'] = $row20['LocationID'];
						$LocationType = $row20['LocationType'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location';
					return json_encode($json);
				}
			}*/

			if($data['group'] != $data['GroupName'])	{ // Location Changed
				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'group') {

					//Start check if valid Group
					$query10 = "select GroupID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);
						$data['GroupID'] = $row10['GroupID'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group

					//Start get free location from group selected
					$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
					$q112 = mysqli_query($this->connectionlink,$query112);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row112 = mysqli_fetch_assoc($q112);
						if($row112['LocationType'] != 'Inbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Only Inbound Location Groups are supported, Scanned Location Group is '.$row112['LocationType'];
							return json_encode($json);
						}
						$data['WarehouseLocationId'] = $row112['LocationID'];
						$newLocationName = $row112['LocationName'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No locations available, in selected group';
						return json_encode($json);
					}
					//End get free location from group selected	



					//$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					// $query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					// $q20 = mysqli_query($this->connectionlink,$query20);
					// if(mysqli_affected_rows($this->connectionlink) > 0) {
					// 	$row20 = mysqli_fetch_assoc($q20);
					// 	if($row20['Locked'] == '1') { 
					// 		$json['Success'] = false;
					// 		$json['Result'] = 'Location is Locked';
					// 		return json_encode($json);
					// 	}

					// 	$data['WarehouseLocationId'] = $row20['LocationID'];
					// 	$LocationType = $row20['LocationType'];
					// } else {
					// 	$json['Success'] = false;
					// 	$json['Result'] = 'Invalid Location Group';
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}
			}

			
			if(true) { //If Update Pallet
				$query = "update pallets set WarehouseLocationId = '".mysqli_real_escape_string($this->connectionlink,$data['WarehouseLocationId'])."',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' ";
				if($old['status'] == '7') { //moving from quarantine to location
					$query = $query . ",status = '1' ";
				}

				$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				$json['newLocationName'] = $newLocationName;
				if($old['WarehouseLocationId'] != $data['WarehouseLocationId']) { // Location Changed

					if($old['WarehouseLocationId'] > 0) {
						$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$old['WarehouseLocationId']."'";
						$querylocold = mysqli_query($this->connectionlink,$sqllocold);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							//return json_encode($json);	
						}
					}					

					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['WarehouseLocationId'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
					if($old['WarehouseLocationId'] > 0) {
						$old['WarehouseLocationId'] = 0;
					}
					if($old['status'] == '7') { //moving from quarantine to location
						$this->RecordLocationHistory('Container',$data['idPallet'],0,$data['WarehouseLocationId'],'Container Moved to Storage Location from Quarantine');
					} else {
						$this->RecordLocationHistory('Container',$data['idPallet'],$old['WarehouseLocationId'],$data['WarehouseLocationId'],'Location Changed for Container in Pending Loads Screen');
					}					
					if($old['status'] == '7') { //moving from quarantine to location
						$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container Moved to Storage Location from Quarantine (Audit Controller : ".$data['AuditController'].")','','".mysqli_real_escape_string($this->connectionlink,$data['WarehouseLocationId'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";
					} else {
						$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container location changed','','".mysqli_real_escape_string($this->connectionlink,$data['WarehouseLocationId'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";
					}					
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = $query2;
						return json_encode($json);
					}
				}

				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container updated','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Container Updated';		

			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ReceiveManualPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if($data['Received'] == 0 && $data['New'] == 1) {//Creating Pallet
				//Start check If Pallet ID alredy exists
				$query = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > '0') {
						$json['Success'] = false;
						$json['Result'] = 'Container ID already exists';
						return json_encode($json);
					}					
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Container';
					return json_encode($json);
				}
				//End check If Pallet ID already exists

				//Start check If Location is valid
				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);
				// 		if($row20['Locked'] == '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is Locked';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['LocationType'] != 'Inbound Storage') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Selected Location is not for Inbound Storage';
				// 			return json_encode($json);
				// 		}

				// 		$json['LocationName'] = $data['location'];
				// 		$data['location'] = $row20['LocationID'];
				// 		$LocationType = $row20['LocationType'];
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Invalid Location';
				// 	return json_encode($json);
				// }
				//End check IF Location is valid

				//Start check If Waste Classification Matches
				$query89 = "select * from facility where FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$q89 = mysqli_query($this->connectionlink,$query89);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row89 = mysqli_fetch_assoc($q89);
					// if($row89['WasteClassification'] == '1') {
					// 	if($data['WasteClassificationType'] != 'WEEE') {
					// 		$json['Success'] = false;
					// 		$json['Result'] = 'Waste Classification is not enabled for current Facility, Waste Classification Type should be WEEE, Move to Quarantine';
					// 		return json_encode($json);
					// 	}
					// }

					if($row89['WasteClassification'] == '0') {
						if($data['WasteClassificationType'] != 'UEEE') {
							$json['Success'] = false;
							$json['Result'] = 'Waste is not enabled for current Facility, Classification Type should be UEEE, Move to Quarantine';
							return json_encode($json);
						}
					}

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid';
					return json_encode($json);
				}

				/*$query90 = "select * from package where FacilityID = '".$_SESSION['user']['FacilityID']."' and idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."'";
				$q90 = mysqli_query($this->connectionlink,$query90);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row90 = mysqli_fetch_assoc($q90);					
					if($data['WasteClassificationType'] != $row90['WasteClassificationType']) {
						$json['Success'] = false;
						$json['Result'] = 'Waste Classification is not matching with Container Type Waste Classification, Move to Quarantine';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid';
					return json_encode($json);
				}*/

				//Ene check If Waste Classification Matches

				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

					//Start check if valid Group
					$query10 = "select * from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);
						if($row10['FacilityID'] != $_SESSION['user']['FacilityID']) {
							$json['Success'] = false;
							$json['Result'] = 'Group Facility is different from User Facility';
							return json_encode($json);
						}

						if($row10['LocationType'] != 'Inbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Location Group is not Inbound Storage';
							return json_encode($json);
						}
						$data['GroupID'] = $row10['GroupID'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group

					//Start get free location from group selected
					$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
					$q112 = mysqli_query($this->connectionlink,$query112);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row112 = mysqli_fetch_assoc($q112);		
						$data['location'] = $row112['LocationID'];						
						$newLocationName = $row112['LocationName'];
						$json['LocationName'] = $row112['LocationName'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No locations available, in selected group';
						return json_encode($json);
					}
					//End get free location from group selected						
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}


				if($data['MaterialType'] == 'Media Rack') {
					$message = '
					{
						"eventType": "RACK_PICKUP",
						"data": {
						"rackAssetId": "'.$data['idPallet'].'",
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"timestamp": "'.time().'"
						}
					}';

					$SNS_Message = $this->SendSNSMessage($message,$data['idPallet'],'RACK_PICKUP','PALLET',NULL,NULL,$data['idPallet'],NULL,NULL);
					if($SNS_Message['Success'] != true) {
						$json['Success'] = false;
						$json['Result'] = 'SNS Message Failed, Holding on Receiving Container';
						return json_encode($json);	
					}
				}  

				//Start get MaterialTypeID from Material Type
				$query1 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."'";
				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$data['MaterialTypeID'] = $row1['MaterialTypeID'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Material Type';
					return json_encode($json);
				}
				//End get MaterialTypeID from Material Type 

				//Start get Waste Code ID
				$query18 = "select distinct(part_type) as part_type from asn_assets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
				$q18 = mysqli_query($this->connectionlink, $query18);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row18 = mysqli_fetch_assoc($q18);	
					$part_type = $row18['part_type'];

					$query19 = "select WasteCode from waste_codes where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$row['PalletFacilityID'])."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$part_type)."' and StatusID = '1' and WasteClassificationType = '".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."'";
					$q19 = mysqli_query($this->connectionlink, $query19);
					if (mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return $json;
					}
					if (mysqli_affected_rows($this->connectionlink) > 0) {	
						$row19 = mysqli_fetch_assoc($q19);						
						$WasteCode = $row19['WasteCode'];						
					} else {
						$WasteCode = 'n/a';
					}

				} else {
					$WasteCode = 'n/a';
				}
				//End get Waste Code ID

				$query = "insert into pallets (idPallet,LoadId,WarehouseLocationId,PalletFacilityID,status,SealNo1,SealNo2,SealNo3,SealNo4,CreatedDate,CreatedBy,Type,Received,ReceivedDate,Verified,POF,MaterialType,idPackage,pallet_netweight,idCustomer,ReceivedBy,WasteCustomerID,WasteClassificationType,MaterialTypeID,idCustomertype,AWSCustomerID,WasteCode) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','1',NOW(),'1','1','".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','".mysqli_real_escape_string($this->connectionlink,$data['pallet_netweight'])."','".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['WasteCustomerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."','".mysqli_real_escape_string($this->connectionlink,$data['MaterialTypeID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idCustomertype'])."','".mysqli_real_escape_string($this->connectionlink,$data['AWSCustomerID'])."','".mysqli_real_escape_string($this->connectionlink,$WasteCode)."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				//Start insert into pallet_items
				$query3 = "insert into pallet_items (palletId,quantity,CreatedDate,CreatedBy,Type) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."',0,NOW(),'".$_SESSION['user']['UserId']."','Manual')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				//End insert into pallet_items
				
				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);	
				}

				$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],'0',$data['location'],'New Container Received');

				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container received at location ','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}

				//Start call API for getting Servers and Switches of Pallet
				if($data['MaterialType'] == 'Media Rack') {
					$create_items = $this->GetRackItems($data['idPallet']);
					$json['OnDemandMedia'] = $create_items['OnDemandMedia'];					
					if($create_items['Success'] == true) {
						//if(true) {
						$query4 = "update pallets set ServersCreatedDate = NOW(),ServersCreatedBy = '".$_SESSION['user']['UserId']."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
						$q4 = mysqli_query($this->connectionlink,$query4);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = $query2;
							return json_encode($json);
						}

						//Start get all created items
						$query16 = "select SerialNumber,Type,HDDCount,SSDCount from speed_expected_servers where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
						$q16 = mysqli_query($this->connectionlink,$query16);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = $query2;
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$serials = array();
							$k=0;
							while($row16 = mysqli_fetch_assoc($q16)) {

								//Start get Media of Servers
								//$query17 = "Select ServerSerialNumber,MediaSerialNumber,MediaType,MediaMPN from speed_expected_media where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$row16['SerialNumber'])."' ";
								$query17 = "Select ServerSerialNumber,MediaSerialNumber,MediaMPN from speed_expected_media where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$row16['SerialNumber'])."' ";

								$q17 = mysqli_query($this->connectionlink,$query17);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = $query2;
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$server_serials = array();
									$l=0;
									while($row17 = mysqli_fetch_assoc($q17)) {
										$server_serials[$l] = $row17;
										$l++;
									}
								} else {
									$server_serials = array();
								}
								$row16['ServerMedia'] = $server_serials;
								//End get Media of Servers


								$serials[$k] = $row16;
								$k++;
							}
							$json['Serials'] = $serials;
						}
						//End get all created items

					} else {
						$json['Serials'] = 'Failure API Response '.$create_items['Error'];
						$json['APIERROR'] = '1';

						//Start API Failure, move Pallet to Quarantine

						$sqlloc = "UPDATE `location` SET `Locked` = '2',`currentItemType` = '',`currentItemID` = '' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
						$queryloc = mysqli_query($this->connectionlink,$sqlloc);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							//return json_encode($json);	
						}

						$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],$data['location'],'0','Container moved to Quarantine');

						$query41 = "update pallets set status = '7',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',WarehouseLocationId = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
						$q41 = mysqli_query($this->connectionlink,$query41);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = $query2;
							return json_encode($json);
						}

						$json['Quarantine'] = '1';
						
						//End API Failure, move Pallet to QUarantine

					}				

				}
				//End call API for getting Servers and Switches of Pallet

				$json['Success'] = true;
				$json['Result'] = 'Container Received';
				$json['palletId'] = $data['idPallet'];			
				return json_encode($json);
			} else { //Updating ASN Created Pallet
				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);
				// 		if($row20['Locked'] == '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is Locked';
				// 			return json_encode($json);
				// 		}
				// 		$json['LocationName'] = $data['location'];
				// 		$data['location'] = $row20['LocationID'];
				// 		$LocationType = $row20['LocationType'];
						
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Invalid Location';
				// 	return json_encode($json);
				// }



				//Start check If Waste Classification Matches
				$query89 = "select * from facility where FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$q89 = mysqli_query($this->connectionlink,$query89);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row89 = mysqli_fetch_assoc($q89);
					// if($row89['WasteClassification'] == '1') {
					// 	if($data['WasteClassificationType'] != 'WEEE') {
					// 		$json['Success'] = false;
					// 		$json['Result'] = 'Waste Classification is not enabled for current Facility, Waste Classification Type should be WEEE, Move to Quarantine';
					// 		return json_encode($json);
					// 	}
					// }

					if($row89['WasteClassification'] == '0') {
						if($data['WasteClassificationType'] != 'UEEE') {
							$json['Success'] = false;
							$json['Result'] = 'Waste is not enabled for current Facility, Classification Type should be UEEE, Move to Quarantine';
							return json_encode($json);
						}
					}

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid';
					return json_encode($json);
				}

				/*$query90 = "select * from package where FacilityID = '".$_SESSION['user']['FacilityID']."' and idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."'";
				$q90 = mysqli_query($this->connectionlink,$query90);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row90 = mysqli_fetch_assoc($q90);					
					if($data['WasteClassificationType'] != $row90['WasteClassificationType']) {
						$json['Success'] = false;
						$json['Result'] = 'Waste Classification is not matching with Container Type Waste Classification, Move to Quarantine';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid';
					return json_encode($json);
				}*/

				//Ene check If Waste Classification Matches


				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

					//Start check if valid Group
					$query10 = "select * from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);
						if($row10['FacilityID'] != $_SESSION['user']['FacilityID']) {
							$json['Success'] = false;
							$json['Result'] = 'Group Facility is different from User Facility';
							return json_encode($json);
						}

						if($row10['LocationType'] != 'Inbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Location Group is not Inbound Storage';
							return json_encode($json);
						}
						$data['GroupID'] = $row10['GroupID'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group

					//Start get free location from group selected
					$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
					$q112 = mysqli_query($this->connectionlink,$query112);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row112 = mysqli_fetch_assoc($q112);		
						$data['location'] = $row112['LocationID'];
						$newLocationName = $row112['LocationName'];
						$json['LocationName'] = $row112['LocationName'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No locations available, in selected group';
						return json_encode($json);
					}
					//End get free location from group selected						
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}


				if(true) { //If Update Pallet


					//Start get MaterialTypeID from Material Type
					$query1 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."'";
					$q1 = mysqli_query($this->connectionlink, $query1);
					if (mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if (mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$data['MaterialTypeID'] = $row1['MaterialTypeID'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Material Type';
						return json_encode($json);
					}
					//End get MaterialTypeID from Material Type

					//Start getting Pallet
					$query22 = "select * from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
					$q22 = mysqli_query($this->connectionlink,$query22);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					if(mysqli_affected_rows($this->connectionlink) > 0)	{
						$pallet = mysqli_fetch_assoc($q22);
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Container ID';
						return json_encode($json);	
					}
					//End getting Pallet

					if($data['MaterialType'] == 'Media Rack') {
						$message = '
						{
							"eventType": "RACK_PICKUP",
							"data": {
							"rackAssetId": "'.$data['idPallet'].'",
							"site": "'.$_SESSION['user']['FacilityName'].'",
							"login": "'.$_SESSION['user']['UserName'].'",
							"timestamp": "'.time().'"
							}
						}';
						
						$SNS_Message = $this->SendSNSMessage($message,$data['idPallet'],'RACK_PICKUP','PALLET',NULL,NULL,$data['idPallet'],NULL,NULL);
						if($SNS_Message['Success'] != true) {
							$json['Success'] = false;
							$json['Result'] = 'SNS Message Failed, Holding on Receiving Container';
							return json_encode($json);	
						}
					}


					//Start get Waste Code ID
					$query18 = "select distinct(part_type) as part_type from asn_assets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."'";
					$q18 = mysqli_query($this->connectionlink, $query18);
					if (mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if (mysqli_affected_rows($this->connectionlink) > 0) {
						$row18 = mysqli_fetch_assoc($q18);	
						$part_type = $row18['part_type'];

						$query19 = "select WasteCode from waste_codes where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$row['PalletFacilityID'])."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$part_type)."' and StatusID = '1' and WasteClassificationType = '".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."'";
						$q19 = mysqli_query($this->connectionlink, $query19);
						if (mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if (mysqli_affected_rows($this->connectionlink) > 0) {
							$row19 = mysqli_fetch_assoc($q19);	
							$WasteCode = $row19['WasteCode'];						
						} else {
							$WasteCode = 'n/a';
						}

					} else {
						$WasteCode = 'n/a';
					}
					//End get Waste Code ID

					$query = "update pallets set Received = '1',ReceivedDate = NOW(),ReceivedBy = '".$_SESSION['user']['UserId']."',Verified = '1',WarehouseLocationId = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."',status = '1',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',POF = '1',MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."',idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$data['pallet_netweight'])."',idCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."',WasteCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['WasteCustomerID'])."',WasteClassificationType = '".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."',idCustomertype = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomertype'])."',AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['AWSCustomerID'])."',MaterialTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialTypeID'])."',WasteCode = '".mysqli_real_escape_string($this->connectionlink,$WasteCode)."' ";
	
	
					$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
	
					$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],'0',$data['location'],'New Container Received');
	
					$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container received at location ','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','location','LocationID','LocationName')";			
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = $query2;
						return json_encode($json);
					}


					//Start call API for getting Servers and Switches of Pallet
					if($data['MaterialType'] == 'Media Rack' && $pallet['ServersCreatedBy'] == NULL) {
						$create_items = $this->GetRackItems($data['idPallet']);
						if($create_items['Success'] == true) {
							$query4 = "update pallets set ServersCreatedDate = NOW(),ServersCreatedBy = '".$_SESSION['user']['UserId']."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
							$q4 = mysqli_query($this->connectionlink,$query4);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = $query2;
								return json_encode($json);
							}

							//Start get all created items
							$query16 = "select SerialNumber,Type from speed_expected_servers where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
							$query16 = "select SerialNumber,Type,HDDCount,SSDCount from speed_expected_servers where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
							$q16 = mysqli_query($this->connectionlink,$query16);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = $query2;
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$serials = array();
								$k=0;
								while($row16 = mysqli_fetch_assoc($q16)) {								
									//Start get Media of Servers
									$query17 = "Select ServerSerialNumber,MediaSerialNumber,MediaType,MediaMPN from speed_expected_media where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$row16['SerialNumber'])."' ";
									$q17 = mysqli_query($this->connectionlink,$query17);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = $query2;
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$server_serials = array();
										$l=0;
										while($row17 = mysqli_fetch_assoc($q17)) {
											$server_serials[$l] = $row17;
											$l++;
										}
									} else {
										$server_serials = array();
									}
									$row16['ServerMedia'] = $server_serials;
									//End get Media of Servers

									$serials[$k] = $row16;
									$k++;
								}
								$json['Serials'] = $serials;
							}
							//End get all created items
							

						} else {
							$json['Serials'] = 'Failure API Response '.$create_items['Error'];
							$json['APIERROR'] = '1';


							//Start API Failure, move Pallet to Quarantine

							$sqlloc = "UPDATE `location` SET `Locked` = '2',`currentItemType` = '',`currentItemID` = '' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
							$queryloc = mysqli_query($this->connectionlink,$sqlloc);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								//return json_encode($json);	
							}

							$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],$data['location'],'0','Container moved to Quarantine');

							$query41 = "update pallets set status = '7',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',WarehouseLocationId = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
							$q41 = mysqli_query($this->connectionlink,$query41);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = $query2;
								return json_encode($json);
							}

							$json['Quarantine'] = '1';
							
							//End API Failure, move Pallet to QUarantine

						}						
					}
					//End call API for getting Servers and Switches of Pallet

					//Start check If all ASN Continers are received, if received, update the load
					$query6 = "select count(*) from pallets where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."' and status = '6'";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row6 = mysqli_fetch_assoc($q6);
						if($row6['count(*)'] == 0) {
							//Before updating the load make sure that was not updated previously
							
							$query8 = "select AllASNContainersReceived from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."' ";
							$q8 = mysqli_query($this->connectionlink,$query8);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								//return json_encode($json);	
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row8 = mysqli_fetch_assoc($q8);
								if($row8['AllASNContainersReceived'] == '1') {
									//Do nothing
								} else {
									//Update the load

									$query7 = "update loads set AllASNContainersReceived = '1',AllASNContainersReceivedDate = NOW(),AllASNContainersReceivedBy = '".$_SESSION['user']['UserId']."' where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."' ";
									$q7 = mysqli_query($this->connectionlink,$query7);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										//return json_encode($json);	
									}								
								}
							}
						}
					}


					//End  check If all ASN Continers are received, if received, update the load 
	
					$json['Success'] = true;
					$json['Result'] = 'Container Received';
					$json['palletId'] = $data['idPallet'];			
				}

				// $json['Success'] = false;
				// $json['Result'] = 'Container alredy Received';					
				// return json_encode($json);
			}	
					
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetExceptionList($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Exceptions')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Exceptions Page';
				return json_encode($json);
			}
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['UserId']
			);
			$query = "select u.*,c.CustomerShotCode,c.CustomerName from uploaded_loads_details u 
			left join customer c on u.idCustomer = c.CustomerID 
			where u.Status = 'Exception' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'LoadId') {
							$query = $query . " AND u.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CustomerName') {
							$query = $query . " AND c.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'idPallet') {
							$query = $query . " AND u.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo1') {
							$query = $query . " AND u.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo2') {
							$query = $query . " AND u.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo3') {
							$query = $query . " AND u.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo4') {
							$query = $query . " AND u.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}						
						if($key == 'SerialNumber') {
							$query = $query . " AND u.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'MPN') {
							$query = $query . " AND u.MPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ExceptionReason') {
							$query = $query . " AND u.ExceptionReason like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ExceptionType') {
							$query = $query . " AND u.ExceptionType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
				if($data['OrderBy'] == 'LoadId') {
					$query = $query . " order by u.LoadId ".$order_by_type." ";
				} else if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by u.idPallet ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo1') {
					$query = $query . " order by u.SealNo1 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo2') {
					$query = $query . " order by u.SealNo2 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo3') {
					$query = $query . " order by u.SealNo3 ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealNo4') {
					$query = $query . " order by u.SealNo4 ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'SerialNumber') {
					$query = $query . " order by u.SerialNumber ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'MPN') {
					$query = $query . " order by u.MPN ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'ExceptionReason') {
					$query = $query . " order by u.ExceptionReason ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'ExceptionType') {
					$query = $query . " order by u.ExceptionType ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'CustomerName') {
					$query = $query . " order by c.CustomerName ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by u.CreatedDate desc ";
			}
			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0)	{
				$i = 0;
				$result = array();
				while($row = mysqli_fetch_assoc($q)) {					
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Exception Records Available";
			}
			$query1 = "select count(*) from uploaded_loads_details u 
			left join customer c on u.idCustomer = c.CustomerID 
			where u.Status = 'Exception' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'LoadId') {
							$query1 = $query1 . " AND u.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CustomerName') {
							$query1 = $query1 . " AND c.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'idPallet') {
							$query1 = $query1 . " AND u.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo1') {
							$query1 = $query1 . " AND u.SealNo1 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo2') {
							$query1 = $query1 . " AND u.SealNo2 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo3') {
							$query1 = $query1 . " AND u.SealNo3 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SealNo4') {
							$query1 = $query1 . " AND u.SealNo4 like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}						
						if($key == 'SerialNumber') {
							$query1 = $query1 . " AND u.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'MPN') {
							$query1 = $query1 . " AND u.MPN like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ExceptionReason') {
							$query1 = $query1 . " AND u.ExceptionReason like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ExceptionType') {
							$query1 = $query1 . " AND u.ExceptionType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$count = $row1['count(*)'];
			}
			$json['total'] = $count;
			return json_encode($json);
		}
		 catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}
	


	public function SearchInboundLoadID ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' =>  $data
		);

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			$query = "select * from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'Ticket Facility is different from User Facility';
					return json_encode($json);
				}

				$input = array();			
				$input['PageURL'] = 'receive/#!/newticket/'.$data['LoadID'];
				$input['TransactionType'] = 'Receive ---> Create New Shipment Ticket';
				$input['Description'] = 'Ticket ID Searched';
				$this->RecordUserNavigationTransaction($input);

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Ticket Details not found';
				return json_encode($json);
			}
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}		
	}

	public function QuarantineManualPallet($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}	
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}

			if($data['Received'] == 0 && $data['New'] == 1) {//Creating Pallet
				//Start check If Pallet ID alredy exists
				$query = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > '0') {
						$json['Success'] = false;
						$json['Result'] = 'Container ID already exists';
						return json_encode($json);
					}					
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Container';
					return json_encode($json);
				}
				//End check If Pallet ID already exists


				if($data['MaterialType'] == 'Media Rack') {
					$message = '
					{
						"eventType": "RACK_PICKUP",
						"data": {
						"rackAssetId": "'.$data['idPallet'].'",
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"timestamp": "'.time().'"
						}
					}';
					
					$SNS_Message = $this->SendSNSMessage($message,$data['idPallet'],'RACK_PICKUP','PALLET',NULL,NULL,$data['idPallet'],NULL,NULL);
					if($SNS_Message['Success'] != true) {
						$json['Success'] = false;
						$json['Result'] = 'SNS Message Failed, Holding on Receiving Container';
						return json_encode($json);	
					}
				}

				$query = "insert into pallets (idPallet,LoadId,PalletFacilityID,status,SealNo1,SealNo2,SealNo3,SealNo4,CreatedDate,CreatedBy,Type,Received,ReceivedDate,ReceivedBy,Verified,POF,MaterialType,idPackage,pallet_netweight,idCustomer,WasteCustomerID,WasteClassificationType) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['LoadId'])."','".mysqli_real_escape_string($this->connectionlink,$data['PalletFacilityID'])."','7','".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['Verified'])."','".mysqli_real_escape_string($this->connectionlink,$data['POF'])."','".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','".mysqli_real_escape_string($this->connectionlink,$data['pallet_netweight'])."','".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteCustomerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				//Start insert into pallet_items
				$query3 = "insert into pallet_items (palletId,quantity,CreatedDate,CreatedBy,Type) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."',0,NOW(),'".$_SESSION['user']['UserId']."','Manual')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);	
				}
				//End insert into pallet_items

				$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],'0','0','New Container Received into Quarantine');

				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container received into Quarantine ','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Container Received into Quarantine';
				$json['palletId'] = $data['idPallet'];			
				return json_encode($json);
			} else { //Updating ASN Created Pallet				
				if(true) { //If Update Pallet

					if($data['MaterialType'] == 'Media Rack') {
						$message = '
						{
							"eventType": "RACK_PICKUP",
							"data": {
							"rackAssetId": "'.$data['idPallet'].'",
							"site": "'.$_SESSION['user']['FacilityName'].'",
							"login": "'.$_SESSION['user']['UserName'].'",
							"timestamp": "'.time().'"
							}
						}';
						
						$SNS_Message = $this->SendSNSMessage($message,$data['idPallet'],'RACK_PICKUP','PALLET',NULL,NULL,$data['idPallet'],NULL,NULL);
						if($SNS_Message['Success'] != true) {
							$json['Success'] = false;
							$json['Result'] = 'SNS Message Failed, Holding on Receiving Container';
							return json_encode($json);	
						}
					}

					$query = "update pallets set Received = '1',ReceivedDate = NOW(),Verified = '".mysqli_real_escape_string($this->connectionlink,$data['Verified'])."',status = '7',SealNo1 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo1'])."',SealNo2 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo2'])."',SealNo3 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo3'])."',SealNo4 = '".mysqli_real_escape_string($this->connectionlink,$data['SealNo4'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',POF = '".mysqli_real_escape_string($this->connectionlink,$data['POF'])."',MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."',idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',pallet_netweight = '".mysqli_real_escape_string($this->connectionlink,$data['pallet_netweight'])."',idCustomer = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomer'])."',WasteCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['WasteCustomerID'])."',WasteClassificationType = '".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."' ";
	
	
					$query = $query ." where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);	
					}
					$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],'0',$data['location'],'Container Received into Quarantine');
	
					$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container received into Quarantine ','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = $query2;
						return json_encode($json);
					}
	
					$json['Success'] = true;
					$json['Result'] = 'Container Received into Quarantine';
					$json['palletId'] = $data['idPallet'];			
				}

				// $json['Success'] = false;
				// $json['Result'] = 'Container alredy Received';					
				// return json_encode($json);
			}	
					
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateAuditController ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Shipments Page';
				return json_encode($json);
			}
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['AuditController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Audit Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Audit Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetFacilityContainerTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$query = "select idPackage,packageName,packageWeight,ContainerClassification from package where FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and Active='1' and ContainerClassification='".mysqli_real_escape_string($this->connectionlink,$data['Type'])."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Container Types Available for Facility";
		}
		return json_encode($json);
	}


	public function CloseContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Shipments Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Shipments Page';
				return json_encode($json);
			}
			

			//Start get Pallet Details
			$query20 = "select * from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$pallet = mysqli_fetch_assoc($q20);
				if($pallet['status'] != 1) {
					$json['Success'] = false;
					$json['Result'] = 'Container Status is not Active';
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);
			}
			//End get Pallet Details

			$query16 = "update pallets set status = 3,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',WarehouseLocationId = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			$q16 = mysqli_query($this->connectionlink,$query16);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			//Start Unlock Pallet Location			
			$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$pallet['WarehouseLocationId']."'";
			$querylocold = mysqli_query($this->connectionlink,$sqllocold);			
			$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],$pallet['WarehouseLocationId'],'0','Container Closed');
			//End Unlock Pallet Location

			// Add tracking record with controller information if available
			$trackingAction = "Container Closed in Pending Shipments Page";
			if (isset($data['AuditController']) && !empty($data['AuditController'])) {
				$trackingAction .= " - Controller: " . $data['AuditController'];
			}

			$query17 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$trackingAction)."','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
			$q17 = mysqli_query($this->connectionlink,$query17);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			
			$json['Success'] = true;
			$json['Result'] = 'Container Closed';
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetMatchingLocationGroups($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		if(strlen($data['keyword']) < 3) {
			$json['Success'] = false;
			$json['Result'] = 'Not enough length';
			return json_encode($json);
		}
		if($data['LocationType'] == '' || $data['LocationType'] == NULL || $data['LocationType'] == null || $data['LocationType'] == 'null') {
			$data['LocationType'] = 'WIP';
		}

		// Handle combined WIP and Outbound Storage location types
		if($data['LocationType'] == 'WIPOROutbound') {
			$locationTypeCondition = "(g.LocationType = 'WIP' OR g.LocationType = 'Outbound Storage')";
		} else {
			$locationTypeCondition = "g.LocationType = '".mysqli_real_escape_string($this->connectionlink,$data['LocationType'])."'";
		}
		//$query = "select l.* from location l where l.FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND l.Locked != 1 AND l.LocationID != 0 and isnull(l.GroupID) and l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%' limit 10 ";
		if($data['FacilityID']) {
			$query = "SELECT
				g.*,
				COUNT(l.GroupID) as total,
				l.GroupID,
				g.LocationType,
				COUNT(available_l.GroupID) as available
			FROM
				location l
			JOIN
				location_group g ON l.GroupID = g.GroupID
			LEFT JOIN
				location available_l ON l.GroupID = available_l.GroupID
				AND available_l.Locked = '2'
				AND available_l.LocationStatus = '1'
			WHERE
				l.FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'
				AND l.LocationStatus = '1'
				AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%'
				AND ".$locationTypeCondition."
			GROUP BY
				l.GroupID
			HAVING
				available > 0
			ORDER BY
				g.GroupName limit 10";

		} else {
			$query = "SELECT
				g.*,
				COUNT(l.GroupID) as total,
				l.GroupID,
				g.LocationType,
				COUNT(available_l.GroupID) as available
			FROM
				location l
			JOIN
				location_group g ON l.GroupID = g.GroupID
			LEFT JOIN
				location available_l ON l.GroupID = available_l.GroupID
				AND available_l.Locked = '2'
				AND available_l.LocationStatus = '1'
			WHERE
				l.FacilityID = '".$_SESSION['user']['FacilityID']."'
				AND l.LocationStatus = '1'
				AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%'
				AND ".$locationTypeCondition."
			GROUP BY
				l.GroupID
			HAVING
				available > 0
			ORDER BY
				g.GroupName limit 10";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}


	public function GetWasteClassifctionOfContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Create New Shipment Ticket Page';
				return json_encode($json);
			}
			
			$query90 = "select * from package where FacilityID = '".$_SESSION['user']['FacilityID']."' and idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."'";
			$q90 = mysqli_query($this->connectionlink,$query90);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row90 = mysqli_fetch_assoc($q90);					
				$json['Success'] = true;
				$json['WasteClassificationType'] = $row90['WasteClassificationType'];
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Classification Type not defined for selected Container Type';
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateDispositionOVerrideController ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {			
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['DispositionOverrideController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Disposition Override Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Disposition Override Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetAllSourceTypes($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Create New Shipment Ticket Page';
			return json_encode($json);
		}

		$query = "select * from customertype where Active = 'Active' order by Cumstomertype";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Source Types Available";
		}
		return json_encode($json);
	}

	public function GetAllAWSCustomers($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Create New Shipment Ticket Page';
			return json_encode($json);
		}

		$query = "select * from aws_customers where Status = 'Active' order by Customer";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Customers Available";
		}
		return json_encode($json);
	}


	public function GetAllAWSMaterialTypes($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Create New Shipment Ticket Page';
			return json_encode($json);
		}

		$query = "select * from material_types where Status = 'Active' order by MaterialType";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Material Types Available";
		}
		return json_encode($json);
	}


	public function GetSourceType($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Create New Shipment Ticket')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Create New Shipment Ticket Page';
			return json_encode($json);
		}

		//Start get MaterialTypeID from Material Type
		$query1 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."'";
		$q1 = mysqli_query($this->connectionlink, $query1);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row1 = mysqli_fetch_assoc($q1);
			$data['MaterialTypeID'] = $row1['MaterialTypeID'];
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Material Type';
			return json_encode($json);
		}
		//End get MaterialTypeID from Material Type

		$query = "select * from source_type_configuration where Status = 'Active' and FacilityID = '".$_SESSION['user']['FacilityID']."' and AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['AWSCustomerID'])."' and MaterialTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialTypeID'])."'";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['idCustomertype'] = $row['idCustomertype'];
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = "Source Type Configuration not defined";
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function ExportContainerListxls($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$_SESSION['ExportContainerListxls'] = $data;
		$json['Success'] = true;
		return json_encode($json);
	}


	public function ManageBulkRecovery($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pending Shipments Page';
			return json_encode($json);
		}

		if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pending Shipments Page';
			return json_encode($json);
		}

		if($data['BatchRecovery'] == 'Yes') {
			$data['BatchRecovery'] = '1';
			$text = 'Yes';
		} else {
			$data['BatchRecovery'] = '0';
			$text = 'No';
		}
		if($_SESSION['user']['ProfileID'] != '1' && $_SESSION['user']['BulkRecoveryController'] == '0') {
			$json['Success'] = false;
			$json['Result'] = 'User is not eligible to change Bulk Recovery';
			return json_encode($json);
		}
		if($data['BatchRecovery'] == '1') {//Check If assets already created, 
			$query1 = "select count(*) from asset where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Serials already created for the Container, Enabling Bulk Recovery is not possible';
					return json_encode($json);
				}
			}			
		}
		$query = "select p.idPallet,p.status,p.BatchRecovery,p.BatchRecoveryCompleted,f.BulkRecoveryEditEligable as FacilityBulkRecovery,m.BulkRecoveryEditEligable as MaterialTypeBulkRecovery,c.BulkRecoveryEditEligable as SourceBulkRecovery from pallets p 
		left join facility f on p.PalletFacilityID = f.FacilityID 
		left join material_types m on p.MaterialTypeID = m.MaterialTypeID 
		left join customer c on p.idCustomer = c.CustomerID 
		where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);

			$error = '';
			if($row['FacilityBulkRecovery'] == '0') {
				$error = $error . 'Current Facility,';
			}

			if($row['MaterialTypeBulkRecovery'] == '0' || $row['MaterialTypeBulkRecovery'] == '') {
				$error = $error . 'Material Type,';
			}

			if($row['SourceBulkRecovery'] == '0' || $row['SourceBulkRecovery'] == '') {
				$error = $error . 'Source,';
			}

			if($error != ''){
				$error = rtrim($error, ",");
				$json['Success'] = false;
				$json['Result'] = $error . " are not eligible for Bulk Recovery";
				return json_encode($json);
			}

			// if($row['BatchRecovery'] == '0') {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'Container is not set Bulk Recovery';
			// 	return json_encode($json);
			// }

			//Start check Bulk Recovery Validations
			$query6 = "";
			//End check Bulk Recovery Validations

			if($row['BatchRecoveryCompleted'] == '1') {
				$json['Success'] = false;
				$json['Result'] = 'Bulk Recovery completed for the Container';
				return json_encode($json);
			}
			$query1 = "update pallets set BatchRecovery = '".$data['BatchRecovery']."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$desc = 'Bulk Recovery is set to '.$text.' for the Container ';
			$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','".mysqli_real_escape_string($this->connectionlink,$data['location'])."',NOW(),'".$_SESSION['user']['UserId']."','','','')";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = $query2;
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Bulk Recovery changed';
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid Container';
			return json_encode($json);
		}
		
	}


	public function RecordUserNavigationTransaction($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$data['PageURL'] = HOST.$data['PageURL'];
		$query = "insert into user_transactions (UserId,TransactionTime,TransactionIPAddress,TransactionType,Description,PageURL) values ('".$_SESSION['user']['UserId']."',NOW(),'".$_SERVER['REMOTE_ADDR']."','".mysqli_real_escape_string($this->connectionlink,$data['TransactionType'])."','".mysqli_real_escape_string($this->connectionlink,$data['Description'])."','".mysqli_real_escape_string($this->connectionlink,$data['PageURL'])."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		$json['Success'] = true;
		$json['Result'] = "Data Saved";
		return json_encode($json);

	}

}
?>